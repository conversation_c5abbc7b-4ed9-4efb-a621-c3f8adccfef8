name: Build ConOps & FDS PDF

on:
  release:
    types:
      - published

jobs:
  generate-conops-fds-pdf:  
    if: startsWith(github.ref, 'refs/tags/conops-fds-v')
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Docker
        uses: docker/setup-buildx-action@v2
      
      - name: Replace tag_name in book.adoc
        run: |
          # Replace the placeholder with the tag_name in book.adoc
          VERSION=$(echo ${{ github.event.release.tag_name }} | sed 's/^conops-fds-//')
          sed -i "s/{{tag_name}}/${VERSION}/g" hinas-control-conops-fds/modules/ROOT/book.adoc

      - name: Generate PDF from AsciiDoc
        run: |
          # Navigate to the correct directory
          cd hinas-control-conops-fds/modules/ROOT

          # Run the Docker command to generate the PDF
          docker run --rm \
            -v $(pwd):/documents \
            asciidoctor/docker-asciidoctor \
            asciidoctor-pdf -a pdf-fontsdir="fonts" -r asciidoctor-mathematical book.adoc

      - name: Upload PDF artifact
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }}
          asset_path: hinas-control-conops-fds/modules/ROOT/book.pdf
          asset_name: ${{ github.event.release.tag_name}}.pdf
          asset_content_type: text/pdf
