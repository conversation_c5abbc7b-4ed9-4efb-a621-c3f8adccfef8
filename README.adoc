= HiNAS Control Documentation Project 

== Release List

=== Operator's Manual

[cols="1,2", options="header"]
|===
|Version (Date) | Description

| link:https://github.com/avikus-ai/nas2-documents/releases/tag/user-manual/v1.2.0[v1.2.0] (July 16, 2025)
a|
* Major documentation improvements with multi-product version support
* Build target conditional compilation for HiNAS Control Standard vs HiNAS Control
* Enhanced Operation Detail documentation based on actual code implementation
* Improved PDF layout quality and eliminated build warnings
* Comprehensive terminology standardization and UI element notation consistency

| link:https://github.com/avikus-ai/nas2-documents/releases/tag/user-manual-v1.0.2-STD[v1.0.2-STD] (June 6, 2025)
a|
* Operator's Manual for HiNAS Control Standard.
* Prepared for DNV Type Approval submission.
* Includes Collision Avoidance (CA) functions.

| link:https://github.com/avikus-ai/nas2-documents/releases/tag/user-manual-v1.0.0-TCS[v1.0.0-TCS] (Apr 17, 2025)
a|
* Initial release of the Operator's Manual for HiNAS Control Standard (TCS only).
* Certified for DNV class-limited operation.
* A variation of `v1.0.0-CA`

| link:https://github.com/avikus-ai/nas2-documents/releases/tag/user-manual-v1.0.0-CA[v1.0.0-CA] (Apr 17, 2025)
a|
* Initial release of the Operator's Manual for HiNAS Control, including Collision Avoidance (CA) functions.
* Applicable to broader system configurations with CA integration.
|===


=== CONOPS-FDS

[cols="1,2", options="header"]
|===
|Version (Date) | Description

| link:https://github.com/avikus-ai/nas2-documents/releases/tag/conops-fds-v0.4.1-ABS[v0.4.1.ABS] (June 11, 2025)
a|
* Operator's Manual for HiNAS Control Standard.
* Prepared for Class ABS submission (ABS only contents added to v0.4.1).
** Risk management and plan
** Network configuration and cyber security
* Includes Collision Avoidance (CA) functions.

| link:https://github.com/avikus-ai/nas2-documents/releases/tag/conops-fds-v0.4.1[v0.4.1] (June 6, 2025)
a|
* Operator's Manual for HiNAS Control Standard.
* Prepared for DNV Type Approval submission.
* Includes Collision Avoidance (CA) functions.

| link:https://github.com/avikus-ai/nas2-documents/releases/tag/conops-fds-v0.4.0[v0.4.0] (May 28, 2025)
a|
* Migration from MS-Word Template v0.3.1
* HiNAS Control Classic -> HiNAS Control (Standard)
|===



== Documentation Build Instructions

=== Operator's Manual

==== Build site
```
$ cd nas2-documents
$ docker run --rm -v $PWD:/antora  -t antora/antora --stacktrace antora-playbook-user-manual.yml
```

====  Build PDF
```
$ cd hinas-control-user-manual/modules/ROOT
$ docker run -it --rm -u $(id -u):$(id -g) -v .:/documents/ asciidoctor/docker-asciidoctor asciidoctor-pdf -a pdf-fontsdir="fonts" -r asciidoctor-mathematical book.adoc
```

=== CONOPS-FDS 

==== Build site
```
$ cd nas2-documents
$ docker run --rm -v $PWD:/antora --rm -t antora/antora --stacktrace antora-playbook-conops-fds.yml
```

====  Build PDF
```
$ cd hinas-control-conops-fds/modules/ROOT
$ docker run -it --rm -u $(id -u):$(id -g) -v .:/documents/ asciidoctor/docker-asciidoctor asciidoctor-pdf -a pdf-fontsdir="fonts" -r asciidoctor-mathematical book.adoc
```