= HiNAS CONTROL Operator Manual ({{tag_name}})
:doctype: book
:sectnums:
:toc: left
// :page-foreground-image: image:watermark.png[]
:icons: font
:front-cover-image: ./images/book-cover.png
:page-background-image: ./images/page-background.png
:pdf-theme: book-theme.yml
// :xrefstyle: full
:xrefstyle: short


== GENERAL NOTICE
include::pages/intro/general-notice.adoc[]

== SYSTEM CONFIGURATION
include::pages/system-configuration/system-configuration.adoc[]


== SYSTEM COMPONENTS
include::pages/system-components/system-config.adoc[]

include::pages/system-components/server-unit.adoc[]

include::pages/system-components/display.adoc[]

include::pages/system-components/control-interface-unit.adoc[]

include::pages/system-components/camera-system.adoc[]


== INTRODUCTION
include::pages/introduction/system-power-on.adoc[]

include::pages/introduction/login.adoc[]

include::pages/introduction/user_category.adoc[]

include::pages/introduction/color-palette.adoc[]

include::pages/introduction/display-mode.adoc[]

include::pages/introduction/brilliance-adjustment.adoc[]

include::pages/introduction/target-ship-info-view.adoc[]


== OPERATIONAL OVERVIEW
include::pages/operational-overview/hinas-control-overview.adoc[]

include::pages/operational-overview/header.adoc[]

include::pages/operational-overview/map-display-area.adoc[]

include::pages/operational-overview/route-information.adoc[]

include::pages/operational-overview/function-act.adoc[]

include::pages/operational-overview/ship-status-and-environment.adoc[]

include::pages/operational-overview/cursor-reading-and-presentation.adoc[]


== SETTINGS
include::pages/settings/settings.adoc[]
include::pages/settings/track-control.adoc[]
include::pages/settings/collision-avoidance.adoc[]
include::pages/settings/target.adoc[]
include::pages/settings/alerts.adoc[]
include::pages/settings/viewing-distance.adoc[]

include::pages/settings/admin-settings.adoc[]
include::pages/settings/ship-specification.adoc[]
include::pages/settings/ship-dynamics.adoc[]
include::pages/settings/advance-track-control.adoc[]
include::pages/settings/advance-alert.adoc[]
include::pages/settings/ccrp.adoc[]


== VOYAGE PLANNING
include::pages/voyage-planning/voyage-planning.adoc[]
include::pages/voyage-planning/operation-import-route.adoc[]
include::pages/voyage-planning/operation-select-engine.adoc[]
include::pages/voyage-planning/operation-input-for-opt.adoc[]
include::pages/voyage-planning/operation-opt-result.adoc[]
include::pages/voyage-planning/operation-applied-route.adoc[]
include::pages/voyage-planning/operation-reoptimization.adoc[]


== ROUTE TRACKING
include::pages/tcs-operation/introduce.adoc[]

include::pages/tcs-operation/control-mode.adoc[]

include::pages/tcs-operation/starting-condition.adoc[]

include::pages/tcs-operation/waypoint-selection.adoc[]

include::pages/tcs-operation/operation.adoc[]

include::pages/tcs-operation/autopilot-activation.adoc[]

include::pages/tcs-operation/bms-activation.adoc[]



== COLLISION AVOIDANCE
include::pages/collision-avoidance/overview.adoc[]
include::pages/collision-avoidance/what-triggers-the-safe-path.adoc[]
include::pages/collision-avoidance/n-and-track-control-mode.adoc[]
include::pages/collision-avoidance/collision-avoidance-assistance-mode.adoc[]
include::pages/collision-avoidance/collision-avoidance-control-mode.adoc[]


== ALERT & OPERATION DETAIL
include::pages/operation/alert.adoc[]
include::pages/operation/operation-detail.adoc[]


== MAINTENANCE
include::pages/maintenance/bridge-console-and-servers.adoc[]

include::pages/maintenance/camera-module.adoc[]

include::pages/maintenance/troubleshooting.adoc[]

include::pages/maintenance/remote-support.adoc[]


== APPENDIX
include::pages/appendix/symbols.adoc[]

include::pages/appendix/terms-and-abbreviations.adoc[]

include::pages/appendix/digital-interfaces.adoc[]

// include::pages/appendix/menu-tree.adoc[]


//== SPECIFICATION OF HINAS
//include::pages/specification/specification.adoc[]