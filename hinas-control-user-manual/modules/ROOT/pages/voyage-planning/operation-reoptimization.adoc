// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Reoptimization Operation

As explained in <<Re-optimization>>, weather forecasts are updated periodically.  
HiNAS enables operators to reflect the latest forecast data even after a mode has been activated by using the re-optimization function.  
This function can also be used to adjust the ETA after HiNAS modes have been initiated.

The [Reoptimization] button is available on the Applied Route page.  
When pressed, the Reoptimization page appears, as shown in <<fig_reoptimization_menu>>.

The start point for re-optimization is indicated as `2(1)` in <<fig_reoptimization_menu>>.A.  
This marks the first waypoint at which changes to RPM CMD or HDG CMD may occur based on the updated data.  
The `To WPT (To Sub-WPT)` for re-optimization is automatically selected by HiNAS.

The end point of re-optimization, shown in <<fig_reoptimization_menu>>.B, defines the last WPT included in the re-optimization range and can be modified by the operator.

ETA (UTC), shown in <<fig_reoptimization_menu>>.C, is set by default to the ETA from the original optimization but may be adjusted as needed.

Loading Condition and Margin settings, shown in <<fig_reoptimization_menu>>.E, can also be modified by the operator.

Once all required parameters are set, pressing the [Calculate] button initiates the re-optimization process.  
If the re-optimization is successful, a summary is provided as shown in <<fig_reoptimization_results>>.A.  
If it fails, a failure message along with the cause is displayed, as shown in <<fig_reoptimization_results>>.B.

[[fig_reoptimization_menu]]
.Reoptimization menu
image::voyage-planning/reoptimization_menu_v0.png[width=600,align="center"]

[[fig_reoptimization_results]]
.Reoptimization result
image::voyage-planning/reoptimization_results_v0.png[width=700,align="center"]

If the re-optimization result is applied, the affected WPTs are marked as `RE-OPT` on the Applied Route page.  
WPTs that were not included in re-optimization remain marked as `OPT`, as shown in <<fig_applied_route_after_reopt>>.

[[fig_applied_route_after_reopt]]
.Applied Route page after re-optimization
image::voyage-planning/applied_route_after_reopt_v0.png[width=400,align="center"]


[NOTE]
====
* The [Reoptimization] button is disabled and re-optimization is not available:
** during CAC mode  
** when no route is applied  
** when following the last WPT  
====