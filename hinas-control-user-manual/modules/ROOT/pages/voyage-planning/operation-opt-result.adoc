// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Optimization Results

Once the optimization is completed, a summary of the results is displayed, as shown in <<fig_optimization_result>>.A.  
This includes FOC, average speed, and average RPM.  
If the calculation fails, a notification indicating the cause of failure is shown, as illustrated in <<fig_optimization_result>>.B, to help the operator correct the input parameters.

If more detailed information is needed, the operator can expand the view by clicking the toggle, as shown in <<fig_optimization_result_detail>>.A.  
To export the optimization result, press the [Export] button, as shown in <<fig_optimization_result_detail>>.B.  
The exported route file can be saved only to a connected USB memory device.

[[fig_optimization_result]]
.Optimization result
image::voyage-planning/optimization_calculation_result_v0.png[width=600,align="center"]

[[fig_optimization_result_detail]]
.Detailed optimization result
image::voyage-planning/optimization_calculation_result_detail_v0.png[width=600,align="center"]

If [Route Opt] is selected using the Oceanwise engine, the optimization result includes optimized waypoint positions and corresponding RPM commands, as shown in <<fig_route_optimization_result>>.  
Note that the waypoints may differ in both position and quantity from the original route.

[[fig_route_optimization_result]]
.Route optimization result
image::voyage-planning/route_optimization_calculation_result_v0.png[width=600,align="center"]

Pressing the [Apply] button applies the optimization result to the HiNAS system, setting it as the monitoring route and enabling it for route tracking.

[NOTE]
====
* If the user's speed plan is applied, the average RPM and average speed reflect the input values used in the calculation.  
* FOC is not affected by the user's speed plan.  
* If [Route Opt] is selected using the Oceanwise engine, the optimized WPTs are shared with the ECDIS.
====