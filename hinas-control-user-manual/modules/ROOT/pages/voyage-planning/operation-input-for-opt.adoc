// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Inputs for Optimization

Once a route is calcualted using the From ECDIS or From File menu, the operator can configure the input parameters used for optimization, as shown in <<fig_inputs_for_opt>>.
After setting the parameters, the operator can runs optimization by clicking the [Calculate] button.  
Detailed explanations for each parameter are provided in the tables below.

[[fig_inputs_for_opt]]
.Input UI for optimization
image::voyage-planning/rpm_optimization_input_conditions_v0.png[width=600,align="center"]

[cols="^1,2,3,2", options="header"]
|===
| Label
| Item 
| Description
| Example 

| A 
| Name 
| Name assigned to the route
| 62065_sen4

| B
a|
* Start WPT
* End WPT
a| 
Start/End waypoints within the route to be used for optimization
a|
* WPT 1
* WPT 20

| C
a|
* Departure Time
* Arrival Time
a|
* Estimated departure time from Start WPT
* Estimated arrival time at End WPT
a|
* 2025-01-16T08:30:00Z
* 2025-01-17T14:00:00Z

| D
a| 
* Fwd. Draft
* Aft. Draft
a|
* Draft measured at the forward end of the vessel  
* Draft measured at the after end of the vessel
a|
* 8.5 [m]
* 9.0 [m] 

| E
| Margin 
| adjusts the SOG relative to RPM. A positive margin value decreases SOG at the same RPM, while a negative value increases it.
| 5 [%]

| G
| Optimization Range
| Visually highlighted section on the route between Start and End WPTs
| -
|===

Route type, user-defined speed plan, and XTL (labeled as F in <<fig_inputs_for_opt>>) are defined below:

[cols="1,3,1", options="header"]
|===
| Name
| Description
| Example

| Route Type
| RL for Rhumb Line, GC for Great Circle
| RL

| Apply User's SPD Plan
| whether to apply the user-defined speed plan
| 6 [kn]

| Turn Radius
| Radius of the vessel’s turning circle during maneuvers
| 0.5 [NM]

| XTL (PORT/STBD)
| XTL – allowable deviation from planned route
| 0.5 [NM]
|===