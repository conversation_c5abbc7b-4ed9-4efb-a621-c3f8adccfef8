// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Importing Route

HiNAS provides two methods for importing a route planned by the operator:

* From ECDIS: imports the route currently monitored by the ECDIS.
* From Files: imports a route stored in an .RTZ file.

==== Importing Route from ECDIS

When the connection between the ECDIS and HiNAS is functioning properly, the route under monitoring mode on the ECDIS is shared with HiNAS.  
This shared route can be imported by selecting Voyage Planning > From ECDIS.  
WPTs of the imported route are displayed in the map area, as shown in <<fig_from_ecdis_menu>>.

[[fig_from_ecdis_menu]]
.Importing route using From ECDIS menu
image::voyage-planning/from_ecdis_import_route_v0.png[width=600,align="center"]

==== Importing Route from File

HiNAS supports route import using files in the RTZ format.  
The RTZ file should be stored on a USB memory stick.  
To import, navigate to Voyage Planning > Choose File, as shown in <<fig_from_file_choose_file>>.  
WPTs from the selected file are then displayed in the map area, as shown in <<fig_from_file_import_route>>.

[[fig_from_file_choose_file]]
.Navigation of route file
image::voyage-planning/from_file_choose_file_v0.png[width=600,align="center"]

[[fig_from_file_import_route]]
.Imported route using From File menu
image::voyage-planning/from_file_import_route_v0.png[width=600,align="center"]