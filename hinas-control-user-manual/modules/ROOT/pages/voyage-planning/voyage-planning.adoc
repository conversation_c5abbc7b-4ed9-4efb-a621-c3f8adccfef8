// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Voyage Planning Functions

The primary function of voyage planning is to optimize the voyage based on a given passage plan.  
HiNAS suggests the optimal RPM required to reach the destination according to the estimated schedule or to pass each WPT efficiently.  
RPM values are selected based on weather forecasts to minimize fuel consumption while ensuring timely arrival.


=== Voyage Optimization

HiNAS offers two types of voyage optimization:

* RPM Optimization: determines the optimal RPM for each WPT, enabling the vessel to minimize FOC while still meeting the ETA.
* Route Optimization: calculates both the optimal route and corresponding RPMs based on a passage plan prepared by the operator.

The optimization server stores high-resolution weather forecast data (less than 0.25°), which includes information on currents, wind, waves, water temperature, and salinity.  
Forecasts are updated four times daily, every six hours.  
Depending on the data type, the forecast coverage ranges from six to sixteen days.


=== Re-optimization

As weather forecasts are updated periodically, HiNAS allows operators to reflect the latest weather data even after mode activation through the re-optimization function.  
Re-optimization can also be used to adjust the ETA after HiNAS modes have been activated.


=== Limitations

* Machine learning models are employed to recommend optimal RPMs that minimize fuel consumption while meeting ETA.These models require sufficient training data, which may not be available initially. To address this, a margin parameter is used. If the margin is positive, the same RPM yields a lower SOG; if negative, it yields a higher SOG. This parameter is configured during sea trials in consultation with navigators and can be adjusted by navigators during voyage planning. As voyage data accumulates, the model’s accuracy improves.

ifdef::build_target_std[]
* In TC mode, the RPM of the current leg cannot be changed. Thus, any optimization based on updated weather data applies from the next waypoint onward.
endif::build_target_std[]

ifdef::build_target_nstd[]
* In Route Tracking mode, the RPM of the current leg cannot be changed. Thus, any optimization based on updated weather data applies from the next waypoint onward.
endif::build_target_nstd[]

[NOTE]
====
* Route optimization is an experimental feature and not included in the official release.
====



// ==== Procedure for Voyage Planning
// The following preparations are required to use optimization.

// ===== Preparation
// * Control Setting
// ** RPM Range[RPM] as described in <<Track Control Settings>>
// * Route Data imported via following actions described in <<How to Import a Route>>
// ** From File
// ** From ECDIS

// ===== Procedure
// . Receive a pre-planned route from ECDIS or import an exported file from ECDIS.
// . Set the following parameters: ETD, ETA, draft, margin, route type, turn radius, XTD, etc., and perform the RPM Optimization.
// . After reviewing the calculation result, confirm that there are no issues and apply the route.  
// . Perform reoptimization once a day to utilize updated weather data.  
// . (Optional) Export route data in .csv or .rtz format.  

// ==== How to Import a Route

// *Menu > Voyage Planning*

// HiNAS offers two methods for importing a route planned by the operator, as shown in <<fig-voyage-planning-menu>>:

// * ② From ECDIS: Imports the currently monitored route from an ECDIS.
// * ③ From Files: Imports a route file in .RTZ format.

// [[fig-voyage-planning-menu]]
// .Voyage planning menu
// image::voyage-planning/applied_route_v1.png[,600,400,align="center"]

// <<<

// ===== From ECDIS
// The following steps describe how to import a route using the *From ECDIS* function.

// * ①: Click the *From ECDIS* tab  
// * ②: Check the imported route information from an ECDIS if available

// [[fig-from-ecdis-menu]]
// .From ECDIS menu
// image::voyage-planning/from_ecdis_1_v1.png[,600,400,align="center"]

// <<<

// ===== From File
// The following steps describe how to import a route in .RTZ format using the *From File* function.

// * ①: Click the *From File* tab
// * ②: Click the *Choose File* button and select a RTZ file.
// * ③: Click the *Import* button and check the imported route information

// [[fig-from-file-menu-step-1]]
// .From File menu
// image::voyage-planning/from_file_1_v1.png[,600,400,align="center"]

// <<<

// ==== Setting Route Parameters for RPM Optimization
// Once a route is imported using *From ECDIS* or *From File* function, the operator can configure parameters such as ETD, ETA, draft, margin, route type, turn radius, XTD, and others for use in RPM optimization, as shown in <<fig-rpm-optimization-parameter-setting>>.
// A detailed explanation of these parameters is provided below.
// After setting the parameters, the operator can initiate RPM optimization by clicking the *Calculation* button.


// ===== ① Route Name
// [%header,cols="2,5,2,2"]
// |===
// |Name                |Description                              |Example        |Required
// |Name                |A clear and distinguishable name for the route.        |sample_name    |Yes
// |===


// ===== ② Start/End WPT and Departure/Arrival Time
// [%header,cols="2,5,2,2"]
// |===
// |Name                |Description                                |Example                |Required
// |Start WPT           |The starting point for optimization within the entire route. |WPT 1  |Yes
// |End WPT             |The ending point for optimization within the entire route. |WPT 2  |Yes
// |Departure Time      |The time when the vessel is expected to depart from its origin. |2025-01-16T08:30:00Z  |Yes
// |Arrival Time        |The time when the vessel is expected to arrive at its destination. |2025-01-17T14:00:00Z  |Yes
// |===

// ===== ③ Drafts and a Margin
// [%header,cols="2,5,2,2"]
// |===
// |Name                |Description                                |Example                |Required
// |Forward Draft       |The draft measured at the forward end of the vessel.            |8.5 [m]                 |Yes
// |After Draft         |The draft measured at the aft end of the vessel.                |9.0 [m]                 |Yes
// |Margin              |Adjusts the SOG relative to RPM. A positive margin value decreases SOG at the same RPM, whereas a negative value increases SOG.|5 [%]                 |Yes
// |===

// ===== ④ Route Type, User's Speed Plan, Cross Track Distance Limit
// [%header,cols="2,5,2,2"]
// |===
// |Name                |Description                                |Example                |Required
// |No.                  |A sequential number used for indexing or identification.       |1                     |Yes
// |LAT                 |The latitude of a specific location in decimal degrees.        |37.7749               |Yes
// |LON                 |The longitude of a specific location in decimal degrees.       |-122.4194             |Yes
// |Route Type          |The type of navigation (e.g., RL for Rhumb Line, GC for Great Circle). |RL(Rhumb Line), GC(Great Circle)|Yes
// |Apply User's SPD Plan|Whether to apply the user's custom speed plan for the route.   |10 [kn]                  |No
// |Turn Radius         |The radius of the vessel's turning circle during a maneuver.   |500 [m]                 |Yes
// |XTL - PORT/STBD     |The cross-track distance allowed for deviation from the planned route. |50 [m]              |Yes
// |===

// <<<

// [[fig-rpm-optimization-parameter-setting]]
// .Parameter setting for RPM optimization
// image::voyage-planning/planning_route_1_v1.png[,600,400,align="center"]

// <<<

// ==== How to Apply Route

// Once, calculation is complete, then 

// * ①: Review an RPM Optimization results  
// * ②: Click *Apply* button.  

// [[fig-rpm-optimization-review]]
// .Review of RPM optimization result
// image::voyage-planning/apply_route_1_v1.png[,600,400,align="center"]

// When reviewing and applying the reviewd route, refer the following tables in the below.

// <<<

// ===== ① Summary of RPM Optimization
// [%header,cols="1,3"]
// |===
// |Name                |Description
// |No.                  |A sequential number used for indexing or identification.
// |Departure           |The departure location in decimal degrees (latitude).
// |ETA                 |The estimated time of arrival at the destination.
// |Fuel Consumption    |The amount of fuel expected to be consumed for the route.
// |Avg. SPD            |The average speed maintained during the route.
// |Avg. RPM            |The average revolutions per minute maintained during the route.
// |Calculation(Success/Fail) |Indicates whether the calculation was successful or failed.
// |Export              |Allows the export of route data to a file or external system.
// |===

// ===== ② Details of RPM Optimization
// [%header,cols="1,3"]
// |===
// |Name                |Description
// |No.                  |A sequential number used for indexing or identification.
// |LAT                 |The latitude of a specific location in decimal degrees.
// |LON                 |The longitude of a specific location in decimal degrees.
// |Route Type          |Specifies the navigation type (e.g., RL for Rhumb Line, GC for Great Circle).
// |CRS                 |The course to be followed during navigation.
// |DIST                |The distance to be covered during the current route segment.
// |SPD                 |The speed at which the vessel is traveling.
// |RPM                 |The revolutions per minute of the vessel's engine.
// |ETA                 |The estimated time of arrival at the next waypoint or destination.
// |XTD - PORT/STBD     |The cross-track distance allowed for deviation from the planned route, port, or starboard.
// |===

// <<<

// ==== How to Export Route Data

// The operator can export the RPM optimization results in either *.RTZ* or *.CSV* format.

// * ①: Click *Export* button
// * ②: Select Format

// [[fig-export-route-data]]
// .Export of RPM Optimization results
// image::voyage-planning/export_route_1_v1.png[,600,400,align="center"]

// NOTE: The exported route file can only be saved to a connected USB memory device.

// <<<

// ==== Applied Route and Sub-WPTs

// The Operator can view the applied route by navigating to *Menu > Voyage Planning > Applied Route* as shown in <<fig-applied-route-subwpt-hidden>>.

// Additionally, clicking the Sub WPTs button located between *WPT X* displays information about the sub-waypoints as shown in <<fig-applied-route-subwpt-exposed>>.

// [[fig-applied-route-subwpt-hidden]]
// .Applied route (sub-waypoints are hidden)
// image::voyage-planning/sub_wpt_1_v1.png[,400,300,align="center"]


// [[fig-applied-route-subwpt-exposed]]
// .Applied route (sub-waypoints are shown)
// image::voyage-planning/sub_wpt_2_v1.png[,400,300,align="center"]



// [NOTE]
// ====
// *Sub-Waypoint*

// * Most navigation equipment such as ECDIS typically displays routes based on main Waypoints (WPTs). However, HiNAS Control introduces the concept of Sub-WPTs (sub-waypoints) to enable more precise RPM optimization.

// * As explained in the Weather Data section, HiNAS Control utilizes high-resolution weather data (below 0.25°) to determine the optimal RPM. In practice, the distance between main waypoints can be too large to fully leverage this high-resolution data. When waypoints are far apart, it becomes difficult to fine-tune RPM in response to changing weather conditions throughout each leg.

// * To address this issue, HiNAS Control automatically generates Sub-WPTs to divide the route into smaller segments. Optimizing RPM at each Sub-WPT allows for more accurate speed adjustments and better fuel efficiency.

// * The criteria for determining Sub-WPT intervals vary depending on the vessel’s specifications. Research and testing are currently underway to further refine this process.
// ====


// ==== How to Reoptimize Route
// **Menu > Voyage Planning > Applied Route**

// . Navigate to the *Applied Route* tab  
// . Click the *Reoptimize* button  
// . Enter parameters  
// .. Refer to the details below for specific parameters  
// . Click the *Calculate* button  


// [[fig-reoptimization-menu]]
// .Update of RPM plan via Reoptimization menu
// image::voyage-planning/reoptimize-route-1.png[,500,400,align="center"]

// [NOTE]
// ====
// If the button is disabled, check:

// * No route is applied  
// * TCS is following the last WPT  
// ====

// <<<
    
// ==== Route Cancel

// **Menu > Voyage Planning > Applied Route**

// The Operator can cancel the applied route by following steps:

// . Navigate to the *Applied Route* tab
// . Click the Route Cancel button  
// . Click the OK button  

// [[fig-route-cancel-1]]
// .Route cancel
// image::voyage-planning/route-cancer-1.png[,500,400,align="center"]

// [[fig-route-cancel-2]]
// .Route canceled
// image::voyage-planning/route-cancer-2.png[,500,400,align="center"]