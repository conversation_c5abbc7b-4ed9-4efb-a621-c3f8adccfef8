// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

==== Ship Specification Settings

Ship Specification page displays ship-related information such as the IMO number, hull number, and interfaced instruments.
It also shows the ship's dimensions, the configured RPM command range permitted by the BMS, and the selected manufacturers of the interfaced navigation and communication equipment.

// HiNAS Control Standard //
ifdef::build_target_std[]

[[fig-ships-specification]]
.Ship specification settings.
image::settings/su_setting_ship_spec_v1.png[ship information, width=900, align=center]

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

[[fig-ships-specification]]
.Ship specification settings.
image::settings/su_setting_ship_spec_v1.png[ship information, width=900, align=center]

endif::build_target_nstd[]


The following table describes each parameter.

[[table_su_setting_ship_spec]]
.Ship specification setting parameters 
[cols="^2,5", options="header"]
|===
| Parameter  
| Description  

| IMO Number
| IMO number of ownship 

| Hull Number
| Hull number of ownship 

| LBP [m]
| LBP of ownship 

| B [m]
| Breadth length of ownship

| Available RPM Range [rpm]
| RPM command range allowed by the BMS

| Autopilot Maker
| Autopilot Maker

| ECDIS Maker
| ECDIS Maker

| BMS Maker
| BMS Maker

| AMS Maker
| AMS Maker
|===

<<<