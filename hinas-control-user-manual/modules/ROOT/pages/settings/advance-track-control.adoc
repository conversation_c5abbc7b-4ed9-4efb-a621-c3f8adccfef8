// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images


// HiNAS Control Standard //
ifdef::build_target_std[]

==== Advanced Track Control Settings

Advanced Track Control page provides settings configuration for TC.

[[fig-advance-track-control-settings]]
.Advance track control settings.
image::settings/su_setting_adv_tcs_v1.png[width=900, align=center]

The following table outlines each parameter's function, recommended values, and allowable ranges available.


[[table_su_setting_adv_tcs]]
.Advanced Track Control parameters
[cols="3,4,^2,^2", options="header"]
|===
| Parameter
| Description
| Recommended value
| Setting range

| Look Ahead Coefficient (Ballast/Half/Full)
| Lower values result in quicker track adherence.
| 20/30/40
| -

| Reach Length Coefficient (Ballast/Half/Full)
| Distance forward before initiating a curved track.
| 1.5/1.8/2.3 [LBP]
| -

| ROT Gain
| Proportional gain (P gain) for XTE during turning. ROT error is calculated as (Heading differ + XTE) times ROT gain.
| 5
| 5 - 20

| ROT Counter Gain
| Derivative gain (D gain) minimizing overshoot from XTE.
| 5
| 5 - 20

| K XTE Angle
| Propertional gain that compensates for XTE during turning.
| 5
| 5 - 20

| Turning ROT Reducer Filter
| Manages ROT deceleration during turns (0: Off; higher values increase deceleration).
| 4
| 1 - 6

| Turning Weather Compensation
| Gain compensating for turning influenced by weather, calculated from STW and SOG differences.
| 7
| 0 - 15

| Nominal ROT [deg/min]
| ROT maintained during straight paths and within corridors.
| 20
| -

| Spike Filter Threshold [NM]
| Threshold for filtering GPS spikes; spikes below this value are ignored.
| 0.05
| -

| Spike Filter Count [s]
| Time frame to monitor GPS spikes.
| 10
| -
|===

endif::build_target_std[]




// HiNAS Control //
ifdef::build_target_nstd[]

==== Advanced Route Tracking Settings

Advanced Route Tracking page provides settings configuration for Route Tracking.

[[fig-advance-track-control-settings]]
.Advanced Route Tracking settings.
image::settings/su_setting_adv_tcs_v1.png[width=900, align=center]

The following table outlines each parameter's function, recommended values, and allowable ranges available.


[[table_su_setting_adv_tcs]]
.Advanced Route Tracking parameters
[cols="3,4,^2,^2", options="header"]
|===
| Parameter
| Description
| Recommended value
| Setting range

| Look Ahead Coefficient (Ballast/Half/Full)
| Lower values result in quicker track adherence.
| 20/30/40
| -

| Reach Length Coefficient (Ballast/Half/Full)
| Distance forward before initiating a curved track.
| 1.5/1.8/2.3 [LBP]
| -

| ROT Gain
| Proportional gain (P gain) for XTE during turning. ROT error is calculated as (Heading differ + XTE) times ROT gain.
| 5
| 5 - 20

| ROT Counter Gain
| Derivative gain (D gain) minimizing overshoot from XTE.
| 5
| 5 - 20

| K XTE Angle
| Propertional gain that compensates for XTE during turning.
| 5
| 5 - 20

| Turning ROT Reducer Filter
| Manages ROT deceleration during turns (0: Off; higher values increase deceleration).
| 4
| 1 - 6

| Turning Weather Compensation
| Gain compensating for turning influenced by weather, calculated from STW and SOG differences.
| 7
| 0 - 15

| Nominal ROT [deg/min]
| ROT maintained during straight paths and within corridors.
| 20
| -

| Spike Filter Threshold [NM]
| Threshold for filtering GPS spikes; spikes below this value are ignored.
| 0.05
| -

| Spike Filter Count [s]
| Time frame to monitor GPS spikes.
| 10
| -
|===

endif::build_target_nstd[]