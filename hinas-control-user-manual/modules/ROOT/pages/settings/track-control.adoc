// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

// HiNAS Control Standard
ifdef::build_target_std[]
==== Track Control Settings
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
==== Route Tracking Settings
endif::build_target_nstd[]

// HiNAS Control Standard //
ifdef::build_target_std[]

Track Control setting page, as shown in the figure below, provides access to the following parameter settings:

* Starting conditions  
* Limits  
* Operational range  
* Control mode selection  
* Loading condition selection

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

Route Tracking setting page, as shown in the figure below, provides access to the following parameter settings:

* Starting conditions  
* Limits  
* Operational range  
* Control mode selection  
* Loading condition selection

endif::build_target_nstd[]


<<<

// HiNAS Control Standard //
ifdef::build_target_std[]

[[fig-track-control-settings]]
.Track control settings.
image::settings/track_control_setting_page_v1.png[width=900, align=center]

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

[[fig-track-control-settings]]
.Route tracking settings.
image::settings/track_control_setting_page_v1.png[width=900, align=center]

endif::build_target_nstd[]




// HiNAS Control Standard //
ifdef::build_target_std[]

The following table describes the function, recommended value, and setting range for each TC setting parameter.

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

The following table describes the function, recommended value, and setting range for each Route Tracking setting parameter.

endif::build_target_nstd[]



// HiNAS Control Standard //
ifdef::build_target_std[]
[[table_track_control_setting_menu]]
.Track control setting parameters
[%unbreakable, cols="^2,5,^2,^2", options="header"]
|===
| Parameter  
| Description  
| Recommended value  
| Setting range  

| Preset Enter Distance [NM]  
| TC can only be initiated when the distance from the starting waypoint is less than the specified value.  
| 1  
| 0 – 1  

| Preset Enter Angle [deg]  
| TC can only be initiated when the angle between own ship and the track is less than the specified value.  
| 15  
| 1 – 60  

| Preset Time to WOL [s]  
| TC can only be initiated when the time to the Waypoint of Leg (WOL) is less than the specified value.  
| 30  
| 0 – 60  

| Off Heading Limit [deg]  
| Maximum heading control angle for straight-track navigation provided to the autopilot (only applicable to and operational with Yokogawa autopilot).  
| 60  
| –  

| Max Rudder [deg]  
| Maximum rudder angle provided to the autopilot (only applicable to and operational with Yokogawa autopilot).  
| 15  
| –  

| ROT Max [deg/min]  
| Maximum rate of turn (ROT) value that can be commanded in Turning Mode.  
| –  
| –  

| Control Mode  
| Control mode selection: HS Mode (Heading and Speed Control), H Mode (Heading Control only), S Mode (Speed control only).  
| HS Mode  
| –  

| RPM Range [RPM]  
| RPM range permitted for HiNAS Control, as defined by the BMS maker.  
| –  
| –  

| Loading Condition  
| Ownship loading condition (Ballast / Half / Full). This parameter should be updated regularly by the navigator.  
| –  
| –  
|===
endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]
[[table_track_control_setting_menu]]
.Route tracking setting parameters
[%unbreakable, cols="^2,5,^2,^2", options="header"]
|===
| Parameter  
| Description  
| Recommended value  
| Setting Range  

| Preset Enter Distance [NM]  
| Route Tracking can only be initiated when the distance from the starting waypoint is less than the specified value.
| 1  
| 0 – 1  

| Preset Enter Angle [deg]  
| Route Tracking can only be initiated when the angle between own ship and the track is less than the specified value.
| 15  
| 1 – 60  

| Preset Time to WOL [s]  
| Route Tracking can only be initiated when the time to the Waypoint of Leg (WOL) is less than the specified value.
| 30  
| 0 – 60  

| Off Heading Limit [deg]  
| Maximum heading control angle for straight-track navigation provided to the autopilot (only applicable to and operational with Yokogawa autopilot).  
| 60  
| –  

| Max Rudder [deg]  
| Maximum rudder angle provided to the autopilot (only applicable to and operational with Yokogawa autopilot).  
| 15  
| –  

| ROT Max [deg/min]  
| Maximum rate of turn (ROT) value that can be commanded in Turning Mode.  
| –  
| –  

| Control Mode  
| Control mode selection: HS Mode (Heading and Speed Control), H Mode (Heading Control only).  
| HS Mode  
| –  

| RPM Range [RPM]  
| RPM range permitted for HiNAS Control, as defined by the BMS maker.  
| –  
| –  

| Loading Condition  
| Ownship loading condition (Ballast / Half / Full). This parameter should be updated regularly by the navigator.  
| –  
| –  
|===
endif::build_target_nstd[]

NOTE: If shaft generator is equipped, adjust *RPM range* to PTO range.

<<<