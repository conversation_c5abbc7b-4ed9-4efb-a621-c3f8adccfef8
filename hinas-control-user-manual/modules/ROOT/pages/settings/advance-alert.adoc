// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

==== Advanced Alert Settings

// HiNAS Control Standard //
ifdef::build_target_std[]

Advanced Alert page allows the operator to enable or disable various TC-related alerts, as shown in the figure below.
Disabling these alerts helps prevent overlapping alarms from other equipment.

[[fig_su_setting_adv_alert]]
.Advanced alert setting page
image::settings/su_setting_adv_alert_v1.png[width=900, align=center]

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

Advanced Alert page allows the operator to enable or disable various Route Tracking-related alerts, as shown in the figure below.
Disabling these alerts helps prevent overlapping alarms from other equipment.

[[fig_su_setting_adv_alert]]
.Advanced alert setting page
image::settings/su_setting_adv_alert_v1.png[width=900, align=center]

endif::build_target_nstd[]