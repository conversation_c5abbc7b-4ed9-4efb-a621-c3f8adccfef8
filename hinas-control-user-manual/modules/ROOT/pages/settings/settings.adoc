// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Settings

Main Menu Setting page, as shown in <<fig_setting_menu>>, provides access to configuration pages for the following categories:

// HiNAS Control Stardard //
ifdef::build_target_std[]
* Track Control  
* Collision Avoidance  
* Target  
* Alert  
* Display (Viewing distance)
endif::build_target_std[]

// HiNAS Control //
ifdef::build_target_nstd[]
* Route tracking  
* Collision Avoidance  
* Target  
* Alert  
* Display (Viewing distance)
endif::build_target_nstd[]


[[fig_setting_menu]]
.Setting menu.
image::settings/setting_menu_v0.png[Setting, width=900, align=center]

<<<