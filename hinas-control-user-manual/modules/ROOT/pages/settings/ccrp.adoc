// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images
==== CCRP Settings

CCRP Settings page, as shown in the figure below, provides access to configuration options for the Conning Command Reference Point (CCRP).

// HiNAS Control Standard //
ifdef::build_target_std[]

[[fig_ccrp_settings]]
.CCRP settings.
image::settings/setting_ccrp_page_v1.png[ccrp settings, width=900, align=center]

endif::build_target_std[]



// HiNAS Control //
ifdef::build_target_std[]

[[fig_ccrp_settings]]
.CCRP settings.
image::settings/setting_ccrp_page_v1.png[ccrp settings, width=900, align=center]

endif::build_target_std[]


The following table describes the function and allowable range for each parameter in the CCRP Settings menu.

[[table_ccrp_setting_menu]]
.CCRP Settings Parameters  
[cols="2,5,^2,^2", options="header"]
|===
| Parameter  
| Description  
| Recommended Value  
| Setting Range  

| Length [m]  
| Length of the own ship  
| –  
| –  

| Beam [m]  
| Width (beam) of the own ship  
| –  
| –  

| Sensor Position [m]  
| Actual position of the installed sensor on the own ship  
| –  
| –  

| CCRP Position [m]  
| Position of the Conning Command Reference Point  
| –  
| –  
|===

[NOTE]
====
* HiNAS Control provides the sensor positions of GPS1 and GPS2.  
* HiNAS Control also provides the radar antenna positions of X-band and S-band radars when `TTM` data is utilized.
====
<<<