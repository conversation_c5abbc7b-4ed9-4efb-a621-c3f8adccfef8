// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

==== Target Settings

Target Setting page, as shown in the figure below, provides access to configuration options for the following parameters:

* Target monitoring  
* Lost target warning  

// HiNAS Control Standard //
ifdef::build_target_std[]

[[fig_target_setting_page]]
.Target setting menu  
image::settings/setting_target_page_v1.png[width=900, align=center]

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

[[fig_target_setting_page]]
.Target setting menu  
image::settings/setting_target_page_v1.png[width=900, align=center]

endif::build_target_nstd[]


The following table describes the function, recommended value, and setting range for each parameter in the Target Setting menu.

[[table_target_setting_menu]]
.Target setting menu  
[cols="^2,5,^2,^2", options="header"]
|===
| Parameter  
| Description  
| Recommended Value  
| Setting Range  

| Target Monitoring [NM]  
| enables or disables target monitoring within the specified range.  
| 20 (On)  
| 0 – 20  

| Lost Target Warning Function  
| enables or disables the Lost Target Warning.  
| On  
| –  
|===

<<<