// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

==== Alert Settings

// HiNAS Control Standard //
ifdef::build_target_std[]

Alert Settings page, as shown in the figure below, provides access to configuration options for the following parameters:

* TC and CA-related alert settings
* Alert sound configuration and test functions

[[fig_alert_setting_page]]
.Alert Settings Menu  
image::settings/setting_alert_page_v1.png[width=900, align=center]

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

Alert Settings page, as shown in the figure below, provides access to configuration options for the following parameters:

* Route Tracking and CA-related alert settings
* Alert sound configuration and test functions

[[fig_alert_setting_page]]
.Alert Settings Menu  
image::settings/setting_alert_page_v1.png[width=900, align=center]

endif::build_target_nstd[]


The following table describes the function, recommended value, and allowable range for each parameter in the Alert Settings menu.

<<<

[[table_alert_setting_menu]]
.Alert Settings Parameters  
[cols="2,5,^2,^2", options="header"]
|===
| Parameter  
| Description  
| Recommended Value  
| Setting Range  

| Position Monitoring Criteria [NM]  
| Threshold for detecting positional deviation  
| 0.1 
| 0 – 1

| Heading Monitoring Criteria [deg]  
| Threshold for heading deviation between HDG sensors  
|  
| 0 – 25

| Maximum Course Difference Limit [deg]  
| Threshold for deviation between track bearing (BRG) and course over ground (COG)  
|  
| 0 – 60

| Low Speed Alert Criteria [kn]  
| Threshold for low-speed condition  
| 5  
| 0 – 30

| Collision Risk Alert  
| enables the collision risk alert generated by HiNAS  
| –  
| –

| Alert Sound Player  
a|
* selects alert sound playback method  
** Buzzer: Plays alert sound using the buzzer built into the monitor  
** Speaker: Plays alert sound via the speaker connected to the server  
| –  
| –

| Alert Sound Test  
| provides test buttons for verifying alert sound playback using the buzzer  
|  
|  
|===

<<<