// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

==== Collision Avoidance Settings

Collision Avoidance setting page, as shown in the figure below, provides access to configuration options for the following parameters:

* Determination of dangerous targets
* Maneuverability of ownship

// HiNAS Control Standard //
ifdef::build_target_std[]

[[fig-collision-avoidance-settings]]
.Collision avoidance settings.
image::settings/collision_avoidance_setting_page_v1.png[width=900, align=center]

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

[[fig-collision-avoidance-settings]]
.Collision avoidance settings.
image::settings/collision_avoidance_setting_page_v1.png[width=900, align=center]

endif::build_target_nstd[]

<<<

The following table describes the function, recommended value, and setting range for each collision avoidance setting parameter.

[[table_collision_avoidance_setting_menu]]
.Control avoidance setting parameters  
[cols="^2,5,^2,^2", options="header"]
|===
| Parameter  
| Description  
| Recommended Value  
| Setting Range  

| CPA [NM] (Safety radius) 
| This parameter is used for determining dangerous targets. A target with lower CPA than this set value is considered as dangerous target candidate.
| 0.5
| 0 – 1  


| TCPA-GW [min]
| This parameter is used for determining dangerous targets. If the TCPA of a target, classified as a give-way, falls below the specified value, it is considered as a dangerous target candidate.
| 40
| 30 - 60  

| TCPA-SO [min]
| This parameter is used for determining dangerous targets. If the TCPA of a target, classified as a stand-on, falls below the specified value, it is considered as a dangerous target candidate.
| 30
| 30 - `TCPA-GW`  

| ROT in evasion[deg/min]
| An expected average ROT in an evasive maneuver. This refers to the maneuverability of an ownship. A lower value leads to a slower heading change during following a safe path.
| 5 - 20
| 0 - `ROT Max`

| ROT in return[deg/min]
| An expected average ROT in returning maneuver. This refers to the maneuverability of an ownship. A lower value leads to a slower heading change when returning to the original route during following the safe path.
| 5 - 20
| 0 - `ROT Max`
|===

<<<