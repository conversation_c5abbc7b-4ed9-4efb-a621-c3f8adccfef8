// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

==== Ship Dynamics Settings

Ship Dynamics page, as shown below, provides setting limits for course changes.

// HiNAS Control Standard //
ifdef::build_target_std[]

[[fig-ship-dynamics]]
.Ship dynamics settings
image::settings/su_setting_ship_dyn_v1.png[width=900, align=center]

endif::build_target_std[]


// HiNAS Control //
ifdef::build_target_nstd[]

[[fig-ship-dynamics]]
.Ship dynamics settings
image::settings/su_setting_ship_dyn_v1.png[width=900, align=center]

endif::build_target_nstd[]


The following table provides detailed descriptions of each parameter:

[[table_su_setting_ship_dyn]]
.Ship dynamics setting parameters
[cols="^2,5", options="header"]
|===
| Parameter
| Description

| Max Course Change [deg]
| The maximum allowable course change based on the vessel's dynamics.
This limit is provided by ship manufacturer and varies according to vessel type.
TC cannot be initiated if the route includes a course change exceeding this limit.

| Min Turn Radius [NM]
| The minimum allowable turning radius based on the vessel's dynamics.
This value is provided by ship manufacturer and depends on the vessel type.
Track Control cannot be initiated if the route includes a turning radius smaller than this minimum.
|===