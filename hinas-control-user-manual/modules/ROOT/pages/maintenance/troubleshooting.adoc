=== Troubleshooting

Effective troubleshooting is essential for maintaining the optimal performance of HiNAS. This chapter provides a comprehensive guide to identifying and resolving common issues that may arise during operation. For issues beyond the scope of this guide or requiring specialized tools and expertise, it is recommended to contact Avikus technical support for professional assistance. By following the troubleshooting procedures outlined here, you can minimize downtime and ensure the reliable operation of the system.


==== General Problems
The following table provides troubleshooting guide for general problems.

[cols="1,3,4,6,4",frame="all",grid="all"]
|===
|No.| Problem | Possible Cause | Troubleshooting Steps | Remarks

|1| HiNAS Control screen is grayed-out and displays "Connection Lost." 
a|
* Connection between the HiNAS Control client PC and HiNAS Control server PC is lost.
* Internal switch hub is malfunctioning.
a|
* Reboot the HiNAS Control client PC. 
* Reboot the HiNAS Control server PC.
| Contact Avikus for technical support. A technician visit may be required.

|2| Connection lost with **ALL** external devices (e.g., autopilot, ECDIS, GPS). 
a|
* Switch hub in the HiNAS Control server PC rack is malfunctioning.
* Firewall in the HiNAS Control server PC rack is malfunctioning.
* Internal connections in the HiNAS Control server PC rack are damaged.
a|
* Check for a "Lost Network" alert. 
|Contact Avikus for technical support. A technician visit may be required.

|3| Connection lost with a specific external device (e.g., autopilot, ECDIS, GPS). 
a|
* The corresponding device is not functioning.
* Cabling to the corresponding device is damaged.
a|
* Verify that the corresponding device is functioning properly and restore its functionality.
* Ensure that the connection to the corresponding device is secure, and restore the connection.
| Check for a "Lost Sensor Data" or "Lost AUX Data" alert.
Contact Avikus for technical support if the issue persists. A technician visit may be required.

|4| Keyboard (or trackball) is not working.
a|
* Connection between the keyboard and HiNAS Control client PC is not secure.
* Keyboard is damaged.
a|
* Open the bridge console and check the connection between the keyboard and the HiNAS Control client PC.
* Reconnect the USB connector.
| If the keyboard is damaged, contact Avikus for a replacement.

|5| Lost the account credentials for the MASTER (admin) account.
|- 
| The initial admin account is set as "captain"/"[HullNo]!!". i.e. Hhi0000!!, Hmd1111!!.
| Contact Avikus for remote support if account recovery is not possible.

|6| Lost the account credentials for non-admin users.
|- 
a|
* Log in with the MASTER (admin) account.
* Go to "Apps". In HiNAS Control, click the profile icon on the top-right corner then click "Apps" on the dropdown.
* Click "Authentication Setting"
* Manage the users (add, edit, unlock) from the "Manage Accounts" tab.
| Contact Avikus for support if user account recovery is unsuccessful.

|7| 3 seconds beep sound from the monitor
a|
* HiNAS Control alerting function is lost.
* Connection between the monitor and HiNAS Control server/client PC is lost.
* Connection between the HiNAS Control client PC and HiNAS Control server PC is lost.
a|
* Check the connection between HiNAS Control server/client PC and monitor.
* Reboot the HiNAS Control client PC.
* Reboot the HiNAS Control server PC.
| Contact Avikus for technical support. A technician visit may be required.

|===



<<<

==== Voyage Planning Problems

The following table provides the troubleshooting guide for Voyage Planning function
[cols="1,3,4,6,4",frame="all",grid="all"]
|===
|No.| Problem | Possible Cause | Troubleshooting Steps | Remarks 

|1| Cannot receive a route from the ECDIS using the "From ECDIS" function.
a|
* Loading the route from the ECDIS may take some time.
* A data transmission error may have occurred.
* Connection to the ECDIS may be lost.
a|
* Wait 3–5 minutes after applying the route in monitoring mode in the ECDIS, then try again.
* Remove the route from monitoring mode in the ECDIS, then reset it as monitoring mode and try again. 
| If the issue persists, contact Avikus for support. 

|2| Cannot load a route file extracted from the ECDIS using the "From File" function.
a|
* The file may not be in the "RTZ" or "RTZP" format.
* The file may be damaged.
a|
* HiNAS supports only the standard route exchange formats "RTZ" and "RTZP." Ensure the file is in one of these formats.
* Files in other formats, such as CSV, are not supported. |-


|3| "Calculate" function keeps failing (applies to both "From File" and "From ECDIS" functions).
a|
* Inappropriate schedule input.
* Internet connection lost.
a|
* Click the calculation result item marked as "Fail."
* If the error code is 104 (indicating the need to adjust departure and arrival times), press the back button and enter new departure and arrival times. The error message will guide you on the appropriate time values.
* If the error indicates a network connection issue, check the internet connection on the vessel. Follow vessel-specific procedures for restoring connectivity.
|-
|===

<<<

// HiNAS Control Standard
ifdef::build_target_std[]
==== Track Control Problems
The following table provides troubleshooting guide for TC functions.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
==== Route Tracking Problems
The following table provides troubleshooting guide for Route Tracking functions.
endif::build_target_nstd[]

// HiNAS Control Standard
ifdef::build_target_std[]
[cols="1,3,4,6,4",frame="all",grid="all"]
|===
|No.| Problem | Possible Cause | Troubleshooting Steps | Remarks

|1| Cannot activate Track Control (TC).
| Starting condition is not met.
| Check the starting condition from the "Operation Detail" view. |-

|2| "Low Perf." is displayed.
| Performance degradation is expected.
| Check the starting condition from the "Operation Detail" view. |-

|3| TC is deactivated after a very short period of time.
| Autopilot is not in external heading control mode.
| After activating TC, switch the autopilot mode to external heading control mode (Ext. HC mode). |-

|4| Cannot set RPM while in TC mode.
a|
* HiNAS Control TC is in Heading Control mode (H-mode).
* The RPM value exceeds the accepted range.
a|
* Navigate to track control settings by clicking "Menu" -> "Setting" -> "Track Control." Switch the "Control Mode" to Heading and Speed Control mode (HS mode).
* Verify the RPM range under "Menu" -> "Setting" -> "Track Control."
|-
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
[cols="1,3,4,6,4",frame="all",grid="all"]
|===
|No.| Problem | Possible Cause | Troubleshooting Steps | Remarks

|1| Cannot activate Route Tracking.
| Starting condition is not met.
| Check the starting condition from the "Operation Detail" view. |-

|2| "Low Perf." is displayed.
| Performance degradation is expected.
| Check the starting condition from the "Operation Detail" view. |-

|3| Route Tracking is deactivated after a very short period of time.
| Autopilot is not in external heading control mode.
| After activating Route Tracking, switch the autopilot mode to external heading control mode (Ext. HC mode). |-

|4| Cannot set RPM while in Route Tracking mode.
a|
* HiNAS Control Route Tracking is in Heading Control mode (H-mode).
* The RPM value exceeds the accepted range.
a|
* Navigate to route tracking settings by clicking "Menu" -> "Setting" -> "Route Tracking." Switch the "Control Mode" to Heading and Speed Control mode (HS mode).
* Verify the RPM range under "Menu" -> "Setting" -> "Route Tracking."
|-
|===
endif::build_target_nstd[]

// with Collision Avoidance
// ==== Track Control and Collision Avoidance Problems

// .Troubleshooting Guide for TC and CA Functions
// [cols="1,3,4,6,4",frame="all",grid="all"]
// |===
// |#| Problem | Possible Cause | Troubleshooting Steps | Remarks 

// |1| Cannot activate Track Control (TC). 
// | Starting condition is not met. 
// | Check the starting condition from the "Operation Detail" view. |-

// |2| Cannot activate Collision Avoidance (CA). 
// | Starting condition is not met. 
// | Check the starting condition from the "Operation Detail" view. |-

// |3| "Low Perf." is displayed. 
// | Performance degradation is expected. 
// | Check the starting condition from the "Operation Detail" view. |-

// |4| TC is deactivated after a very short period of time. 
// | Autopilot is not in external heading control mode. 
// | After activating TC, switch the autopilot mode to external heading control mode (Ext. HC mode). |-

// |5| Cannot set RPM while in TC mode. 
// |* HiNAS Control TC is in Heading Control mode (H-mode). +
//  * The RPM value exceeds the accepted range.
// |* Navigate to track control settings by clicking "Menu" -> "Setting" -> "Track Control." Switch the "Control Mode" to Heading and Speed Control mode (HS mode). +
//  * Verify the RPM range under "Menu" -> "Setting" -> "Track Control."
// |-
// |===


<<<

==== Camera Unit Problems

The following table provides the troubleshooting guide for HiNAS camera unit.

[cols="1,3,4,6,4",frame="all",grid="all"]
|===
|No.| Problem | Possible Cause | Troubleshooting Steps | Remarks 

|1| Camera disconnection alert | The camera unit is disconnected from HiNAS. 
a|
* Reboot the HiDOM Server.
* Reboot the camera unit via the junction box.
* Check the cabling. | Contact Avikus for technical support. A technician visit may be required.

|2| Camera view is blacked-out on the monitor | Error in the image processing software module. 
a|
* Reboot the HiDOM Server.
* Reboot the camera unit via the junction box. | Contact Avikus for technical support. A technician visit may be required.

|3| Camera view is partially blacked-out on the monitor | Camera unit is damaged. |- | Contact Avikus for technical support. A technician visit may be required.

|4| Camera view has color distortion (e.g., green or magenta dots) on the monitor. 
a|
* Camera is damaged.
* Camera is overheating.
* Junction box is overheating. 
a|
* Check the sun visor on the camera unit. | Contact Avikus for technical support. A technician visit may be required.

|5| Camera view has flickering on the monitor | Camera is overheating. | * Check the sun visor on the camera unit. | Contact Avikus for technical support. A technician visit may be required.

|6| Camera view is blurred on the monitor | Camera lens is dirty. | Clean the camera lens with a soft cloth. |-

|7| Camera view has unexpected spots or marks. 
a|
* Camera lens is dirty.
* An object is obstructing the camera lens.
* Camera lens has cracks or scratches. 
a|
* Clean the camera lens with a soft cloth.
* Remove the object obstructing the camera lens. |- 

|8| Camera view appears misaligned or separated. 
a|
* Unexpected movement of the camera unit.
* Improper camera alignment. 
| Perform camera alignment. | Contact Avikus for technical support. 

|9| Target tags are not displayed in the camera view. 
a|
* AIS is turned off.
* Error in the HiNAS software module. 
a|
* Turn on or restart AIS.
* Reboot the HiDOM Server. |-

|10| Target tags are incorrectly positioned in the camera view. 
| Improper camera alignment. | Perform camera alignment. | Contact Avikus for technical support.
|===