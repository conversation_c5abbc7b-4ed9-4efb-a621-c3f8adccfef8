// HTML, PDF conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Ship Status And Environment

// For HiNAS Control //
ifdef::build_target_nstd[]
HiNAS provides sensor data and its status.  
Operator can select a sensor for a dual-sensor configuration to be used with Route Tracking and CAA/CAC.
endif::build_target_nstd[]

// For HiNAS Control Standard//
ifdef::build_target_std[]
HiNAS provides sensor data and its status.  
Operator can select a sensor for a dual-sensor configuration to be used with TC and CAA/CAC.
endif::build_target_std[]

[[fig_ship_status_and_env]]
.Ship status and environment
image::operational-overview/ship_status_and_environment_v1.png[width=400, align="center"]

[NOTE]
====
`T` in the Environment area represents "TRUE".
====

[cols="^1,2,4", options="header"]
|===
| Label
| Area
| Description                  

| A
| Ship Status
| displays *HDG*, *COG*, *STW*, *SOG*, *Rudder*, *RPM*, *ROT*, *UKC*, and *POSN*, and provides selection options for *GPS* and *GYRO*.

| B
| Environment
| displays environmental values, including *WIND* and *CUR*. 
|===

<<<

The color of indicators and sensor names of Ship Status and Environment changes based on the status of the sensor data.
The table below summarizes the basic information.

[cols="^2,^2,^2,^2,^2", options="header"]
|===
| Color of data type
| Color of data source
| Color of sensor data
| Format
| State                  

| Green | Green | Green | 888.88 | Normal
| Green | Green | Green | `**`   | No signal
| Red   | Red   | Red   | `**`   | Invalid
|===

<<<