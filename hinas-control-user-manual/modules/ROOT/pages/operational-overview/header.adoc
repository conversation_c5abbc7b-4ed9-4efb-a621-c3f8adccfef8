// HTML, PDF conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Header

Header bar is located at the top of the display.  
<PERSON>er provides the *Main Tab*, which includes *Voyage Planning*, *Operation Details*, *Settings*, *Alert Box*, *Brilliance Control*, *Display Mode Control*, *Time*, and *User Profile*.

// STD //
ifdef::build_target_std[]
[[fig-operational-overview-header]]
.Header.
image::operational-overview/header-v0.png[width=800, align="center"]
endif::build_target_std[]

// NSTD //
ifdef::build_target_nstd[]
[[fig-operational-overview-header]]
.Header.
image::operational-overview/header-v0.png[width=800, align="center"]
endif::build_target_nstd[]


[cols="1,2,4", options="header"]
|===
| No.
| Button or Item 
| Description                  

| 1 
| Main Tab
| allows selection of *Voyage Planning*, *Operation Details*, and *Settings*.

| 2
| Alert Box
| displays the *Dedicated Area*, provides access to the *Alert List*, and allows control of *Acknowledgment (Ack)*.

| 3
| Brilliance Control
| manages brilliance settings.

| 4
| Display Mode Control
| allows selection of display modes.

| 5
| Time
| displays UTC time.

| 6
| User Profile
| displays the user profile.  
|===

<<<