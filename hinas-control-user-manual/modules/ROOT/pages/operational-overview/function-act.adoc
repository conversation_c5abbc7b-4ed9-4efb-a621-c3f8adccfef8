// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Function Activation


// For HiNAS Control Standard//
ifdef::build_target_std[]

HiNAS provides toggle switches for activating TC.
Additionally, related information is displayed near the activation switch.

[[fig_function_act_ui]]
.Function activation UI
image::operational-overview/function_act_v3.png[width=800,align="center"]

[cols="^1,2,4", options="header"]
|===
| Label
| Item
| Description                  

| A
| TC activation
| enables TC activation

| B
| TCS status
| displays detailed information related to TC, including the mode and the states of the autopilot and BMS

| C
| CA Assistance
| enables the activation of CAA

| D
| CA Control
| displays detailed information about the CAC-related state

| E
| Safe Path Info
| displays SG and provides a decision (CA Control / Not Follow)

| F
| Safe Path Info
| displays the applied route for TC, SG, and CA path

| G
| Operation detail
| a button that navigates to the Operation Detail page
|===

endif::build_target_std[]



// For HiNAS Control //
ifdef::build_target_nstd[]

HiNAS provides toggle switches for activating Route Tracking.
Additionally, related information is displayed near the activation switch.

[[fig_function_act_ui]]
.Function activation UI
image::operational-overview/function_act_v3.png[width=800,align="center"]

[cols="^1,2,4", options="header"]
|===
| Label
| Item
| Description                  

| A
| Route Tracking activation
| enables Route Tracking activation

| B
| Route Tracking status
| displays detailed information related to Route Tracking, including the mode and the states of the autopilot and BMS

| C
| CA Assistance
| enables the activation of CAA

| D
| CA Control
| displays detailed information about the CAC-related state

| E
| Safe Path Info
| displays SG and provides a decision (CA Control / Not Follow)

| F
| Safe Path Info
| displays the applied route for Route Tracking, SG, and CA path

| G
| Operation detail
| a button that navigates to the Operation Detail page
|===

endif::build_target_nstd[]

<<<