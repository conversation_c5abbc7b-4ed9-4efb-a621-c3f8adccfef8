// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images
// Build option test//
// :build_target_std:
// :build_target_nstd:

=== HiNAS Overview

HiNAS screen is divided into several area as shown below.

ifdef::build_target_std[]
[[fig_operational_overview_std]]
.HiNAS screen.
image::operational-overview/operational_overview_v5.png[width=800,align="center"]
endif::build_target_std[]

ifdef::build_target_nstd[]
[[fig_operational_overview_std]]
.HiNAS screen.
image::operational-overview/operational_overview_v5.png[width=800,align="center"]
endif::build_target_nstd[]


<<<

ifdef::build_target_std[]
[cols="2,3", options="header"]
|===
|Area
|Description                  

|Header
|provides *Settings*, *Alert Box*, *Brilliance Adjustment*, selection of *Display Modes*, and *Time*.

|Map Display Area
|shows the *route*, *ownship*, *target* symbols, and more.

|Route Info Box
|displays *route information* whenever a WPT is selected.

|Activation Box
|indicates the status of TC-related instruments and allows activation of TC and CA.

|Sensor Info Box
|displays sensor information with its status and allows sensor selection.

|Map Functions
|enables selection of *map scale*, *following mode*, and *target filtering* options.


|Camera Display Area
|shows *EO/IR camera views*, *HDG/COG*, *Next Turn*, *Target-Ship AR Symbol*, *Planned/Actual Info.*, and more.

|HDG/COG
|displays the *HDG/COG* of ownship.

|Target-Ship AR Symbol
|shows calculated or relayed information of target ships.

|Planned/Actual Info.
|displays essential sensor information and plans simultaneously.


|Cursor Reading
|displays *cursor readings* from ownship.

|Presentation
|indicates *presentation modes* and related settings.
|===
endif::build_target_std[]


ifdef::build_target_nstd[]
[cols="2,3", options="header"]
|===
|Area
|Description                  

|Header
|provides *Settings*, *Alert Box*, *Brilliance Adjustment*, selection of *Display Modes*, and *Time*.

|Map Display Area
|shows the *route*, *ownship*, *target* symbols, and more.

|Route Info Box
|displays *route information* whenever a WPT is selected.

|Activation Box
|indicates the status of Route Tracking-related instruments and allows activation of Route Tracking and CA.

|Sensor Info Box
|displays sensor information with its status and allows sensor selection.

|Map Functions
|enables selection of *map scale*, *following mode*, and *target filtering* options.


|Camera Display Area
|shows *EO/IR camera views*, *HDG/COG*, *Next Turn*, *Target-Ship AR Symbol*, *Planned/Actual Info.*, and more.

|HDG/COG
|displays the *HDG/COG* of ownship.

|Target-Ship AR Symbol
|shows calculated or relayed information of target ships.

|Planned/Actual Info.
|displays essential sensor information and plans simultaneously.


|Cursor Reading
|displays *cursor readings* from ownship.

|Presentation
|indicates *presentation modes* and related settings.
|===
endif::build_target_nstd[]

<<<