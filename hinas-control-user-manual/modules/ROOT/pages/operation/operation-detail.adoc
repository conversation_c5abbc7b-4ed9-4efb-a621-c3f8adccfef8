// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Operation Detail

==== General

// HiNAS Control Standard
ifdef::build_target_std[]
Operation Detail displays the status of the conditions required for activating TC and CAA/CAC of HiNAS using the following four states:
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
Operation Detail displays the status of the conditions required for activating Route Tracking and CAA/CAC of HiNAS using the following four states:
endif::build_target_nstd[]

* *Not Ready*: The functionality is *OFF*, and the corresponding condition is preventing the functionality from being activated.
* *Ready*: The functionality is *OFF*, but the corresponding condition is prepared for activation.
* *Low Performance*: The functionality is *ON*, but the corresponding condition may cause a performance degradation.
* *Active*: The functionality is *ON*, the corresponding condition is operating normally, and there is no impact on the functionality's performance.


The status of the functionality is determined by the status of the sub-items that can be checked in the Operation Detail.

* When the functionality is *OFF*:
** If all sub-items are in the *Ready* state, the functionality is *Ready*.
** If one or more sub-items are in the *Not Ready* state, the functionality is *Not Ready*.
* When the functionality is *ON*:
** If all sub-items are in the *Active* state, the functionality is *Active*.
** If one or more sub-items are in the *Low Performance* state, the functionality is *Low Performance*.


// HiNAS Control Standard
ifdef::build_target_std[]
[[fig_operation_detail_tc_active]]
.Operation Detail when TC functionality being ON and in an Active state.
image::operation/operation-detail-tc-active.png[width=600,align="center"]

[[fig_operation_detail_tc_not_ready]]
.Operation Detail when TC functionality being OFF and in a Not Ready state.
image::operation/operation-detail-tc-not-ready.png[width=600,align="center"]
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
[[fig_operation_detail_route_tracking_active]]
.Operation Detail when Route Tracking functionality being ON and in an Active state.
image::operation/operation-detail-tc-active.png[width=600,align="center"]

[[fig_operation_detail_route_tracking_not_ready]]
.Operation Detail when Route Tracking functionality being OFF and in a Not Ready state.
image::operation/operation-detail-tc-not-ready.png[width=600,align="center"]
endif::build_target_nstd[]

In the Operation Detail window, users can view the conditions required for each functionality by navigating to the corresponding tab. Below is an explanation of each column:

* *State*: One of the four states of the condition.
** *Ready* and *Active* states are indicated in green.
** *Not Ready* state is indicated in yellow.
** *Low Performance* state is indicated in orange.
* *Title*: The name of the condition.
* *Description*: Detailed explanation of the condition.
** If a condition is in an abnormal state (*Not Ready* or *Low Performance*), the description helps identify the issue.


<<<

==== Starting Condition
// HiNAS Control Standard
ifdef::build_target_std[]
All starting conditions should be satisfied before TC or CAA/CAC can be activated.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
All starting conditions should be satisfied before Route Tracking or CAA/CAC can be activated.
endif::build_target_nstd[]

These conditions are as follows:

// HiNAS Control Standard
ifdef::build_target_std[]
===== Starting Conditions for TC

[%unbreakable, cols="6,10,5", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | All HiNAS software modules must function properly for TC. |
| Position Sensor | Position data is mandatory for TC. |
| Heading Sensor | Heading data is mandatory for TC. |
| Speed Sensor | SOG and STW data are required to activate TC. |
| Autopilot Mode | Autopilot must be in Heading Control mode (Tokyo-keiki: HC mode/ Yokogawa: AUTO mode) to activate TC. |
| Autopilot Connection | Autopilot connection is required for TC. |
| Autopilot Signal Validity | Valid autopilot signal is required for TC. |
| BMS Ready | In HS-Mode, BMS must be in ready status to activate TC. | In HiNAS HS-Mode only
| BMS Signal Validity | In HiNAS HS-Mode, a valid BMS signal is required for TC. | In HiNAS HS-Mode only
| Own Ship RPM | In HiNAS HS-Mode, the difference between HiNAS command RPM and actual RPM must be less than 10 to activate TC. | In HiNAS HS-Mode only
| RPM Range | In HiNAS HS-Mode, HiNAS command RPM must be within the 'RPM Range' for TC to work properly. | In HiNAS HS-Mode only
| Back-up Navigator Alarm | All back-up navigator alarms must be acknowledged to activate TC. |
| Voyage Planning | Voyage Planning must be performed to activate TC. |
| XTD Limit | XTE must be within the user-defined XTD limit to activate TC |
| Preset Enter Distance | Distance to start waypoint must be less than the 'Preset Enter Distance' to activate TC, ensuring the own ship is close enough to the waypoint. |
| Preset Enter Angle | The angle between the own ship's heading and the track course must be less than the 'Preset Enter Angle' to activate TC. |
| Min Turn Radius | Turning radius (RAD) of applied route must exceed the 'Min Turn Radius' to activate TC. |
| Max Course Change | Course difference between all consecutive waypoints must be less than the 'Max Course Change' to activate TC. |
| ROT Max | Rate of Turn (ROT) of applied route must be less than the 'ROT Max' to activate TC. |
| Preset Time to WOL | TWOL (Time to Wheel Over Line) must be greater than the 'Preset Time to WOL' to activate TC. |
| End of Track | TC cannot be activated at the end of the track. |
| Position Monitoring | Secondary position sensor is mandatory for TC. |
| Heading Monitoring | Secondary heading sensor is mandatory for TC. |
| RPM Optimization | Speed control is available only when the planned RPM is optimized. |
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
===== Starting Conditions for Route Tracking

[%unbreakable, cols="6,10,5", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | All HiNAS software modules must function properly for Route Tracking. |
| Position Sensor | Position data is mandatory for Route Tracking. |
| Heading Sensor | Heading data is mandatory for Route Tracking. |
| Speed Sensor | SOG and STW data are required to activate Route Tracking. |
| Autopilot Mode | Autopilot must be in Heading Control mode (Tokyo-keiki: HC mode/ Yokogawa: AUTO mode) to activate Route Tracking. |
| Autopilot Connection | Autopilot connection is required for Route Tracking. |
| Autopilot Signal Validity | Valid autopilot signal is required for Route Tracking. |
| BMS Ready | In HS-Mode, BMS must be in ready status to activate Route Tracking. | In HiNAS HS-Mode only
| BMS Signal Validity | In HiNAS HS-Mode, a valid BMS signal is required for Route Tracking. | In HiNAS HS-Mode only
| Own Ship RPM | In HiNAS HS-Mode, the difference between HiNAS command RPM and actual RPM must be less than 10 to activate Route Tracking. | In HiNAS HS-Mode only
| RPM Range | In HiNAS HS-Mode, HiNAS command RPM must be within the 'RPM Range' for Route Tracking to work properly. | In HiNAS HS-Mode only
| Back-up Navigator Alarm | All back-up navigator alarms must be acknowledged to activate Route Tracking. |
| Voyage Planning | Voyage Planning must be performed to activate Route Tracking. |
| XTD Limit | XTE must be within the user-defined XTD limit to activate Route Tracking |
| Preset Enter Distance | Distance to start waypoint must be less than the 'Preset Enter Distance' to activate Route Tracking, ensuring the own ship is close enough to the waypoint. |
| Preset Enter Angle | The angle between the own ship's heading and the track course must be less than the 'Preset Enter Angle' to activate Route Tracking. |
| Min Turn Radius | Turning radius (RAD) of applied route must exceed the 'Min Turn Radius' to activate Route Tracking. |
| Max Course Change | Course difference between all consecutive waypoints must be less than the 'Max Course Change' to activate Route Tracking. |
| ROT Max | Rate of Turn (ROT) of applied route must be less than the 'ROT Max' to activate Route Tracking. |
| Preset Time to WOL | TWOL (Time to Wheel Over Line) must be greater than the 'Preset Time to WOL' to activate Route Tracking. |
| End of Track | Route Tracking cannot be activated at the end of the track. |
| Position Monitoring | Secondary position sensor is mandatory for Route Tracking. |
| Heading Monitoring | Secondary heading sensor is mandatory for Route Tracking. |
| RPM Optimization | Speed control is available only when the planned RPM is optimized. |
|===
endif::build_target_nstd[]


===== Starting Conditions for CAA

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | All HiNAS software modules must function properly for CAA. |
| Position Data | Position data is mandatory for CAA. |
| COG Data | COG data is mandatory for CAA. |
| SOG Data | SOG data is mandatory for CAA. |
| RADAR Connection | RADAR connection is mandatory for CAA. |
| AIS Connection | AIS connection is mandatory for CAA activation. Loss of AIS targets reduces CAA performance. |
| Near Target | CAA function cannot be activated if an obstacle or vessel is detected at close range. |
| Safe Path Generation | CA Assistance should generate a safe path in high-risk collision situation. |
| Open Sea | CA Assistance is available in the open sea. |
| HiNAS H/HS-Mode | CA Assistance is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode. |
|===

// HiNAS Control Standard
ifdef::build_target_std[]
===== Starting Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| TC Status | All TC conditions are required to be Active to enable CAC. |
| CAA Status | All CAA conditions are required to be Active to enable CAC. |
| Safe Path Confirmation | Newly proposed CA path is required confirmation of the operator before use. |
| Preset Enter Distance | Distance to the start WPT is required to be less than the Preset Enter Distance to activate CAC. |
| Preset Enter Angle | The angle between the ownship's heading and the track course are required to be less than the Preset Enter Angle to activate CAC. |
| Device Connection | HiNAS HS-Mode requires both autopilot and BMS connections / HiNAS H-Mode requires an autopilot connection. |
| HiNAS H/HS-Mode | CAC is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode. |
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
===== Starting Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| Route Tracking Status | All Route Tracking conditions are required to be Active to enable CAC. |
| CAA Status | All CAA conditions are required to be Active to enable CAC. |
| Safe Path Confirmation | Newly proposed CA path is required confirmation of the operator before use. |
| Preset Enter Distance | Distance to the start WPT is required to be less than the Preset Enter Distance to activate CAC. |
| Preset Enter Angle | The angle between the ownship's heading and the track course are required to be less than the Preset Enter Angle to activate CAC. |
| Device Connection | HiNAS HS-Mode requires both autopilot and BMS connections / HiNAS H-Mode requires an autopilot connection. |
| HiNAS H/HS-Mode | CAC is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode. |
|===
endif::build_target_nstd[]



<<<

==== Low Performance Condition
// HiNAS Control Standard
ifdef::build_target_std[]
After activation of TC and CAA/CAC, an activated mode can still be operated but that function is not assured.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
After activation of Route Tracking and CAA/CAC, an activated mode can still be operated but that function is not assured.
endif::build_target_nstd[]

If any of the low performance condition is met, corresponding warnings or indications should be raised

// HiNAS Control Standard
ifdef::build_target_std[]
===== Low Performance Conditions for TC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| Speed Sensor | Loss of SOG or STW data reduces TC performance. |
| COG Sensor | Loss of COG data reduces TC performance. |
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
===== Low Performance Conditions for Route Tracking

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| Speed Sensor | Loss of SOG or STW data reduces Route Tracking performance. |
| COG Sensor | Loss of COG data reduces Route Tracking performance. |
|===
endif::build_target_nstd[]


===== Low Performance Conditions for CAA

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| AIS Connection | AIS must be available and functioning correctly. Loss of AIS targets reduces CAA performance. |
| Safe Path Generation | CAA should generate a safe path when new risks are detected. Failure to generate a path may force navigation near dangerous targets or beyond the planned route's XTL. |
|===


// HiNAS Control Standard
ifdef::build_target_std[]
===== Low Performance Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| TC Status | TC is in Low Performance state. Any TC condition in low performance state causes CAC Low Performance. |
| CAA Status | CAA is in Low Performance state. Any CAA condition in low performance state causes CAC Low Performance. |
| Safe Path Status | The safe path being followed is outdated. If CAA proposes a new safe path due to situation change, the operator must accept the new safe path to avoid this condition. |
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
===== Low Performance Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| Route Tracking Status | Route Tracking is in Low Performance state. Any Route Tracking condition in low performance state causes CAC Low Performance. |
| CAA Status | CAA is in Low Performance state. Any CAA condition in low performance state causes CAC Low Performance. |
| Safe Path Status | The safe path being followed is outdated. If CAA proposes a new safe path due to situation change, the operator must accept the new safe path to avoid this condition. |
|===
endif::build_target_nstd[]

<<<

==== Failure Condition
// HiNAS Control Standard
ifdef::build_target_std[]
TC/CAC automatically de-activated if a failure occurs, ensuring the safety and stability of the vessel.
When this occurs, a TRK Control Stop warning is issued to notify the operator of the deactivation.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
Route Tracking/CAC automatically de-activated if a failure occurs, ensuring the safety and stability of the vessel.
When this occurs, a Route Tracking Stop warning is issued to notify the operator of the deactivation.
endif::build_target_nstd[]

// HiNAS Control Standard
ifdef::build_target_std[]
===== Failure Conditions for TC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | All HiNAS software modules must function properly for TC. |
| Position Sensor | Position data is mandatory for TC. |
| Heading Sensor | Heading data is mandatory for TC. |
| Autopilot Override | When TKM autopilot switches from Ext.HC to Manual or HC mode, TC is deactivated / When YDK autopilot switches from AUTO to Manual mode, TC is deactivated. |
| Autopilot Ext.HC | When TKM autopilot doesn't enter Ext.HC mode within 60s of TC activation, TC is deactivated. Press the [Ext.HC] button within 60s to keep the function active. | For TKM autopilot only
| Autopilot Connection | Autopilot connection is required for TC. |
| Autopilot Signal Validity | Valid autopilot signal is required for TC. |
| BMS Override | When BMS auto mode switches from ON to OFF in HiNAS HS-Mode, TC is deactivated. | In HiNAS HS-Mode only
| BMS Ready | In HiNAS HS-Mode, BMS must be ready for TC to work properly. | In HiNAS HS-Mode only
| BMS Signal Validity | In HiNAS HS-Mode, a valid BMS signal is required for TC. | In HiNAS HS-Mode only
| RPM Range | In HiNAS HS-Mode, HiNAS command RPM must be within the 'RPM Range' for TC to work properly. | In HiNAS HS-Mode only
| BMS(HiNAS) | In HiNAS HS mode, when BMS doesn't enter HiNAS mode within 60s of TC activation, TC is deactivated. | In HiNAS HS-Mode only
| Position Monitoring | Secondary position sensor is mandatory for TC. |
| Heading Monitoring | Secondary heading sensor is mandatory for TC. |
| RPM Optimization | Speed control is available only when the planned RPM is optimized. |
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
===== Failure Conditions for Route Tracking

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | All HiNAS software modules must function properly for Route Tracking. |
| Position Sensor | Position data is mandatory for Route Tracking. |
| Heading Sensor | Heading data is mandatory for Route Tracking. |
| Autopilot Override | When TKM autopilot switches from Ext.HC to Manual or HC mode, Route Tracking is deactivated / When YDK autopilot switches from AUTO to Manual mode, Route Tracking is deactivated. |
| Autopilot Ext.HC | When TKM autopilot doesn't enter Ext.HC mode within 60s of Route Tracking activation, Route Tracking is deactivated. Press the [Ext.HC] button within 60s to keep the function active. | For TKM autopilot only
| Autopilot Connection | Autopilot connection is required for Route Tracking. |
| Autopilot Signal Validity | Valid autopilot signal is required for Route Tracking. |
| BMS Override | When BMS auto mode switches from ON to OFF in HiNAS HS-Mode, Route Tracking is deactivated. | In HiNAS HS-Mode only
| BMS Ready | In HiNAS HS-Mode, BMS must be ready for Route Tracking to work properly. | In HiNAS HS-Mode only
| BMS Signal Validity | In HiNAS HS-Mode, a valid BMS signal is required for Route Tracking. | In HiNAS HS-Mode only
| RPM Range | In HiNAS HS-Mode, HiNAS command RPM must be within the 'RPM Range' for Route Tracking to work properly. | In HiNAS HS-Mode only
| BMS(HiNAS) | In HiNAS HS mode, when BMS doesn't enter HiNAS mode within 60s of Route Tracking activation, Route Tracking is deactivated. | In HiNAS HS-Mode only
| Position Monitoring | Secondary position sensor is mandatory for Route Tracking. |
| Heading Monitoring | Secondary heading sensor is mandatory for Route Tracking. |
| RPM Optimization | Speed control is available only when the planned RPM is optimized. |
|===
endif::build_target_nstd[]


===== Failure Conditions for CAA

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | All HiNAS software modules must function properly for CAA to work properly. |
| Position Data | Position data is mandatory for CAA. CAA is deactivated if position sensor fails for extended period. |
| COG Data | COG data is mandatory for CAA. CAA is deactivated if COG sensor fails for extended period. |
| SOG Data | SOG data is mandatory for CAA. CAA is deactivated if SOG sensor fails for extended period. |
| RADAR Connection | RADAR must be connected for CAA to work properly. CAA is deactivated if RADAR connection is lost for extended period. |
| Near Target | CAA function is deactivated if there is an obstacle or vessel in close range. CAA cannot operate safely with nearby targets. |
|===

// HiNAS Control Standard
ifdef::build_target_std[]
===== Failure Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| TC Status | All TC conditions must function properly for CAC to work properly. CAC is deactivated if TC is not in Active or Low Performance state. |
| CAA Status | All CAA conditions must function properly for CAC to work properly. CAC is deactivated if CAA is not in Active or Low Performance state. |
| Device Connection | HiNAS HS-Mode requires both autopilot and BMS connections / HiNAS H-Mode requires an autopilot connection. CAC is deactivated if required device connections are not established. |
| HiNAS H/HS-Mode | CAC is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode. CAC is deactivated if system is not in H-Mode or HS-Mode. |
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
===== Failure Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| Route Tracking Status | All Route Tracking conditions must function properly for CAC to work properly. CAC is deactivated if Route Tracking is not in Active or Low Performance state. |
| CAA Status | All CAA conditions must function properly for CAC to work properly. CAC is deactivated if CAA is not in Active or Low Performance state. |
| Device Connection | HiNAS HS-Mode requires both autopilot and BMS connections / HiNAS H-Mode requires an autopilot connection. CAC is deactivated if required device connections are not established. |
| HiNAS H/HS-Mode | CAC is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode. CAC is deactivated if system is not in H-Mode or HS-Mode. |
|===
endif::build_target_nstd[]