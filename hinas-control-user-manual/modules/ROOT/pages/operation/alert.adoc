=== Alert
:imagesdir: images/operation/alerts

[.lead]
==== Alert

The alerts are announcing abnormal situations and conditions requiring attention.  
An alert provides information about how to announce this event in a defined way to the system and the operator.

===== Alert Priority

The alerts are divided in four priorities: Emergency alarm, Alarm, Warning, and Caution.  
The priority of an alert is indicated by the color of the alert and the sound of the alert.  
It is also indicated by the way the alert is displayed on the screen.

[options="header",cols="1,4",frame="all",grid="all"]
|===
|Priority 
|Description

|Emergency alarm
|Highest priority of an alert. Alarms which indicate immediate danger to human life or to the ship and its machinery exits and require immediate action.

|Alarm
|An alarm is a high-priority alert. Condition requiring immediate attention and action by the bridge team, to maintain the safe navigation of the ship.

|Warning
|Condition requiring immediate attention, but no immediate action by the bridge team. Warnings are presented for precautionary reasons to make the bridge team aware of changed conditions which are not immediately hazardous, but may become so if no action is taken.

|Caution
|Lowest priority of an alert. Awareness of a condition which does not warrant an alarm or warning condition, but still requires attention out of the ordinary consideration of the situation or of given information.
|===

===== Alert Category

The alerts are divided in three categories: A, B, and C.

[options="header",cols="1,5",frame="all",grid="all"]
|===
|Category |Description
|A
|Category A alerts for which graphical information at the task station directly assigned to the function generating the alert is necessary, as decision support for the evaluation of the alert related condition.

|B
|Category B alerts where no additional information for decision support is necessary.

|C
|Category C alerts that cannot be acknowledged on the bridge but for which information is required about the status and treatment of the alert.

|===

==== Alert Presentation and Handling

When an alert is generated, the alert is displayed on the dedicated area of the screen with buzzer sound. 

===== Dedicated Area

Dedicated area located in the top left corner of the display, contains the number of the active alerts, [alert list/history] button, [silence] button, and display for the highest priority of active alert.

// dedicated area image
[options="align-center"]
image::Dedicated_Area.png[width=700,align="center"]

The number of the alerts which priority of alarm, warning, and caution are shown in the left side of the dedicated area. 
With the [alert list/history] button, the operator can see the list of the alerts and the operator can temporarily stop the buzzer sound with the [silence] icon button.
The highest priority of active alert is shown in the right side of the dedicated area with state icon, and the [acknowledge] button will be shown when the highest priority of active alert is alarm or warning.

About handling the alerts (acknowledge, silence), see <<Alert Handling>>.

===== Alert State Icon

The table below shows the alert state icons representing the status of the alerts.

[options="header",cols="1,1,3,3,^1",frame="all",grid="all"]
|===
|No. |Priority |Alert State |Description |Icon graphic
|1 .5+|Alarm |Active - Unacknowledged | A flashing red triangle with a symbol of loudspeaker |image:alert-state-icon-2.svg[]
|2 |Active - Silenced | A flashing red triangle with a symbol of loudspeaker overlayed with a diagonal line. |image:alert-state-icon-3.svg[]
|3 |Active - Acknowledged | A red triangle with an exclamation mark |image:alert-state-icon-8.svg[]
|4 |Active - Responsibility transferred | A red triangle with an arrow pointing to the right |image:alert-state-icon-9.svg[]
|5 |Rectified - Unacknowledged | A flashing red triangle with a tick mark |image:alert-state-icon-6.svg[]
|6 .5+|Warning |Active - Unacknowledged | A flashing yellowish orange circle with a symbol of loudspeaker |image:alert-state-icon-4.svg[]
|7 |Active - Silenced | A flashing yellowish orange circle with a symbol of loudspeaker overlayed with a diagonal line. |image:alert-state-icon-5.svg[]
|8 |Active - Acknowledged | A yellowish orange circle with an exclamation mark |image:alert-state-icon-10.svg[]
|9 |Active - Responsibility transferred | A yellowish orange circle with an arrow pointing to the right |image:alert-state-icon-11.svg[]
|10 |Rectified - Unacknowledged | A flashing yellowish orange circle with a tick mark |image:alert-state-icon-7.svg[]
|11 |Caution |Active | A yellow square with an exclamation mark |image:alert-state-icon-12.svg[]
|===

===== Alert List

The list of the alerts is shown when the operator clicks the [alert list/history] button.

// alert list image
[options="align-center"]
image::Alert_List.png[width=700,align="center"]

The list of the alerts is shown in the order of the priority of the alerts. 
The contents of the alert which is shown in the list are:

* Priority and status of the alert (with icon)
* Alert ID
* Alert title
* Alert description
* Alert occurrence UTC time
* Alert acknowledge UTC time (if the alert is acknowledged)

If the alert description is too long and gets cut off, the operator can see the full description by hovering the mouse pointer over the alert. 
The alert description includes the situation consequence, the action to be taken and further information about the remedy.
The alert list shows the maximum of 20 alerts in each page, and the operator can see the other pages by clicking the [next/previous] button, or page number.

The order of the alerts in the list is determined by the priority of the alerts.
Details of the order of the alerts are as follows:

[options="header",cols="1,2,4,2,2,2",frame="all",grid="all"]
|===
|Order |Priority |State |Display after acknowledgement |Display after rectifying |Display after responsibility transfer
|1 |Alarm |Active - Unacknowledged + 
& Active - Silenced | 5 | 3 | 6
|2 |Warning |Active - Unacknowledged + 
& Active - Silenced | 7 | 4 | 8
|3 |Alarm |Rectified - Unacknowledged | 10 | - | -
|4 |Warning |Rectified - Unacknowledged | 11 | - | -
|5 |Alarm |Active - Acknowledged | - | 10 | -
|6 |Alarm |Active - Responsibility transferred | 5 | 10 | -
|7 |Warning |Active - Acknowledged | - | 11 | -
|8 |Warning |Active - Responsibility transferred | 7 | 11 | -
|9 |Caution |Active | - | 12 | -
|10 |Alarm |Normal(Not display) | - | - | -
|11 |Warning |Normal(Not display) | - | - | -
|12 |Caution |Normal(Not display) | - | - | -
|===

If the order of the alerts is the same, the newest alert is shown at the top of the list.

===== Alert Handling

The alarm and warning priority alerts require the attention (and action) of the operator.
There are two ways to handle the alerts: acknowledge and silence as explained in <<Alert Acknowledge for Warning and Alarm>> and <<Alert Silence>>, respectively. 
Optionally, if the central alert management system(CAMS) or the device which have the revaluate function are available, the responsibility of the alert can be transferred to them.

====== Alert Acknowledge for Warning and Alarm

The operator can acknowledge the alert by clicking the [acknowledge] button in both the dedicated area and the alert list.
The highest priority alert will be acknowledged by default when the operator clicks the [acknowledge] button.
However, the operator can acknowledge the other alerts by selecting them in the list and clicking the [acknowledge] button.
Acknowledged alerts will change their state icons to *Active - Acknowledged* state icon as shown in <<Alert State Icon>>.

====== Alert Silence

The operator can temporarily stop the buzzer sound by clicking the [silence] button in both the dedicated area and the alert list.
The silence will be applied to all the active alerts, and it will last for 30 seconds.
During the silence period, the silenced alerts will change their state icon to the *Active - Silenced* state icon as shown in <<Alert State Icon>>.
The alert which generates during the silence period will not be silenced.

====== Alert Responsibility Transfer Warning and Alarm

When the central alert management system(CAMS) or the device which the revaluate function are connected to HiNAS, they can take over the responsibility of the alert. 
The category B alerts can be transferred, and the category A alerts can be optionally transferred only when the devices are possible to provide the enough graphical information instead of HiNAS.
The responsibility transferred alerts will change their state icon to the *Active - Responsibility transferred* state icon as shown in <<Alert State Icon>>. 
The priority of the alert or even the alert itself can be revaluated by the CAMS or the device.

====== Normalize Alert

The alerts are normalized when the alert is rectified. For the alarm and warning priority alerts, acknowledgement from the operator is required additionally. In the case of the responsibility transferred alerts, even if the alert priority is alarm or warning, the alert will be normalized with only the rectification.

===== Alert History

The alert history stores the information of the alerts which have occurred in the past. The history last for 2 months, and the operator can see the history by clicking the [alert history] button in the alert list.

// alert history image
[options="align-center"]
image::Alert_History.png[width=700,align="center"]

The contents of the alert history which is shown in the list are:

* Alert ID
* Alert title
* Alert description
* Alert occurrence UTC time
* Alert acknowledge UTC time
* Alert rectify UTC time

The operator can filter the alert history by the date which the alert occurred with calendar picker.
The alert history shows the maximum of 20 alerts in each page, and the operator can see the other pages by clicking the [next/previous] button, or page number.

The order of the alerts in the history is determined by the occurrence time of the alerts.

==== Alert Escalation

The alert escalation is the process of refreshing the attention of the operator to the alert.
The alert escalation is applied to the alarm and warning priority alerts.
The alert escalation is applied when the alert is not acknowledged within a certain period.

===== Warning Priority Alert Escalation
The warning priority alerts are escalated in two ways: repeating the warning priority alert and escalating to the alarm priority alert.
The repeating warnings has their own repeating period, and the alert will be repeated as a warning until the alert is acknowledged.
The default repeating period of the alerts repeating as a warning cannot be exceeded 5 minutes.
Escalating to tha alarm priority alert is applied when the warning priority alert is not acknowledged within the 30 seconds after the alert is generated.
Not every warning priority alert is escalated to the alarm priority alert, and details of the escalation are described in *alert list* as shown in <<Alert List of HiNAS Control>>.

===== Alarm Priority Alert Escalation
Unlike the warning priority alerts, the alarm priority alerts require the action of the operator for the safety of the ship and the crew.
So the alarm priority alerts are always escalated to the back-up navigator alarm which call the back-up navigator to the bridge.
The back-up navigator alarm is transfer to the BNWAS (Bridge Navigational Watch Alarm System) when the alarm priority alerts are not acknowledged by the operator within the 30 seconds after the alert is generated.

==== Alert List of HiNAS Control
NA in Escalation column means that the alert escalate to the back-up navigator alarm.
If the cell is not applicable, it is marked with a hyphen '-'.

Associated Function column shows the function which is related to the alert.
The alert will be raised if the associated function is enabled and required for that function.

// HiNAS Control Standard
ifdef::build_target_std[]
* TC: Track Control
* CAA: Collision Avoidance Assistance
* CAC: Collision Avoidance Control
* POSN MON: Position Monitor
* HDG MON: Heading Monitor
* Lost TGT: Lost Target Warning function
* Collision Risk: Collision Risk Alerting function
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
* Route Tracking: Route Tracking function
* CAA: Collision Avoidance Assistance
* CAC: Collision Avoidance Control
* POSN MON: Position Monitor
* HDG MON: Heading Monitor
* Lost TGT: Lost Target Warning function
* Collision Risk: Collision Risk Alerting function
endif::build_target_nstd[]

// For TC only version
// [cols="1,1,2,3,1,1,1",frame="all",grid="all"]
// |===
// 2+|Alert ID .2+|Alert Title .2+|Alert Description .2+|Priority/ Category .2+|Escalation .2+|Associated Function
// |ALF ID |Instance No. 
// |3001 |- |Lost Network |All function will stop. Take over the control. Check the onboard network status connected to HiNAS Control. |Alarm / B |NA |TC
// |3003 |- |Lost Network |HiNAS Control lost network connection. Check the onboard network status connected to HiNAS Control. |Caution / B | - |-
// |3007 |1 |TRK Control Stop |Last command will be maintained. Take over the control. |Alarm / B |NA |TC
// |3008 |1 |TRK Control Stop |Last command will be maintained. Take over the control. |Warning / B |Escalate to Alarm |TC
// |3008 |3 |POSN MON Stop |HiNAS main functions will stop. Check the connection with GPSs and network.|Warning / B |Repeat Warning |POSN MON
// |3008 |4 |HDG MON Stop |Control functions will stop. Check the connection with Gyros and network. |Warning / B |Repeat Warning |HDG MON
// |3012 |1 |Doubtful POSN |Position data from two or more GPS difference exceeds the threshold. Check the connection with GPSs and network. |Warning / A |Repeat Warning |POSN MON
// |3012 |2 |Doubtful HDG |Heading data from two or more Gyro difference exceeds the threshold. Check the connection with Gyros and network. |Warning / A |Repeat Warning |HDG MON
// |3014 |101 |Lost Position |Track Control will stop. Take over the control. Change the primary position sensor, or verify its status and connection to restore Track Control. |Alarm / B |NA |TC
// |3014 |102 |Lost SOG/COG |Track Control performance can be reduced. Change the primary SOG/COG sensor, or verify its status and connection to recover the performance of Track Control. |Alarm / B |NA |TC
// |3014 |104 |Lost Heading |Track Control will stop. Take over control. Change the primary heading sensor, or verify its status and connection to restore Track Control. |Alarm / B |NA |TC
// |3014 |105 |Lost ROT |Track Control will stop. Take over the control. Change the primary ROT sensor, or verify its status and connection to restore Track Control. |Alarm / B |NA |TC
// |3014 |106 |Lost STW |Track Control performance can be reduced. Change the primary STW sensor, or verify its status and connection to recover the performance of Track Control. |Alarm / B |NA |TC
// |3015 |100 |Lost Sensor Data |HiNAS functions may not be activated. Check the details on Operation Detail. Change the primary sensor, or verify its status and connection to enable functions. |Warning / B |Repeat Warning | -
// |3016 |4 |Lost EO Camera |Lost EO camera input. Check the connection with EO camera and network. |Caution / B |- |-
// |3016 |5 |Lost IR Camera |Lost IR camera input. Check the connection with IR camera and network. |Caution / B |- |-
// |3016 |100 |Lost AUX Data |Lost AUX sensor data cannot be selected. Verify the auxiliary sensor or check its connection with HiNAS Control. |Caution / B |- |-
// |3016 |201 |Lost Radar |CA assistance cannot be activated. Verify radar status and connection to restore CA assistance function. |Caution / B |- |-
// |3016 |202 |Lost AIS |CA assistance cannot be activated. Verify AIS status and connection to restore CA assistance function. |Caution / B |- |-
// |3022 |- |Power fail |HiNAS Control detected power failure. Take over the control. Check the power source. |Warning / B |Repeat Warning |-
// |3024 |1 |XTE High |XTE exceeds the pre-planned threshold. Take over the control if ownship cannot return into XTE limit automatically. |Alarm / B |NA |TC
// |3024 |2 |End of Track |Ready for take the control authority. Last command will be maintained even ownship pass the last waypoint. Take over the control. |Alarm / A |NA |TC
// |3025 |1 |End of Track |Ready for take the control authority. Last command will be maintained even ownship pass the last waypoint. Take over the control. |Warning / A |Escalate to Alarm |TC
// |3027 |- |Actual CRS CHG |Ownship is crossing the wheel-over-line. Keep an eye on the ownship’s status. |Alarm / A |NA |TC
// |3028 |- |Actual CRS CHG |Approaching WOL in 30 seconds. Keep an eye on ownship’s status. |Warning / A |Escalate to Alarm |TC
// |3032 |- |CRS Difference |Course differ from pre-planned course. Take over the control if ownship cannot return into course difference limit automatically. |Warning / B |Repeat Warning |TC
// |3038 |- |Early CRS CHG |Approaching WOL in 3 minutes. Keep an eye on ownship’s status. |Warning / A |Escalate to Alarm |TC
// |3052 |- |Lost Target |Detected target has been lost. |Warning / A |Repeat Warning |Lost TGT
// |3065 |- |Low Speed |Track Control performance can be reduced. Verify that it's still safe to follow the path. |Warning / B |Repeat Warning |TC
// |3077 |- |Early CRS CHG |Approaching WOL in 2 mins 30secs. Keep an eye on the vessel’s status. |Alarm / A |NA |TC
// |777001 |- |HiNAS SW Abn. |HiNAS Control can operate unintended. Take over the control. |Alarm / B |NA |TC
// |777003 |- |HiNAS SW Abn. |HiNAS Control can operate unintended. If HiNAS Control not recover itself, contact to customer service. |Caution / B |- |-
// |777006 |- |Lost Autopilot |HiNAS Control cannot start Track Control. Check the connection between HiNAS Control and the autopilot. |Caution / B |- |-
// |777009 |- |Lost BMS |HiNAS Control cannot start Track Control(HS). Track Control(H) might be able to start. Switch “Control Mode” from HS to H on setting. Check the connection between HiNAS Control and the BMS. |Caution / B |- |-
// |777012 |- |Route Mismatch |ECDIS route differs from HiNAS. Reapply the route to make HiNAS follow the intended route. |Warning / A |Repeat Warning |-
// |777088 |1 |AIS Capacity Max |100% capacity of target detect module. Observe the surroundings with bare eyes or other equipment. |Warning / A |Repeat Warning |-
// |777088 |2 |ARPA CAP Max |100% capacity of target detect module. Observe the surroundings with bare eyes or other equipment. |Warning / A |Repeat Warning |-
// |777089 |1 |AIS Capacity 95% |95% of capacity of target detect module. |Caution / A |- |-
// |777089 |2 |ARPA CAP 95% |95% of capacity of target detect module. |Caution / A |- |-
// |777099 |- |Camera Detection |HiNAS Camera detected the targets. |Caution / A |- |-
// |777102 |1 |Low Performance |Switch to valid speed sensor or turn off. Change the primary SOG sensor, or verify its status and connection to restore the performance of Track Control. |Warning / B |Repeat Warning |TC
// |777109 |- |Lost UPS |HiNAS Control cannot detect the power failure. Check the network connection between HiNAS Control and UPS. |Caution / B |- |-
// |===

// HiNAS Control Standard
ifdef::build_target_std[]
[cols="1,1,2,3,1,1,1",frame="all",grid="all"]
|===
2+|Alert ID .2+|Alert Title .2+|Alert Description .2+|Priority/ Category .2+|Escalation .2+|Associated Function
|ALF ID |Instance No.
|3001 |- |Lost Network |All function will stop. Take over the control. Check the onboard network status connected to HiNAS Control. |Alarm / B |NA |TC, CAC
|3002 |- |Lost Network |CA Assistance will stop. All other functions will also stop. Check the onboard network status connected to HiNAS Control. |Warning / B |Repeat Warning |CAA
|3003 |- |Lost Network |HiNAS Control lost network connection. Check the onboard network status connected to HiNAS Control. |Caution / B | - |-
|3007 |1 |TRK Control Stop |Last command will be maintained. Take over the control. |Alarm / B |NA |TC
|3007 |2 |CA Function Stop |System follows the last active safe path without updating the safe path. Verify that it's still safe to follow the path. |Alarm / B |NA |CAC 
|3007 |3 |CA/TC Stop |Last command will be maintained. Take oer the control. |Alarm / B |NA |CAC
|3008 |1 |TRK Control Stop |Last command will be maintained. Take over the control. |Warning / B |Escalate to Alarm |TC
|3008 |2 |CA Assist Stop |Cannot generate safe collision avoidance path. Observe the surroundings with bare eyes or other equipment. |Warning / B |Repeat Warning |CAA
|3008 |3 |POSN MON Stop |HiNAS main functions will stop. Check the connection with GPSs and network.|Warning / B |Repeat Warning |POSN MON
|3008 |4 |HDG MON Stop |Control functions will stop. Check the connection with Gyros and network. |Warning / B |Repeat Warning |HDG MON
|3012 |1 |Doubtful POSN |Position data from two or more GPS difference exceeds the threshold. Check the connection with GPSs and network. |Warning / A |Repeat Warning |POSN MON
|3012 |2 |Doubtful HDG |Heading data from two or more Gyro difference exceeds the threshold. Check the connection with Gyros and network. |Warning / A |Repeat Warning |HDG MON
|3014 |101 |Lost Position |Track Control will stop. Take over the control. Change the primary position sensor, or verify its status and connection to restore Track Control. |Alarm / B |NA |TC
|3014 |102 |Lost SOG/COG |Track Control performance can be reduced. Change the primary SOG/COG sensor, or verify its status and connection to recover the performance of Track Control. |Alarm / B |NA |TC
|3014 |104 |Lost Heading |Track Control will stop. Take over control. Change the primary heading sensor, or verify its status and connection to restore Track Control. |Alarm / B |NA |TC
|3014 |105 |Lost ROT |Track Control will stop. Take over the control. Change the primary ROT sensor, or verify its status and connection to restore Track Control. |Alarm / B |NA |TC
|3014 |106 |Lost STW |Track Control performance can be reduced. Change the primary STW sensor, or verify its status and connection to recover the performance of Track Control. |Alarm / B |NA |TC
|3014 |201 |Lost Position |HiNAS Control functions will stop. Take over the control. Change the primary position sensor, or verify its status and connection to restore functions. |Alarm / B |NA |TC and CAA
|3014 |202 |Lost SOG/COG |CA Assistance will stop and the vessel will follow the track with low performance. Change the primary SOG/COG sensor, or verify its status and connection to recover CA Assistance and the performance of Track Control. |Alarm / B |NA |TC and CAA
|3014 |204 |Lost Heading |HiNAS Control functions will stop. Take over the control. Change the primary heading sensor, or verify its status and connection to restore functions. |Alarm / B |NA |CAC
|3014 |205 |Lost ROT |HiNAS Control functions will stop. Take over the control. Change the primary ROT sensor, or verify its status and connection to restore functions. |Alarm / B |NA |CAC
|3014 |302 |Lost SOG/COG |CA Assistance will stop and the vessel will follow the active safe path with low performance. Change the primary SOG/COG sensor, or verify its status and connection to recover CA Assistance and the performance of Track Control. |Alarm / B |NA |CAC
|3015 |100 |Lost Sensor Data |HiNAS functions may not be activated. Check the details on Operation Detail. Change the primary sensor, or verify its status and connection to enable functions. |Warning / B |Repeat Warning | -
|3015 |201 |Lost Position |CA Assistance will stop. Change the primary position sensor, or verify its status and connection to restore CA Assistance. |Warning /B |Repeat Warning |CAA
|3015 |202 |Lost SOG/COG |CA Assistance will stop. Change the primary SOG/COG sensor, or verify its status and connection to restore CA Assistance. |Warning / B |Repeat Warning |CAA
|3015 |204 |Lost Radar|CA assistance will stop. Verify radar status and connection to restore CA assistance function.|Warning / B |Repeat Warning |CAA
|3015 |205 |Lost AIS |CA assistance will not consider AIS target(s) for collision avoidance. Verify AIS status and connection to restore AIS targets.|Warning / B |Repeat Warning |CAA
|3016 |4 |Lost EO Camera |Lost EO camera input. Check the connection with EO camera and network. |Caution / B |- |-
|3016 |5 |Lost IR Camera |Lost IR camera input. Check the connection with IR camera and network. |Caution / B |- |-
|3016 |100 |Lost AUX Data |Lost AUX sensor data cannot be selected. Verify the auxiliary sensor or check its connection with HiNAS Control. |Caution / B |- |-
|3016 |201 |Lost Radar |CA assistance cannot be activated. Verify radar status and connection to restore CA assistance function. |Caution / B |- |-
|3016 |202 |Lost AIS |CA assistance cannot be activated. Verify AIS status and connection to restore CA assistance function. |Caution / B |- |-
|3022 |- |Power fail |HiNAS Control detected power failure. Take over the control. Check the power source. |Warning / B |Repeat Warning |-
|3024 |1 |XTE High |XTE exceeds the pre-planned threshold. Take over the control if ownship cannot return into XTE limit automatically. |Alarm / B |NA |TC
|3024 |2 |End of Track |Ready for take the control authority. Last command will be maintained even ownship pass the last waypoint. Take over the control. |Alarm / A |NA |TC
|3025 |1 |End of Track |Ready for take the control authority. Last command will be maintained even ownship pass the last waypoint. Take over the control. |Warning / A |Escalate to Alarm |TC
|3027 |- |Actual CRS CHG |Ownship is crossing the wheel-over-line. Keep an eye on the ownship’s status. |Alarm / A |NA |TC, CAC
|3028 |- |Actual CRS CHG |Approaching WOL in 30 seconds. Keep an eye on ownship’s status. |Warning / A |Escalate to Alarm |TC, CAC
|3032 |- |CRS Difference |Course differ from pre-planned course. Take over the control if ownship cannot return into course difference limit automatically. |Warning / B |Repeat Warning |TC, CAC
|3038 |- |Early CRS CHG |Approaching WOL in 3 minutes. Keep an eye on ownship’s status. |Warning / A |Escalate to Alarm |TC, CAC
|3052 |- |Lost Target |Detected target has been lost. |Warning / A |Repeat Warning |Lost TGT
|3065 |- |Low Speed |Track Control performance can be reduced. Verify that it's still safe to follow the path. |Warning / B |Repeat Warning |TC
|3077 |- |Early CRS CHG |Approaching WOL in 2 mins 30secs. Keep an eye on the vessel’s status. |Alarm / A |NA |TC, CAC
|777001 |- |HiNAS SW Abn. |HiNAS Control can operate unintended. Take over the control. |Alarm / B |NA |TC, CAC
|777003 |- |HiNAS SW Abn. |HiNAS Control can operate unintended. If HiNAS Control not recover itself, contact to customer service. |Caution / B |- |-
|777006 |- |Lost Autopilot |HiNAS Control cannot start Track Control. Check the connection between HiNAS Control and the autopilot. |Caution / B |- |-
|777009 |- |Lost BMS |HiNAS Control cannot start Track Control(HS). Track Control(H) might be able to start. Switch “Control Mode” from HS to H on setting. Check the connection between HiNAS Control and the BMS. |Caution / B |- |-
|777012 |- |Route Mismatch |ECDIS route differs from HiNAS. Reapply the route to make HiNAS follow the intended route. |Warning / A |Repeat Warning |-
|777026 |- |CA No Path |CA Assistance failed to propose a new safe path. Users may need to exceed XTL for collision avoidance. |Caution / A |- |CAA, CAC
|777034 |- |Collision Danger |HiNAS Control detected collision danger. Keep an eye on the surroundings. Confirm the new safe path or disable HiNAS Control and be aware of vessel's safety. |Alarm / A |NA |(CAA, CAC) and Collision Risk
|777035 |- |Collision Danger |HiNAS Control detected collision danger. Keep an eye on the surroundings. Confirm the new safe path or disable HiNAS Control and be aware of vessel's safety. |Warning / A |Escalate to Alarm |(CAA, CAC) and Collision Risk
|777038 |- |High Speed TGT |30kn or higher speed target detected. Keep an eye on the target(s) and verify the safe path. The target’s high speed may affect the current or suggested safe path. |Warning / A |Repeat Warning |
|777043 |- |CA TGT Limit |Over 2 targets in CA detect range. Verify the safe path. More than 2 targets may degrade to generate the safe path. |Caution / A |- |
|777044 |- |Near Target |Target approaching very close. Keep an eye on the target. Be aware of the collision with the target. |Alarm / A |NA |CAA, CAC
|777047 |- |Exceed PDL |Exceeded Position Deviation Limit (PDL). Update to the new safe path or disable HiNAS Control and be aware of vessel’s safety. PDL is the boundary for HiNAS Control’s CA Control mode. For safe collision avoidance, ownship should not exceed the PDL. Exceeding PDL can generate a new safe path for safety. |Alarm / A |NA |CAC
|777088 |1 |AIS Capacity Max |100% capacity of target detect module. Observe the surroundings with bare eyes or other equipment. |Warning / A |Repeat Warning |-
|777088 |2 |ARPA CAP Max |100% capacity of target detect module. Observe the surroundings with bare eyes or other equipment. |Warning / A |Repeat Warning |-
|777089 |1 |AIS Capacity 95% |95% of capacity of target detect module. |Caution / A |- |-
|777089 |2 |ARPA CAP 95% |95% of capacity of target detect module. |Caution / A |- |-
|777099 |- |Camera Detection |HiNAS Camera detected the targets. |Caution / A |- |-
|777102 |1 |Low Performance |Switch to valid speed sensor or turn off. Change the primary SOG sensor, or verify its status and connection to restore the performance of Track Control. |Warning / B |Repeat Warning |TC, CAC
|777102 |2 |Low Performance |CA function will not consider AIS target for collision avoidance. Verify AIS status and connection to restore AIS targets. |Warning / B |Repeat Warning |CAA, CAC
// |777102 |3 |Low Performance |Update to the new safe path or turn off.  Ownship exceeded Position Deviation Limit (PDL), which is boundary for HiNAS Control’s CA Control mode. For safe operation, ownship should not exceed the PDL. |Warning / A |Repeat Warning |CAC
|777109 |- |Lost UPS |HiNAS Control cannot detect the power failure. Check the network connection between HiNAS Control and UPS. |Caution / B |- |-
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
[cols="1,1,2,3,1,1,1",frame="all",grid="all"]
|===
2+|Alert ID .2+|Alert Title .2+|Alert Description .2+|Priority/ Category .2+|Escalation .2+|Associated Function
|ALF ID |Instance No.
|3001 |- |Lost Network |All function will stop. Take over the control. Check the onboard network status connected to HiNAS Control. |Alarm / B |NA |Route Tracking, CAC
|3002 |- |Lost Network |CA Assistance will stop. All other functions will also stop. Check the onboard network status connected to HiNAS Control. |Warning / B |Repeat Warning |CAA
|3003 |- |Lost Network |HiNAS Control lost network connection. Check the onboard network status connected to HiNAS Control. |Caution / B | - |-
|3007 |1 |Route Tracking Stop |Last command will be maintained. Take over the control. |Alarm / B |NA |Route Tracking
|3007 |2 |CA Function Stop |System follows the last active safe path without updating the safe path. Verify that it's still safe to follow the path. |Alarm / B |NA |CAC
|3007 |3 |CA/Route Tracking Stop |Last command will be maintained. Take oer the control. |Alarm / B |NA |CAC
|3008 |1 |Route Tracking Stop |Last command will be maintained. Take over the control. |Warning / B |Escalate to Alarm |Route Tracking
|3008 |2 |CA Assist Stop |Cannot generate safe collision avoidance path. Observe the surroundings with bare eyes or other equipment. |Warning / B |Repeat Warning |CAA
|3008 |3 |POSN MON Stop |HiNAS main functions will stop. Check the connection with GPSs and network.|Warning / B |Repeat Warning |POSN MON
|3008 |4 |HDG MON Stop |Control functions will stop. Check the connection with Gyros and network. |Warning / B |Repeat Warning |HDG MON
|3012 |1 |Doubtful POSN |Position data from two or more GPS difference exceeds the threshold. Check the connection with GPSs and network. |Warning / A |Repeat Warning |POSN MON
|3012 |2 |Doubtful HDG |Heading data from two or more Gyro difference exceeds the threshold. Check the connection with Gyros and network. |Warning / A |Repeat Warning |HDG MON
|3014 |101 |Lost Position |Route Tracking will stop. Take over the control. Change the primary position sensor, or verify its status and connection to restore Route Tracking. |Alarm / B |NA |Route Tracking
|3014 |102 |Lost SOG/COG |Route Tracking performance can be reduced. Change the primary SOG/COG sensor, or verify its status and connection to recover the performance of Route Tracking. |Alarm / B |NA |Route Tracking
|3014 |104 |Lost Heading |Route Tracking will stop. Take over control. Change the primary heading sensor, or verify its status and connection to restore Route Tracking. |Alarm / B |NA |Route Tracking
|3014 |105 |Lost ROT |Route Tracking will stop. Take over the control. Change the primary ROT sensor, or verify its status and connection to restore Route Tracking. |Alarm / B |NA |Route Tracking
|3014 |106 |Lost STW |Route Tracking performance can be reduced. Change the primary STW sensor, or verify its status and connection to recover the performance of Route Tracking. |Alarm / B |NA |Route Tracking
|3014 |201 |Lost Position |HiNAS Control functions will stop. Take over the control. Change the primary position sensor, or verify its status and connection to restore functions. |Alarm / B |NA |Route Tracking and CAA
|3014 |202 |Lost SOG/COG |CA Assistance will stop and the vessel will follow the track with low performance. Change the primary SOG/COG sensor, or verify its status and connection to recover CA Assistance and the performance of Route Tracking. |Alarm / B |NA |Route Tracking and CAA
|3014 |204 |Lost Heading |HiNAS Control functions will stop. Take over the control. Change the primary heading sensor, or verify its status and connection to restore functions. |Alarm / B |NA |CAC
|3014 |205 |Lost ROT |HiNAS Control functions will stop. Take over the control. Change the primary ROT sensor, or verify its status and connection to restore functions. |Alarm / B |NA |CAC
|3014 |302 |Lost SOG/COG |CA Assistance will stop and the vessel will follow the active safe path with low performance. Change the primary SOG/COG sensor, or verify its status and connection to recover CA Assistance and the performance of Route Tracking. |Alarm / B |NA |CAC
|3015 |100 |Lost Sensor Data |HiNAS functions may not be activated. Check the details on Operation Detail. Change the primary sensor, or verify its status and connection to enable functions. |Warning / B |Repeat Warning | -
|3015 |201 |Lost Position |CA Assistance will stop. Change the primary position sensor, or verify its status and connection to restore CA Assistance. |Warning /B |Repeat Warning |CAA
|3015 |202 |Lost SOG/COG |CA Assistance will stop. Change the primary SOG/COG sensor, or verify its status and connection to restore CA Assistance. |Warning / B |Repeat Warning |CAA
|3015 |204 |Lost Radar|CA assistance will stop. Verify radar status and connection to restore CA assistance function.|Warning / B |Repeat Warning |CAA
|3015 |205 |Lost AIS |CA assistance will not consider AIS target(s) for collision avoidance. Verify AIS status and connection to restore AIS targets.|Warning / B |Repeat Warning |CAA
|3016 |4 |Lost EO Camera |Lost EO camera input. Check the connection with EO camera and network. |Caution / B |- |-
|3016 |5 |Lost IR Camera |Lost IR camera input. Check the connection with IR camera and network. |Caution / B |- |-
|3016 |100 |Lost AUX Data |Lost AUX sensor data cannot be selected. Verify the auxiliary sensor or check its connection with HiNAS Control. |Caution / B |- |-
|3016 |201 |Lost Radar |CA assistance cannot be activated. Verify radar status and connection to restore CA assistance function. |Caution / B |- |-
|3016 |202 |Lost AIS |CA assistance cannot be activated. Verify AIS status and connection to restore CA assistance function. |Caution / B |- |-
|3022 |- |Power fail |HiNAS Control detected power failure. Take over the control. Check the power source. |Warning / B |Repeat Warning |-
|3024 |1 |XTE High |XTE exceeds the pre-planned threshold. Take over the control if ownship cannot return into XTE limit automatically. |Alarm / B |NA |Route Tracking
|3024 |2 |End of Track |Ready for take the control authority. Last command will be maintained even ownship pass the last waypoint. Take over the control. |Alarm / A |NA |Route Tracking
|3025 |1 |End of Track |Ready for take the control authority. Last command will be maintained even ownship pass the last waypoint. Take over the control. |Warning / A |Escalate to Alarm |Route Tracking
|3027 |- |Actual CRS CHG |Ownship is crossing the wheel-over-line. Keep an eye on the ownship's status. |Alarm / A |NA |Route Tracking, CAC
|3028 |- |Actual CRS CHG |Approaching WOL in 30 seconds. Keep an eye on ownship's status. |Warning / A |Escalate to Alarm |Route Tracking, CAC
|3032 |- |CRS Difference |Course differ from pre-planned course. Take over the control if ownship cannot return into course difference limit automatically. |Warning / B |Repeat Warning |Route Tracking, CAC
|3038 |- |Early CRS CHG |Approaching WOL in 3 minutes. Keep an eye on ownship's status. |Warning / A |Escalate to Alarm |Route Tracking, CAC
|3052 |- |Lost Target |Detected target has been lost. |Warning / A |Repeat Warning |Lost TGT
|3065 |- |Low Speed |Route Tracking performance can be reduced. Verify that it's still safe to follow the path. |Warning / B |Repeat Warning |Route Tracking
|3077 |- |Early CRS CHG |Approaching WOL in 2 mins 30secs. Keep an eye on the vessel's status. |Alarm / A |NA |Route Tracking, CAC
|777001 |- |HiNAS SW Abn. |HiNAS Control can operate unintended. Take over the control. |Alarm / B |NA |Route Tracking, CAC
|777003 |- |HiNAS SW Abn. |HiNAS Control can operate unintended. If HiNAS Control not recover itself, contact to customer service. |Caution / B |- |-
|777006 |- |Lost Autopilot |HiNAS Control cannot start Route Tracking. Check the connection between HiNAS Control and the autopilot. |Caution / B |- |-
|777009 |- |Lost BMS |HiNAS Control cannot start Route Tracking(HS). Route Tracking(H) might be able to start. Switch "Control Mode" from HS to H on setting. Check the connection between HiNAS Control and the BMS. |Caution / B |- |-
|777012 |- |Route Mismatch |ECDIS route differs from HiNAS. Reapply the route to make HiNAS follow the intended route. |Warning / A |Repeat Warning |-
|777026 |- |CA No Path |CA Assistance failed to propose a new safe path. Users may need to exceed XTL for collision avoidance. |Caution / A |- |CAA, CAC
|777034 |- |Collision Danger |HiNAS Control detected collision danger. Keep an eye on the surroundings. Confirm the new safe path or disable HiNAS Control and be aware of vessel's safety. |Alarm / A |NA |(CAA, CAC) and Collision Risk
|777035 |- |Collision Danger |HiNAS Control detected collision danger. Keep an eye on the surroundings. Confirm the new safe path or disable HiNAS Control and be aware of vessel's safety. |Warning / A |Escalate to Alarm |(CAA, CAC) and Collision Risk
|777038 |- |High Speed TGT |30kn or higher speed target detected. Keep an eye on the target(s) and verify the safe path. The target's high speed may affect the current or suggested safe path. |Warning / A |Repeat Warning |
|777043 |- |CA TGT Limit |Over 2 targets in CA detect range. Verify the safe path. More than 2 targets may degrade to generate the safe path. |Caution / A |- |
|777088 |1 |AIS Capacity Max |100% capacity of target detect module. Observe the surroundings with bare eyes or other equipment. |Warning / A |Repeat Warning |-
|777088 |2 |ARPA CAP Max |100% capacity of target detect module. Observe the surroundings with bare eyes or other equipment. |Warning / A |Repeat Warning |-
|777089 |1 |AIS Capacity 95% |95% of capacity of target detect module. |Caution / A |- |-
|777089 |2 |ARPA CAP 95% |95% of capacity of target detect module. |Caution / A |- |-
|777099 |- |Camera Detection |HiNAS Camera detected the targets. |Caution / A |- |-
|777102 |1 |Low Performance |Switch to valid speed sensor or turn off. Change the primary SOG sensor, or verify its status and connection to restore the performance of Route Tracking. |Warning / B |Repeat Warning |Route Tracking, CAC
|777102 |2 |Low Performance |CA function will not consider AIS target for collision avoidance. Verify AIS status and connection to restore AIS targets. |Warning / B |Repeat Warning |CAA, CAC
// |777102 |3 |Low Performance |Update to the new safe path or turn off.  Ownship exceeded Position Deviation Limit (PDL), which is boundary for HiNAS Control's CA Control mode. For safe operation, ownship should not exceed the PDL. |Warning / A |Repeat Warning |CAC
|777109 |- |Lost UPS |HiNAS Control cannot detect the power failure. Check the network connection between HiNAS Control and UPS. |Caution / B |- |-
|===
endif::build_target_nstd[]