// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images


// HiNAS Control
ifdef::build_target_nstd[]
=== N/Route Tracking Modes
endif::build_target_nstd[]

// HiNAS Control Standard
ifdef::build_target_std[]
=== N/Track Control Modes
endif::build_target_std[]

N mode (Monitoring mode) displays interfaced sensor readings, streaming video, and geoinformation.
On the monitoring page, all information is presented over the base of the electronic nautical chart and the camera display.
Additionally, information generated by the recognition, planning, decision, and control functions is also displayed in the monitoring module.

Route Tracking function provides heading control and speed control using interfaced autopilot and BMS as described in <<ROUTE TRACKING>>.


// The information for the functionality of collision avoidance is displayed as shown in <<edited_day_NorTC>>.

// [[edited_day_NorTC]]
// .Collision avoidance related action and state items
// image::collision-avoidance/ca_related_action_state_v1.png[width=500, align="center"]


// [cols="<10%,<20%,<70%", options="header"]
// |===
// | No. | Item | Description

// | ①
// | Function Act 
// | Displays the status of autopilot, BMS, and Control Mode of TCS

// | ②
// | Track Control
// | On/off toggle for the activation of TC. Indicates whether the TC function is available and its current activation status. Refer to <<State Toggle>> for details.

// | ③
// | CA Assistance
// | On/off toggle for the activation of CAA. Indicates whether the CAA function is available and its current activation status. Refer to <<Target Symbols>> for details.

// | ④
// | CA Control
// | Displays the status of CAC. The statuses include Not Ready, Ready, and On, which mean unavailable, available, and in use, respectively.

// | ⑤
// | Green target
// | Refer to <<Target Symbols>> Safe vessels are displayed in green. 

// | ⑥
// | Red target
// | Dangerous targets are displayed in red. Detection of a dangerous target triggers the generation of a safe path.
// |===

// Clicking a target provides information of tareget as displayed in ⑥ in <<edited_day_NorTC>>.
// Only one target information can be viewed at once.
// CPA, TCPA, distance (DIST), bearing (TBRG), and SOG are included.
// Additionally, the source of each item are presented.
// Here, CALC means that the value has come from the calculation of HiNAS. 
// The name of the sensor means that the value has come from the sensor.
