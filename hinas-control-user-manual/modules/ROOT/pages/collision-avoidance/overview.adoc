// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Collision Avoidance Functions

HiNAS CA function supports safe vessel navigation by suggesting a safe path while maintaining track control.  
HiNAS provides the following CA-related functions:

* CAA: Evaluates collision risks and suggests a safe path to mitigate them.
* CAC: Steers the vessel along the suggested (system-generated) path after operator confirmation.


=== CAA Function

CAA offers the following capabilities:

* Proposes one safe path at a time to the operator to mitigate collision risks.
* If no suitable path can be generated, CAA may refrain from making a suggestion.
* The suggested safe path remains within grounding-safe areas.
// HiNAS Control
ifdef::build_target_nstd[]
* Any safe path suggested by CAA does not extend beyond XTE < 5 NM from the preplanned route.
endif::build_target_nstd[]
// HiNAS Control Standard
ifdef::build_target_std[]
* Any safe path suggested by CAA does not extend beyond the XTL of the preplanned route.
endif::build_target_std[]
* CAA provides a function to fix the current safe path and prevent further updates.  
If this feature is active, CAA issues an alert if a newly generated safe path invalidates the fixed one.


=== CAC Function

CAC offers the following capabilities:

* Steers the vessel along the safe path suggested by CAA.
// HiNAS Control
ifdef::build_target_nstd[]
* Utilizes Route Tracking for vessel control.
endif::build_target_nstd[]
// HiNAS Control Standard
ifdef::build_target_std[]
* Utilizes TCS for vessel control.
endif::build_target_std[]
* A safe path becomes active only after it is accepted by the operator.  
CAC does not modify or deactivate the active path without user confirmation, except when the path reaches its end.

// HiNAS Control
ifdef::build_target_nstd[]
[NOTE]
====
CAA and Route Tracking operate independently.  
Even if a safe path is shown, the vessel will not follow it unless CAC is active.  
CAC can only be enabled when both CAA and Route Tracking are active.
====
endif::build_target_nstd[]

// HiNAS Control Standard
ifdef::build_target_std[]
[NOTE]
====
CAA and TC operate independently.  
Even if a safe path is shown, the vessel will not follow it unless CAC is active.  
CAC can only be enabled when both CAA and TC are active.
====
endif::build_target_std[]



=== Modes of Operation

// HiNAS Control
ifdef::build_target_nstd[]
CA functions operate in the following modes, based on the activation status of CAA, Route Tracking, and CAC:

* N (Monitoring mode):  
  All CA functions and Route Tracking are deactivated.

* CAA mode:  
  Only CAA is activated. The operator retains control, and a suggested path is displayed.  
  The path may update without operator intervention.  
  HiNAS follows the preplanned route, but the path is not followed.

* Route Tracking/CAA mode:  
  Route Tracking and CAA are active, CAC is not.  
  HiNAS follows the preplanned route while showing the safe path.  
  The suggested path does not influence vessel maneuvering.

* CAC mode:  
  All three—Route Tracking, CAA, and CAC—are active.  
  HiNAS follows the suggested safe path.  
  Any change to the path requires operator confirmation.

[NOTE]
====
CAA and Route Tracking operate independently.  
Even if a safe path is displayed, the vessel will not follow it unless CAC is active.  
CAC requires both CAA and Route Tracking to be active.
====
endif::build_target_nstd[]

// HiNAS Control Standard
ifdef::build_target_std[]
CA functions operate in the following modes, based on the activation status of CAA, TC, and CAC:

* N (Monitoring mode):  
  All CA functions and TC are deactivated.

* CAA mode:  
  Only CAA is active. The operator retains control, and a suggested path is displayed.  
  The path may update without operator intervention.  
  HiNAS follows the preplanned route, but the suggested path is not followed.

* TC/CAA mode:  
  TC and CAA are active, CAC is not.  
  HiNAS follows the preplanned route and shows the suggested safe path.  
  The path does not affect vessel maneuvering.

* CAC mode:  
  TC, CAA, and CAC are all active.  
  HiNAS follows the safe path after it is accepted by the operator.  
  Changes to the path require operator confirmation.

[NOTE]
====
CAA and TC operate independently.  
Even if a safe path is shown, the vessel will not follow it unless CAC is active.  
CAC can only be enabled when both CAA and TC are active.
====
endif::build_target_std[]