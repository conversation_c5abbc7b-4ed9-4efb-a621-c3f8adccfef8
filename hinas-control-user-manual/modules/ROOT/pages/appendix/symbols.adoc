=== Symbols
:reference: IEC62288:

==== Own Ship Symbols
[cols="1,6,3"]
|===
| |Symbol name and description |Symbol graphic(s)           

|1.1.a|
*Own ship - True scaled outline* + 
This symbol represents the true scaled outline of the own ship.
It is displayed when the beam of the outline is greater than 3 mm. 
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/own-ship-true-scaled-outline.png[width=150,align="center"]

|1.1.b|
*Own ship - Simplified symbol* 
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/own-ship-simplified-symbol.png[width=120,align="center"]

|1.1.c|
*Own ship - Minimised symbol* + 
The minimised symbol is comprised of the heading line (symbol 1.2) and the beam line (symbol 1.3). 
It is combined with true scaled outline (symbol 1.1.a) own ship symbol.
| image:appendix/symbols/own-ship-minimised-symbol.png[width=200]

// radar antenna position

|1.2|*Own ship heading line* + 
The heading line forms part of the own ship minimised symbol (symbol 1.1.c). 
The line originates at CCRP and extends in the direction of own ship heading. 
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/heading-line.png[width=150]

|1.3|
*Beam line* + 
The beam line forms part of the own ship minimised symbol (symbol 1.1.c). 
The line is perpendicular to the heading line, passing through the CCRP.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/beam-line.png[width=150]

|1.4.a|
*Velocity vector* + 
The velocity vector originates at the CCRP and extends in the direction of the COG.
| image:appendix/symbols/velocity-vector.png[width=230]

|1.4.b|
*Velocity vector - Stabilisation indicator* + 
The ground stabilisation indicator is presented as a double arrowhead.
| image:appendix/symbols/velocity-vector-stabilisation-indicator.png[width=230]

|1.5|
*Past track* + 
Past track is drawn using a solid line. 
| &nbsp;&nbsp;image:appendix/symbols/past-track.png[width=200]
|===



==== Target Symbols
[cols="1,6,3"]
|===
| |Symbol name and description |Symbol graphic(s)           

|2.1|
*Green target* + 
Indicates a target assessed as safe based on the risk assessment results.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/green-target.png[width=100]

|2.2|
*Yellow target* + 
Represents a target previously classified as high-risk (red) but determined to be safe when following a safe path. 
This is visible only when CAC function is activated.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/yellow-target.png[width=100]

|2.3|
*Red target* + 
Indicates a high-risk target that requires collision avoidance actions. 
When CAA is activated, it generates a safe path to avoid the red targets.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/red-target.png[width=100]

|2.4|
*Blinking target* + 
When CAA function is activated, red targets (symbol 2.3) blink to draw attention.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/blinking-target.png[width=170]

|2.5|
*Lost target* + 
When a previously detected target disappears, it is displayed with an "X" mark over the red target symbol.
The lost target blink to draw attention.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/lost-target.png[width=170]

|2.6|
*Selected target* + 
When the user clicks on a target, it is highlighted with a white square featuring L-shaped corners. 
Once a target is selected, its information including CPA, TCPA, target distance, target bearing, and SOG are displayed in a separate window.
| &nbsp;&nbsp;
image:appendix/symbols/selected-target-2.png[width=200]

|2.7|
*Target velocity vector* + 
The target velocity vector is displayed alongside the target symbol (symbols 2.1, 2.2, 2.3).
User can choose to show or hide the velocity vector in the display option.
The length of the velocity vector represents the distance the target is expected to travel within a specified time period, 
which can also be adjusted in the display option.
| &nbsp;&nbsp;
image:appendix/symbols/target-velocity-vector.png[width=200]
|===



==== Navigation Symbols
[cols="1,6,3"]
|===
| |Symbol name and description |Symbol graphic(s)           

|3.1.a|
*Waypoint*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/waypoint.png[width=120]

|3.1.b|
*Next waypoint*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/next-waypoint.png[width=120]

|3.1.c|
*Departure waypoint* + 
The symbol is displayed over the waypoint (symbol 3.1.a).
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/departure-waypoint.png[width=30]

|3.1.d|
*Arrival waypoint* + 
The symbol is displayed over the waypoint symbol (symbol 3.1.a). 
If the own ship is on the last leg, the symbol is displayed over the next waypoint symbol (symbol 3.1.b).
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/arrival-waypoint.png[width=30]

|3.2.a|
*Routes* + 
Leg lines on the monitored route are drawn as dashed lines. 
Bearing, planned RPM and speed may be indicated on each leg.
| &nbsp;&nbsp;&nbsp;
image:appendix/symbols/routes.png[width=200]

|3.2.b|
*Past leg* + 
Past legs are displayed as gray dashed line.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/past-leg.png[width=150]

|3.2.c|
*Current leg* + 
The leg currently being followed is displayed as a red dashed line.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/current-leg.png[width=150]

|3.2.d|
*Remaining leg* + 
Remaining legs are displayed as orange dashed line.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/remaining-leg.png[width=150]

|3.3|
*Wheel over line (WOL)* + 
WOL is displayed as a straight solid line.
It is displayed only for the next waypoint (symbol 3.1.b).
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/wheel_over_line_v1.png[width=170,align="center"]

// |3.4.a|
// *Suggested safe path* + 
// A recommended path not yet confirmed by the user.
// It is displayed in magenta.
// | &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// image:appendix/symbols/suggested-safe-path.png[width=100]

// |3.4.b|
// *Following safe path* + 
// Once the suggested safe path (symbol 3.4.a) is confirmed by user and being followed, it changes to the following safe path.
// It is displayed in orange.
// | &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// image:appendix/symbols/following-safe-path.png[width=100]

// |3.5|
// *Position deviation limit (PDL)* + 
// The PDL defines the boundary for HiNAS Control’s CAC function.
// To ensure safe collision avoidance, the own ship should remain within the PDL.
// | &nbsp;&nbsp;
// image:appendix/symbols/position-deviation-limit.png[width=200]
|===


==== Button Symbols
[cols="1,6,3"]
|===
| |Symbol name and description |Symbol graphic(s)           

|4.1|
*User cursor*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/user-cursor.png[width=120]

|4.2.a|
*Number of alarms*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/number-of-alarms.png[width=120]

|4.2.b|
*Number of warnings*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/number-of-warnings.png[width=120]

|4.2.c|
*Number of cautions*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/number-of-cautions.png[width=120]

|4.2.d|
*View alert list and history*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/view-alert-list-and-history.png[width=120]

|4.2.e|
*Silence alert*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/silence-alert.png[width=120]

|4.2.f|
*Acknowledge alert*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/acknowledge-alert.png[width=120]

|4.3|
*Brightness control* + 
Control brightness of screen, camera, and alert indicators.
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/brightness-control.png[width=120]

|4.4.a|
*Day mode*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/day-mode.png[width=120]

|4.4.b|
*Dusk mode*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/night-mode.png[width=120]

|4.5.a|
*Map view*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/map-view.png[width=120]

|4.5.b|
*Camera view*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/camera-view.png[width=120]

|4.5.c|
*Split view (Map and Camera)*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/split-view.png[width=120]

|4.6|
*Target filter*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/target-filter.png[width=120]

|4.7.a|
*Center map at own ship position*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/center-map.png[width=120]

|4.7.b|
*Fix own ship position at the map center*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/fix-own-ship.png[width=120]

|4.8|
*Map zoom*
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
image:appendix/symbols/map-zoom.png[width=170]
|===