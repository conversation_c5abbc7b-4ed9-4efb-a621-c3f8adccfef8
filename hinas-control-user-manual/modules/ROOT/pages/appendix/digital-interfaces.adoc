=== Digital Interfaces
:digital-interfaces!:

* *Input sentences*
** ACN, CUR, DBT, DTM, DPT, ETL, GGA, GLL, HBT, HDT, HTD, MWV, OSD, ROT, RPM
TLB, TTD, TTM, ROR, RSA, RSD, THS, TXT, VBW, VTG, VHW, VDM, VDO, VDR, ZDA

* *Output sentences*
** ALC, ALF, ALR, ARC, HBT, HDT, HSC, HTC, PAVKVDR, ROT, VBW, VTG, XTE


==== Data Transmission

Data is transmitted in serial asynchronous form in accordance with the standards referenced in IEC 61162-1.
The following parameters are used:

* Baud rate: 38400 bps (HTC, HTD). The baud rate of all other sentences is 4800 bps.
* Data bits: 8 (D7 = 0)
* Parity: none
* Stop bits: 1

:imagesdir: images
[#data-transmission-format]
.Data transmission format
image::appendix/digital-interfaces/data-transmission-format.png[width=600]

==== Input Sentences

** *ACN - Alert command*
----
$--ACN,hhmmss.ss,aaa,x.x,x.x,c,a*hh<CR><LF> 
           1      2   3   4  5 6
1. Release time (UTC) of alert command
2. Manufacturer mnemonic code
  - For standardized alerts, this should be a null field.
3. Alert identifier
4. Alert instance (1-999999)
5. Alert command (A, Q, O, S)
  A = Acknowledge
  Q = Request/repeat information
  O = Responsibility transfer
  S = Silence
6. Sentence status flag
  - This field should be "C" and should not be a null field. 
----

** *CUR - Water current layer – Multi-layer water current data*
----
$--CUR,A,x,x.x,x.x,x.x,a,x.x,x.x,x.x,a,a*hh<CR><LF>
       1 2  3   4   5  6  7   8   9 10 11
1. Validity of data (A=valid; V=invalid)
2. Data set number (0-9)
3. Layer number
4. Current depth in metres
5. Current direction in degrees
6. Direction reference in use, True/Relative T/R
7. Current speed in knots
8. Reference layer depth in metres
9. Heading in degrees
10. Heading reference in use, True/Magnetic T/M
11. Speed reference
  B = Bottom track
  W = Water track
  P = Positioning system
----

** *DBT - Depth below transducer*
----
$--DBT,x.x,f,x.x,M,x.x,F*hh<CR><LF>
        1  2  3  4  5  6
1. Water depth, feet
2. feet
3. Water depth, metres
4. Metres
5. Water depth, fathoms
6. Fathoms
----

** *DTM - Datum reference*
----
$--DTM,ccc,a,x.x,a,x.x,a,x.x,ccc*hh<CR><LF>
        1  2  3  4  5  6  7   8
1. Local datum
  WGS84 = W84
  WGS72 = W72
  SGS85 = S85
  PE90 = P90
  BDCS = C00
  User defined = 999
2. Local datum subdivision code
3. Lat offset, min
4. N/S
5. Lon offset, min
6. E/W
7. Altitude offset, m
8. Reference datum
  WGS84 = W84
  WGS72 = W72
  SGS85 = S85
  PE90 = P90
  BDCS = C00
----

** *DPT - Depth*
----
$--DPT,x.x,x.x,x.x*hh<CR><LF>
        1   2   3
1. Water depth relative to the transducer, in metres
2. Offset from transducer, in metres
3. Maximum range scale in use
----

** *ETL – Engine telegraph operation status*
----
$--ETL,hhmmss.ss,a,xx,xx,a,x*hh<CR><LF>
           1     2  3 4  5 6
1. Event time
2. Message type (O=Order; A=Answer-back)
3. Position indicator of engine telegraph
  00 = STOP ENGINE
  01 = [AH] DEAD SLOW
  02 = [AH] SLOW
  03 = [AH] HALF
  04 = [AH] FULL
  05 = [AH] NAV. FULL
  11 = [AS] DEAD SLOW
  12 = [AS] SLOW
  13 = [AS] HALF
  14 = [AS] FULL
  15 = [AS] CRASH ASTERN
4. Position indicator of sub-telegraph
  20 = S/B (Stand-by engine)
  30 = F/A (Full away – Navigation full)
  40 = F/E (Finish with engine)
5. Operating location indicator
  B = Bridge
  P = Port wing
  S = Starboard wing
  C = Engine control room
  E = Engine side/local
  W = Wing (port or starboard not specified)
6. Number of engine or propeller shaft
  0 = single or on centre-line
  Odd = starboard
  Even = port
----

** *GGA – Global positioning system (GPS) fix data*
----
$--GGA,hhmmss.ss,llll.ll,a,yyyyy.yy,a,x,xx,x.x,x.x,M,x.x,M,x.x,xxxx*hh<CR><LF>
           1          2    3     4    5 6  7  8   9  10 11 12 13  14
1. UTC of position
2. Latitude 
3. N/S
4. Longitude
5. E/W
6. GPS quality indicator
7. Number of satellites in use (00-12)
8. Horizontal dilution of precision
9. Antenna altitude above/below
10. Units of antenna altitude, m
11. Geoidal separation
12. Units of geoidal separation, m
13. Age of differential GPS data
14. Differential reference station ID (0000-1023)
----

** *GLL - Geographic position - Latitude/longitude*
----
$--GLL,llll.ll,a,yyyyy.yy,a,hhmmss.ss,A,a*hh<CR><LF>
          1    2     3    4      5    6 7
1. Latitude 
2. N/S
3. Longitude
4. E/W
5. UTC of position
6. Status (A=data valid; V=data invalid)
7. Mode indicator
  A = Autonomous
  D = Differential
  E = Estimated
  M = Manual Input
  S = Simulator
  N = Data not valid
----

** *HBT – Heartbeat supervision sentence*
----
$--HBT,x.x,A,x*hh<CR><LF>
        1  2 3
1. Configured repeat interval
2. Equipment status (A=normal; V=abnormal)
3. Sequential sentence identifier (0-9)
----

** *HDT - Heading true*
----
$--HDT,x.x,T*hh<CR><LF>
        1  2
1. Heading, degrees
2. True (T)
----

** *HTD – Heading /track control data*
----
$--HTD,A,x.x,a,a,a,x.x,x.x,x.x,x.x,x.x,x.x,x.x,a,A,A,A,x.x*hh<CR><LF>
       1  2  3 4 5  6   7   8   9   10 11  12 13 14 15 16 17
1. Override (A=in use; V=not in use)
2. Commanded rudder angle, degrees
3. Commanded rudder direction (L/R = port/starboard)
4. Selected steering mode
5. Turn mode
  R = Radius controlled
  T = Turn rate controlled
  N = Turn is not controlled
6. Commanded rudder limit, degrees (unsigned)
7. Commanded off-heading limit, degrees (unsigned)
8. Commanded radius of turn for heading changes, n.miles
9. Commanded rate of turn for heading changes, degrees/min
10. Commanded heading-to-steer, degrees
11. Commanded off-track limit. n.miles (unsigned)
12. Commanded track, degrees
13. Heading reference in use, T/M
14. Rudder status (A=within limits, V=limit reached or exceeded)
15. Off-heading status (A=within limits, V=limit reached or exceeded)
16. Off-track status (A=within limits, V=limit reached or exceeded)
17. Vessel heading, degrees
----

** *MWV - Wind speed and angle*
----
$--MWV,x.x,a,x.x,a,A*hh<CR><LF>
        1  2  3  4 5
1. Wind angle in degrees (0-359)
2. Reference
  R = Relative
  T = Theoretical
3. Wind speed
4. Wind speed Units
  K = km/h
  M = m/s
  N = knots
5. Status (A=data valid; V=data invalid)
----

** *OSD - Own ship data*
----
$--OSD,x.x,A,x.x,a,x.x,a,x.x,x.x,a*hh<CR><LF>
        1  2  3  4  5  6  7   8  9
1. Heading, degrees true
2. Heading status (A=data valid; V=data invalid)
3. Vessel course, degrees true
4. Course reference
  B = Bottom tracking log
  M = Manually entered
  W = Water referenced
  R = Radar tracking (or fixed target)
  P = Positioning system ground reference
5. Vessel speed
6. Speed reference
  B = Bottom tracking log
  M = Manually entered
  W = Water referenced
  R = Radar tracking (or fixed target)
  P = Positioning system ground reference
7. Vessel set, degrees true; Manually entered
8. Vessel drift (speed); Manually entered 
9. Speed units
  K = km/h
  N = knots
  S = statute miles/h
----


** *ROT - Rate of turn*
----
$--ROT,x.x,A*hh<CR><LF>
        1  2
1. Rate of turn, deg/min, "-"=bow turns to port
2. Status (A=data valid; V=data invalid)
----

** *RPM - Revolutions*
----
$--RPM,a,x,x.x,x.x,A*hh<CR><LF>
       1 2  3   4  5
1. Source (S=shaft; E=engine)
2. Engine or shaft number, numbered from centreline
  Odd = starboard
  Even = port
  0 = single or on centre-line
3. Speed, revolutions/min, "-"=counter-clockwise
4. Propeller pitch, % of maximum, "-"=astern
5. Status (A=data valid; V=data invalid)
----

** *TLB – Target label*
----
$--TLB,x.x,c--c,x.x,c--c,...x.x,c--c*hh<CR><LF>
        1   2    3   4     5     6
1. Target number 'n' reported by the device
2. Label assigned to target 'n'
3-6. Additional label pairs
----

** *TTD – Tracked target data*
----
!--TTD,hh,hh,x,s—s,x*hh<CR><LF>
       1  2  3  4  5
1. Total hex number of sentences needed to transfer the message (01-FF)
2. Hex sentence number (01-FF)
3. Sequential message identifier (0-9)
4. Encapsulated tracked target data
5. Number of fill-bits (0-5)
----

** *TTM – Tracked target message*
----
$--TTM,x.x,x.x,x.x,a,x.x,x.x,a,x.x,x.x,a,c--c,a,a,hhmmss.ss,a*hh<CR><LF>
        1   2   3  4  5   6  7  8   9 10  11 12 13    14    15
1. Target number (0-999)
2. Target distance from own ship
3. Bearing from own ship, degrees true/relative (T/R)
4. True or Relative
5. Target speed
6. Target course, degrees true/relative (T/R)
7. True or Relative
8. Distance of closest-point-of-approach
9. Time to CPA, min., "-"=increasing
10. Speed/distance units, K/N/S
11. Target name
12. Target status
  L = Lost, tracked target has been lost
  Q = Query, target in the process of aquisition
  T = tracking
13. Reference target=R, null field otherwise
14. Time of data (UTC)
15. Type of acquisition
  A = Automatic
  M = Manual
  R = Reported
----

** *ROR – Rudder order status*
----
$--ROR,x.x,A,x.x,A,a*hh<CR><LF>
        1  2  3  4 5
1. Starboard (or single) rudder order
2. Status (A=data valid; V=data invalid)
3. Port rudder order
4. Status (A=data valid; V=data invalid)
5. Operating location indicator (as TRC)

$--ROR,x.x,A,x.x,A,a,x.x,A,x.x,A*hh<CR><LF>
        1  2  3  4 5  6  7  8  9
1. Starboard (or single) rudder order
2. Status (A=data valid; V=data invalid)
3. Port rudder order
4. Status (A=data valid; V=data invalid)
5. Operating location indicator (as defined in TRC)
6. Center rudder order
7. Status (A=data valid; V=data invalid)
8. Bow or other rudder order
9. Status (A=data valid; V=data invalid)
----

** *RSA - Rudder sensor angle*
----
$--RSA,x.x,A,x.x,A*hh<CR><LF>
        1  2  3  4
1. Starboard (or single) rudder sensor
  - Relative measurement of rudder angle without units
2. Status (A=data valid)
3. Port rudder sensor
  - Relative measurement of rudder angle without units
4. Status (A=data valid)

If rudder sensor field is not null field, 
the corresponding status field should not be a null field.
----

** *RSD - Radar system data*
----
$--RSD,x.x,x.x,x.x,x.x,x.x,x.x,x.x,x.x,x.x,x.x,x.x,a,a*hh<CR><LF>
        1   2   3   4   5   6   7   8   9  10  11 12 13
1. Origin 1 range, from own ship
2. Origin 1 bearing, degrees from 0
3. Variable range marker 1 (VRM1), range
4. Bearing line 1 (EBL1), degrees from 0
5. Origin 2 range
6. Origin 2 bearing
7. VRM 2, range
8. EBL 2, degrees
9. Cursor range, from own ship
10. Cursor bearing, degrees clockwise from 0
11. Range scale in use
12. Range units
  K = km
  N = nautical miles
  S = statute miles
13. Display rotation
----

** *THS - True heading and status*
----
$--THS,x.x,a*hh<CR><LF>
        1  2
1. Heading, degrees true
2. Mode indicator
  A = Autonomous
  E = Estimated
  M = Manual Input
  S = Simulator mode
  V = Data not valid (including standby)
----

** *TXT – Text transmission*
----
$--TXT,xx,xx,xx,c--c*hh<CR><LF>
       1  2  3   4
1. Total number of sentences (01-99)
2. Sentence number (01-99)
3. Text identifier
4. Text message
----

** *VBW - Dual ground/water speed*
----
$--VBW,x.x,x.x,A,x.x,x.x,A,x.x,A,x.x,A*hh<CR><LF>
        1   2  3  4   5  6  7  8  9  10
1. Longitudinal water speed, knots, "-"=astern
2. Transverse water speed, knots, "-"=port
3. Status: water speed (A=data valid; V=data invalid)
4. Longitudinal ground speed, knots, "-"=astern
5. Transverse ground speed, knots, "-"=port
6. Status: ground speed (A=data valid; V=data invalid)
7. Stern transverse water speed, knots
8. Status: stern water speed (A=data valid; V=data invalid)
9. Stern transverse ground speed, knots
10. Status: stern ground speed (A=data valid; V=data invalid)
- The status fields should not be a null field.
----

** *VTG – Course over ground and ground speed*
----
$--VTG,x.x,T,x.x,M,x.x,N,x.x,K,a*hh<CR><LF>
        1  2  3  4  5  6  7  8 9
1. Course over ground, degrees true
2. True
3. Course over ground, degrees magnetic
4. Magnetic
5. Speed over ground, knots
6. knots
7. Speed over ground, km/h
8. km/h
9. Mode indicator
----

** *VHW - Water speed and heading*
----
$--VHW,x.x,T,x.x,M,x.x,N,x.x,K*hh<CR><LF>
        1  2  3  4  5  6  7  8
1. Heading, degrees true
2. True
3. Heading, degrees magnetic
4. Magnetic
5. Speed, knots
6. knots
7. Speed, km/h
8. km/h
----

** *VDM - AIS VHF data-link message*
----
!--VDM,x,x,x,a,s--s,x*hh<CR><LF>
       1 2 3 4  5   6
1. Total number of sentences needed to transfer the message (1-9)
2. Sentence number (1-9)
3. Sequential message identifier (0-9)
4. AIS channel (A or B)
5. Encapsulated ITU-R M.1371 radio message
6. Number of fill-bits (0-5)
----

** *VDO - AIS VHF data-link own-vessel report*
----
!--VDO,x,x,x,a,s--s,x*hh<CR><LF>
       1 2 3 4  5   6
1. Total number of sentences needed to transfer the message (1-9)
2. Sentence number (1-9)
3. Sequential message identifier (0-9)
4. AIS channel (A or B)
5. Encapsulated ITU-R M.1371 radio message
6. Number of fill-bits (0-5)
----

** *VDR - Set and drift*
----
$--VDR,x.x,T,x.x,M,x.x,N*hh<CR><LF>
        1  2  3  4  5  6
1. Direction, degrees true
2. True
3. Direction, degrees magnetic
4. Magnetic
5. Current speed, knots
6. knots
----

** *ZDA - Time and date*
----
$--ZDA,hhmmss.ss,xx,xx,xxxx,xx,xx*hh<CR><LF>
           1     2  3   4   5  6
1. UTC
2. Day (01-31; UTC)
3. Month (01-12; UTC)
4. Year (UTC)
5. Local zone hours
6. Local zone minutes
----


==== Output Sentences

** *ALC – Cyclic alert list*
----
$--ALC,xx,xx,xx,x.x,aaa,x.x,x.x,x.x,……,aaa,x.x,x.x,x.x*hh <CR><LF>
        1  2  3  4   5   6   7   8    9   10  11  12  13
1. Total number of sentences for this message (01-99)
2. Sentence number (01-99)
3. Sequential message identifier (00-99)
4. Number of alert entries
5-8. Alert entry 1
  5. Manufacturer's mnemonic code
  6. Alert identifier
  7. Alert instance (0-999999)
  8. Revision counter (1-99)
9. Additional alert entries
10-13. Alert entry n

Each alert entry consists of four fields:
  - Manufacturer's mnemonic code
  - Alert identifier
  - Alert instance (0-999999)
  - Revision counter (1-99)
----

** *ALF - Alert sentence*
----
$--ALF,x,x,x,hhmmss.ss,a,a,a,aaa,x.x,x.x,x.x,x,c--c*hh <CR><LF>
       1 2 3     4     5 6 7  8   9  10  11  12 13
1. Total number of ALF sentences for this message (1, 2)
2. Sentence number (1, 2)
3. Sequential message identifier (0-9)
4. Time of last change
5. Alert category (A, B or C)
6. Alert priority (E, A, W or C)
  E = Emergency alarm
  A = Alarm
  W = Warning
  C = Caution
7. Alert state (A, S, N, O, U or V)
  A = active-acknowledged or active
  S = active-silenced
  N = normal
  O = active-responsibility transferred
  U = rectified-unacknowledged
  V = active-unacknowledged
8. Manufacturer's mnemonic code
9. Alert identifier
10. Alert instance (0-999999)
11. Revision counter (1-99)
12. Escalation counter (0-9)
13. Alert text
----

** *ALR - Set alarm state*
----
$--ALR,hhmmss.ss,xxx,A,A,c--c*hh<CR><LF>
           1      2  3 4  5
1. Time of alarm condition change, UTC
2. Unique alarm number (identifier) at alarm source
3. Alarm condition (A=threshold exceeded; V=not exceeded)
4. Alarm's acknowledge state (A=acknowledged; V=unacknowledged)
5. Alarm's description text
----

** *ARC – Alert command refused*
----
$--ARC,hhmmss.ss,aaa,x.x,x.x,c*hh <CR><LF>
           1      2   3   4  5
1. Release time of the alert command refused
2. Manufacturer's mnemonic code
3. Alert identifier
4. Alert instance (1-999999)
5. Refused alert command
  A = Acknowledge
  Q = Request / repeat information
  O = Responsibility transfer
  S = Temporary silence
----

** *HBT – Heartbeat supervision sentence*
----
$--HBT,x.x,A,x*hh<CR><LF>
        1  2 3
1. Configured repeat interval
2. Equipment status (A=normal; V=abnormal)
3. Sequential sentence identifier (0-9)
----

** *HDT - Heading true*
----
$--HDT,x.x,T*hh<CR><LF>
        1  2
1. Heading, degrees
2. True (T)
----

** *HSC – Heading steering command*
----
$--HSC,x.x,T,x.x,M,a*hh<CR><LF>
        1  2  3  4 5
1. Commanded heading, degrees true
2. True
3. Commanded heading, degrees magnetic
4. Magnetic
5. Sentence status flag
  R = Sentence is a status report of current setting.
  C = Sentence is a configuration command to change settings.
----

** *HTC – Heading/track control command*
----
$--HTC,A,x.x,a,a,a,x.x,x.x,x.x,x.x,x.x,x.x,x.x,a,a*hh<CR><LF>
       1  2  3 4 5  6   7   8   9   10 11  12 13 14
1. Override (A=in use; V=not in use)
2. Commanded rudder angle, degrees
3. Commanded rudder direction (L/R = port/starboard)
4. Selected steering mode
5. Turn mode
  R = Radius controlled
  T = Turn rate controlled
  N = Turn is not controlled
6. Commanded rudder limit, degrees (unsigned)
7. Commanded off-heading limit, degrees (unsigned)
8. Commanded radius of turn for heading changes, n.miles
9. Commanded rate of turn for heading changes, degrees/min
10. Commanded heading-to-steer, degrees
11. Commanded off-track limit. n.miles (unsigned)
12. Commanded track, degrees
13. Heading reference in use, T/M
14. Sentence status
----

** *PAVKVDR - VDR logging*
----
$PAVKVDR,A,A,A,A,xxx.x,xxx.x*hh<CR><LF>
         1 2 3 4   5     6
1. Control authority 
  A=HiNAS gained control authority
  V=No control authority on HiNAS
2. Track Control status (A=on; V=0ff)
3. CA Control status (A=on; V=0ff)
4. CA Assistance status (A=on; V=0ff)
5. Heading command generated by HiNAS Control [deg]
6. RPM command generated by HiNAS Control [RPM]
----


** *ROT - Rate of turn*
----
$--ROT,x.x,A*hh<CR><LF>
        1  2
1. Rate of turn, deg/min, "-"=bow turns to port
2. Status (A=data valid; V=data invalid)
----

** *VBW - Dual ground/water speed*
----
$--VBW,x.x,x.x,A,x.x,x.x,A,x.x,A,x.x,A*hh<CR><LF>
        1   2  3  4   5  6  7  8  9  10
1. Longitudinal water speed, knots, "-"=astern
2. Transverse water speed, knots, "-"=port
3. Status: water speed (A=data valid; V=data invalid)
4. Longitudinal ground speed, knots, "-"=astern
5. Transverse ground speed, knots, "-"=port
6. Status: ground speed (A=data valid; V=data invalid)
7. Stern transverse water speed, knots
8. Status: stern water speed (A=data valid; V=data invalid)
9. Stern transverse ground speed, knots
10. Status: stern ground speed (A=data valid; V=data invalid)
- The status fields should not be a null field.
----

** *VTG – Course over ground and ground speed*
----
$--VTG,x.x,T,x.x,M,x.x,N,x.x,K,a*hh<CR><LF>
        1  2  3  4  5  6  7  8 9
1. Course over ground, degrees true
2. True
3. Course over ground, degrees magnetic
4. Magnetic
5. Speed over ground, knots
6. knots
7. Speed over ground, km/h
8. km/h
9. Mode indicator
----

** *XTE – Cross-track error, measured*
----
$--XTE,A,A,x.x,a,N,a*hh<CR><LF>
       1 2  3  4 5 6
1. XTE warning flag (A=within XTD; V=off-track)
2. Status (A=data valid; V=Loran-C cycle lock warning flag)
3. Magnitude of cross-track error
4. Direction to steer, L/R
5. Units, nautical miles
6. Mode indicator
  A = Autonomous mode
  D = Differential mode
  E = Estimated (dead reckoning) mode
  M = Manual input mode
  S = Simulator mode
  N = Data not valid
----

// == Serial Interface
// MOXA (61162 인증서로 대체)