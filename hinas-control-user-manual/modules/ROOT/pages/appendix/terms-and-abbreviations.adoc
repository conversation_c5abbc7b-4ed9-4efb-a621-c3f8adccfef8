=== Terms and Abbreviations


==== Active safe path
A safe path that has been accepted by the user to be applied for vessel control.

==== Activate / Activation
// HiNAS Control Standard
ifdef::build_target_std[]
Activation indicates that the CA function or TC function is operational and provides the designated service to the user, essentially turning on the function.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
Activation indicates that the CA function or Route Tracking function is operational and provides the designated service to the user, essentially turning on the function.
endif::build_target_nstd[]

==== Automatic identification system (AIS)
Automatic identification system.

==== Association check parameter (ACP)
Association check parameter.

==== Association view (AV)
Association View.

==== Bridge maneuvering system (BMS)
Bridge maneuvering system.

==== Camera based detector (CBD)
Camera based Detector.

==== Collision avoidance assistance (CA assistance, CAA)
A function in HiNAS that provides a safe path to the user in situations where there is a high risk of collision.

==== Collision avoidance control (CA control, CAC)
A function in HiNAS that controls the vessel in accordance with the safe path provided by CAA.

==== Collision avoidance function (CA function)
The HiNAS function designed for collision avoidance, which includes CAA, CAC.

==== CCRP
Consistent Common Reference Point

==== Cross-track distance (XTD)
The lateral distance between the vessel's current position and the planned track. XTD is used to measure how far the vessel has deviated from its intended course.

==== Cross-track limit (XTL)
The maximum allowable deviation (lateral distance) from the planned track before the vessel is off course. XTL defines the boundary within which the vessel must remain to avoid collision risks.

==== Deactivate / Deactivation
// HiNAS Control Standard
ifdef::build_target_std[]
Deactivation means the CA function or TC function stops working and no longer provides its service to the user, essentially turning off the function.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
Deactivation means the CA function or Route Tracking function stops working and no longer provides its service to the user, essentially turning off the function.
endif::build_target_nstd[]

==== Disable
A disabled function means the function cannot be initiated.

==== Distance (DIST)
Distance.

==== Distance to Wheel over line (DWOL)
Distance to Wheel over line.

==== Enable
An enabled function means the function is capable of being initiated.

==== Electronic chart systems (ENC)
Electronic chart systems.

==== Estimated time of arrival (ETA)
Estimated time of arrival.

==== Fuel oil consumption (FOC)
Feul oil consumption [ton/hour].

==== Length between perpendicular (LBP)
Length between perpendicular.

==== Operational design domain (ODD)
The Operational Design Domain of a function refers to a defined set of conditions under which the function is designed to operate, including all tolerable events.

==== N mode
// HiNAS Control Standard
ifdef::build_target_std[]
Mode of HiNAS Control without any of the CA/TC function is activated.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
Mode of HiNAS Control without any of the CA/Route Tracking function is activated.
endif::build_target_nstd[]

==== Near miss
A target that was previously detected outside the safety limit but, for any reason, has moved closer than the safety limit.

==== Near target
A target that is placed inside the safety limit from the own ship.

==== Next CRS (NCRS)
Next course.

==== Next RPM (NRPM)
Next RPM.

==== Next WPT (NWPT)
Next WPT.

==== Object detection and target classification (ODTC)
Object detection and target classification.

==== Operational envelope (OE)
The Operational Envelope of a ship refers to a defined set of conditions that outline the function's operational capabilities and limitations.

==== Position deviation limit (PDL)
The maximum allowable deviation from the planned position at a certain point of time from the safe path and safe path schedule. PDL defines the circular boundary area within which the vessel must remain to avoid collision risks at a certain point of time.

==== Preplanned route
// HiNAS Control Standard
ifdef::build_target_std[]
A route the user has given to the CA and TC function. None of CA or TC function can modify preplanned route.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
A route the user has given to the CA and Route Tracking function. None of CA or Route Tracking function can modify preplanned route.
endif::build_target_nstd[]

==== Primary source (PS)
Primary source.

==== Risk assessement (RA)
Risk assessement.

==== Safety distance
Represents the minimum safe distance that the user intends to maintain from the target. Safety distance is recommended to be larger than 0.5 NM.

==== Safety limit
Represents a safe area that the user intends to keep clear of any targets. The safety limit may be defined as a circular area with its radius representing the safety distance.

==== Sensor fusion (SF)
Target associator of HiNAS.

==== Expected safety limit (ESL)
Represents the safety limit based on the planned position at a specific point in time, as outlined in the safe path and safe path schedule.

==== Safe path
A guidance path computed by the CAA to help avoid high collision risk situations.

==== Safe path generation (SPG)
Safe path generation.

==== Safe path schedule
A guidance schedule for the safe path that corresponds to the time domain of the safe path. The safe path cannot ensure collision avoidance without an accompanying safe path schedule.

==== To waypint (To WPT)
To-waypoint.

// HiNAS Control Standard
ifdef::build_target_std[]
==== Track control (TC)
Track control.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
==== Route Tracking
Route Tracking function.
endif::build_target_nstd[]

==== Target Track (TT)
Target Track.

==== Time to Wheel over line (TWOL)
Time to Wheel over line.

==== User / HiNAS user / Operator
Navigators (or operators) using HiNAS system.

==== UPS (Uninterruptible power supply)
Uninterruptible power supply.

// new page
<<<