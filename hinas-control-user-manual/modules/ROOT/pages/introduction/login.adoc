// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images 

=== Login

After powering on HiNAS and completing system initialization, the following login page appears on the monitor.
The provided ID and password are required to access HiNAS.
Clicking the Login button navigates to the Apps page.

[[fig_login_page]]
.Login page
image::introduction/login_page_v0.png[width=600,align="center"]

If login is successful, the Apps page is displayed.
This page provides a button to navigate to the HiNAS Control page, where most HiNAS functions are available.
Clicking the Logout button returns the user to the login page.

[[fig_apps_page]]
.Apps page
image::introduction/apps_page_v0.png[width=600,align="center"]