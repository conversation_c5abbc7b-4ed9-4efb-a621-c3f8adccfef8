// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

[[ssec_sys_config_type]]
=== System Configuration

==== Two Client Servers Version

[#img-drw-two-clients]
.Connection diagram (two client servers version).
image::system-configuration/sys_conf_two_clients_v4.png[width=600,align="center"]
NOTE: This configuration is standard, actual configuration may vary with the ship’s condition.

<<<

==== A Single Client Server Version

[#img-drw-single-clients]
.Connection diagram (single client server version).
image::system-configuration/sys_conf_single_client_v4.png[width=600,align="center"]
NOTE: This configuration is standard, actual configuration may vary with the ship’s condition.

<<<

==== Zero Client Server Version
[#img-drw-zero-clients]
.Connection diagram (zero client servers version).
image::system-configuration/sys_conf_zero_client_v4.png[width=600,align="center"]
NOTE: This configuration is standard, actual configuration may vary with the ship’s condition.

<<<
    
==== Monitors Available With HiNAS
The following monitoring options are available with HiNAS:

[cols="2,3,2", options="header"]
|===
|Maker       |Model               |Viewing distance

|Tetradyne   |TIMX-UW584^*1^      |2.0 m
|            |TIMX-W320^*1^       |1.3 m
|            |TIMX-W270^*2^^*3^   |1.3 m
|===

*1: Standard supply for two client PC version

*2: Standard supply for single client PC version

*3: Standard supply for zero client PC version


==== ECDISs Available with HiNAS
Any ECDIS that support exchange of route on RTZ v.1.0 or v.1.2 format by use of RRT sentences.


==== Autopilots Available with HiNAS
The following Autopilots are available with HiNAS:
|===
|Maker       |Model       

|Tokyo-Keiki |PR-9000
|YDK         |PT900
|===


==== BMSs Available with HiNAS
The following BMSs are available with HiNAS:
|===
|Maker       |Model       

|Kongsberg   |Auto Cheif 600
|Nabtesco    |M-800-V/VII
|===

NOTE: This interface is an optional interface.