// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== BMS Operation

The following subsections describe the procedure for activating the interfaced BMS required for speed control using the Route Tracking function.  
HiNAS can interface with the following models:

* NABTESCO M-800-V/VII  
* KONGSBERG AutoChief 600  


==== NABTESCO M-800-V/VII

The following steps describe how to activate AUTO MODE for NABTESCO M-800-V/VII when interfaced with HiNAS:

* Ensure that the control mode in HiNAS is set to HS or S mode.  
* Confirm that the Auto Mode Ready condition is met, as shown in <<fig-display-panel-nabtestco>>.  
* After toggling on the Route Tracking switch in HiNAS, its state changes to Requested, and the [AUTO MODE] button on the M-800-V/VII's bridge telegraph display panel begins blinking along with an audible alert.  
* Press the [AUTO MODE] button to enable HiNAS to take control of the BMS.  
* Verify that the Route Tracking toggle switch changes its state to Active.

[[fig-display-panel-nabtestco]]
.Bridge telegraph display panel (NABTESCO M-800-V/VII)
image::tcs-operation/nabtesco_bms_panel_v2.png[width=500, align="center"]

The diagram below illustrates the control flow for NABTESCO M-800-V/VII as provided by the manufacturer:

* Transition from B/R Handle Mode to AUTO MODE 
* Transition from AUTO MODE back to B/R Handle Mode

[[fig-bms-control-flow-nabtestco]]
.Control flow for HiNAS (NABTESCO M-800-V/VII)
image::tcs-operation/nabtesco_bms_handle_2_auto_mode.png[width=500, align="center"]

[NOTE]
====
* In AUTO MODE, the M-800-V/VII allows HiNAS to take control of the BMS.  
* In B/R Handle Mode, control remains with the M-800-V/VII and HiNAS cannot control the BMS.
====


==== KONGSBERG AutoChief 600

The following steps describe how to activate HiNAS control for the AutoChief 600 when interfaced with HiNAS:

* Ensure that the control mode in HiNAS is set to HS or S mode.  
* Confirm that the BMS status conditions are met — all status indicators should be green, as shown in <<fig-bms-kongsberg-display>>.
* Verify that both `HiNAS Connected` and `HiNAS System Ready` statuses are Ready.
* After toggling On Route Tracking toggle switch in HiNAS, the switch state changes to Requested, and the `HiNAS TCS mode on` condition is Ready.  
* Press the [Activate HiNAS] button to allow HiNAS to take control of the BMS.  
* Check that Route Tracking toggle switch changes to the Active state.
* The telegraph lever will begin moving according to the commands issued by HiNAS.

[[fig-bms-kongsberg-display]]
.Control display of BMS (KONGSBERG AutoChief 600)
image::tcs-operation/kongsberg_bms_display.png[width=500, align="center"]