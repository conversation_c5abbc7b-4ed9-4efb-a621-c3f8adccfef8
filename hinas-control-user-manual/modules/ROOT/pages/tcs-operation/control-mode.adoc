// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Control Mode

HiNAS manages its control functions through the autopilot for heading control and the BMS for speed control.  
To accommodate different operational preferences and requirements from users or ship owners, HiNAS Control supports three control modes (S mode is an optional feature):

[[table_concept_of_control_mode]]
[cols="^1,^2,3", options="header"]
|===
| No.
| Control Mode
| Description 

| 1
| HS Mode (Default)
| controls both heading and speed using the autopilot and BMS.

| 2
| H Mode
| controls only the heading using the autopilot.  

| 3
| S Mode (Optional)
| controls only the speed using the BMS.  
|===

[NOTE]
====
S Mode is an optional feature and may not be available in all system configurations.
====


// ==== Mode Selection

// [IMPORTANT]
// ====
// Using the Track Control functionality, the user must specify one of the following modes: **HS Mode** or **H Mode**.
// ====

// The following image shows the HiNAS-Control UI for the Track Control system:

// [[fig-track-control-on]]
// .HiNAS screen
// image::tcs-operation/track_control_on_v1.png[HiNAS-Control UI, width=900, align=center]

// Shifting between two modes (*H* / *HS*) can be found in <<Track Control Settings>>.
// On the left middle section **Function Act Tap** of the UI, click the **Mode** setting area:

// image::tcs-operation/set_mode.png[Mode Selection, width=900, align=center]

// When changing the mode, a confirmation dialog will appear to ensure the selection:

// image::tcs-operation/set_mode_H_to_HS.png[Mode change (H to HS), width=900, align=center]

// image::tcs-operation/set_mode_HS_to_H.png[Mode change (HS to H), width=900, align=center]