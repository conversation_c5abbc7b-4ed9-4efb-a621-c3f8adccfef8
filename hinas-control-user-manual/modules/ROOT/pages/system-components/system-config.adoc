// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images
// view option //
// :build_target_nstd:

=== System Configuration

HiNAS control includes the components shown in the illustrations on the <<ssec_sys_config_type>> page.

ifdef::build_target_std[]
// HiNAS Control Standard
ifdef::build_target_std[]
* Server: Servers are installed in the bridge console and the electrical equipment locker. These server units are the core of the HiNAS system and are responsible for TCS and CA.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
* Server: Servers are installed in the bridge console and the electrical equipment locker. These server units are the core of the HiNAS system and are responsible for Route Tracking and CA.
endif::build_target_nstd[]
endif::build_target_std[]

ifdef::build_target_nstd[]
* Server: Servers are installed in the bridge console and the electrical equipment locker. These server units are the core of the HiNAS system and are responsible for Route Tracking and CA.
endif::build_target_nstd[]

* Monitor: Monitors are installed on the bridge and are responsible for displaying information and providing user interfaces.

* Control Interface Unit (keyboard with trackball / selector switch / switch dimmer): Control interface units, which enable interaction between navigators and HiNAS Control, are strategically located near the operational position.

* Camera unit: The cameras unit is equipped with three EO cameras and three IR cameras, and provides a panoramic view of the heading direction. 