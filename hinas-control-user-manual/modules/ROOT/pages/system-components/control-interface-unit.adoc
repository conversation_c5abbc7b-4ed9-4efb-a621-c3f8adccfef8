// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images 

=== Control Interface Unit

Control interface units, enabling interaction between navigators and HiNAS Control,
are strategically located near the operational position. 

==== Keyboard with trackball
This 85 key backlit keyboard features a compact keyboard with integrated waterproof 38 mm ergonomic trackball with scroll wheel.
The rubber keys are proven for efficient cleaning, reduced disinfection time and protection against cross contamination.
The backlit keys warrant for optimized performance while working in low-light environments. 

[[img_Keyboard_and_trackball]]
.Keyboard with trackball.
image::system-components/keyboard_and_trackball_v1.png[width=400,align="center"]

==== Dimmer
* When using the HiNAS Control system, brightness of the monitors should be adjusted according to the operating conditions.
* The brightness of the monitors can be adjusted using the dimmer switch.
* This switch is located on the display unit or near the operator's position.

===== Remote Dimmer Switch (for TIMX-UW584)
Remote Dimmer Switch for TIMX-UW584 provides the status of the monitor, power on/off control, and dimming functionality.

|===
|Control            |Description                  

|Power              |turns the monitor on or off.
|Status LED         |shows the current state of the monitor.
|Dimming -/+        |adjusts the brightness. Press the "-" button to decrease brightness or the "+" button to increase brightness.
|===

[[fig_remote_dimmer_switch]]
.Remote Dimmer Switch
image::introduction/remote-dimmer-switch-v0.png[width=400,align="center"]


===== Dimmer Switch (TIMX-W270 and TIMX-W320)
TIMX-W270 and TIMX-W320 provide power on/off control and dimming functionality

|===
|Control            |Description                  

|Power              |turns the monitor on or off.
|Dimming -/+        |adjusts the brightness. Press the "-" button to decrease brightness or the "+" button to increase brightness.
|===

[[fig_w270_dimmer_switch]]
.Dimmer switch (TIMX-W270 and TIMX-W320)
image::introduction/w270-dimmer-switch-v1.png[width=400,align="center"]


==== Selector switch / switch dimmer

ifdef::build_target_std[]
// HiNAS Control Standard
ifdef::build_target_std[]
A selector switch is used to gain or return control authority of the TCS and CA.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
A selector switch is used to gain or return control authority of the Route Tracking and CA.
endif::build_target_nstd[]
The switch dimmer for the selector switch provides dimming functionality.
endif::build_target_std[]

ifdef::build_target_nstd[]
A selector switch is used to gain or return control authority of the Route Tracking and CA.
The switch dimmer for the selector switch provides dimming functionality.
endif::build_target_nstd[]

[[fig_layout_selector_switch]]
.Layout - A selector switch /switch dimmer (Unit: mm).
image::system-components/selector_switch_v1.png[width=400,align="center"]

ifdef::build_target_std[]
|===
|Control          |Description                  

// HiNAS Control Standard
ifdef::build_target_std[]
|NAS              |turns the HiNAS TCS/CA On.
|ECDIS            |turns the HiNAS TCS/CA Off.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
|NAS              |turns the HiNAS Route Tracking/CA On.
|ECDIS            |turns the HiNAS Route Tracking/CA Off.
endif::build_target_nstd[]
|===
endif::build_target_std[]

ifdef::build_target_nstd[]
|===
|Control          |Description                  

|NAS              |turns the HiNAS Route Tracking/CA On.
|ECDIS            |turns the HiNAS Route Tracking/CA Off.
|===
endif::build_target_nstd[]

<<<