= HiNAS ConOps FDS ({{tag_name}})
:doctype: book
:sectnums:
:toc: left
:icons: font
:front-cover-image: ./images/book-cover.png
:page-background-image: ./images/page-background.png
:pdf-theme: book-theme.yml
:math:
:imagesoutdir: generated_images
:imagesdir: images
:stem: latexmath
// :xrefstyle: full
:xrefstyle: short
:build_target_nstd:

include::pages/revision_history.adoc[]

== INTRODUCTION
include::pages/INTRODUCTION/references.adoc[]

include::pages/INTRODUCTION/abbreviation.adoc[]

include::pages/INTRODUCTION/intro.adoc[]


== CONCEPT OF OPERATIONS
include::pages/CONCEPT_OF_OPERATION/introduction.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_start_failure_conditions.adoc[]

include::pages/CONCEPT_OF_OPERATION/role_and_responsibility_of_operator.adoc[]

include::pages/CONCEPT_OF_OPERATION/system_operation_mode.adoc[]

include::pages/CONCEPT_OF_OPERATION/function_flow_of_hinas_control.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_monitoring.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_recognition.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_sensor_fusion.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_target_alert.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_camera_only_detected_target_alert.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_voyage_planning.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_situational_awareness.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_collision_avoidance.adoc[]

include::pages/CONCEPT_OF_OPERATION/colregs_compliance.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_health_monitoring_of_hinas_control.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_control_mode.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_autopilot_control.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_bms_control.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_requirement_of_maintenance.adoc[]

include::pages/CONCEPT_OF_OPERATION/concept_of_software_update_plan.adoc[]

== FUNCTIONAL INTEGRATION
include::pages/FUNCTIONAL_INTEGRATION/intro.adoc[]

include::pages/FUNCTIONAL_INTEGRATION/requirement_of_interfaced_devices.adoc[]


== ALERT SYSTEM OF HINAS CONTROL
include::pages/ALERT_SYSTEM_OF_HINAS_CONTROL/intro.adoc[]

// include::pages/ALERT_SYSTEM_OF_HINAS_CONTROL/handling_of_backup-navigation_alarm.adoc[]

include::pages/ALERT_SYSTEM_OF_HINAS_CONTROL/alert_method_of_hinas_control.adoc[]

== FUNCTION DESCRIPTION
include::pages/FUNCTIONAL_DESCRIPTION/intro.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/applied_technologies.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/sensor_fusion.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/image_stitch.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/vision_based_object_detection.adoc[]
// include::pages/FUNCTIONAL_DESCRIPTION/collision_avoidance.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/menu_visual_structure.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/menu_overview.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/header_main.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/voyage_planning.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/route_info.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/function_activation.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/tcs_mode.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/ca_related_mode.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/ship_status_and_env.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/map_display_and_func.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/cam_display_related_func.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/cursor_reading_and_presentation.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/setting.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/setting_track_control.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/setting_collision_avoidance.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/setting_target.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/setting_alert.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/setting_display.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/su_setting_main.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/su_setting_ship_spec.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/su_setting_ship_dyn.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/su_setting_adv_tcs.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/su_setting_adv_alert.adoc[]
include::pages/FUNCTIONAL_DESCRIPTION/setting_ccrp.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/alert.adoc[]

include::pages/FUNCTIONAL_DESCRIPTION/user_category.adoc[]


== SYSTEM
include::pages/SYSTEM/intro.adoc[]

include::pages/SYSTEM/data_input_list.adoc[]
include::pages/SYSTEM/data_output_list.adoc[]
include::pages/SYSTEM/hardware_and_software_spec.adoc[]
include::pages/SYSTEM/system_configuration.adoc[]

// ABS only
include::pages/SYSTEM/risk_managements_and_plan.adoc[]

include::pages/SYSTEM/network_config_and_cs.adoc[]