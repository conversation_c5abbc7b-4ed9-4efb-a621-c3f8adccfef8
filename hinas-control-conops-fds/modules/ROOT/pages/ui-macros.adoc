= User Interface Macros
:example-caption!:

Asciidoctor has three user interface (UI) macros:

* Button `btn:[]`
* Keyboard `kbd:[]`
* Menu `menu:[]`

TIP: The `:experimental:` attribute must be set in the document header, component version descriptor (_antora.yml_), or globally in your Antora playbook (_antora-playbook.yml_) to enable UI macros.

== Button

Communicate that a user should press a button with the button macro.

.Button UI macro
----
Press the btn:[Submit] button when you are finished the survey.

Select a file in the file navigator and click btn:[Open].
----

.Result
====
Press the btn:[Submit] button when you are finished the survey.

Select a file in the file navigator and click btn:[Open].
====

== Keyboard

Create keyboard shortcuts with the keyboard macro.

.Keyboard UI macro
----
Press kbd:[esc] to exit insert mode.

Use the shortcut kbd:[Ctrl+T] to open a new tab in your browser.

kbd:[Ctrl+Shift+N] will open a new incognito window.
----

.Result
====
Press kbd:[esc] to exit insert mode.

Use the shortcut kbd:[Ctrl+T] to open a new tab in your browser.

kbd:[Ctrl+Shift+N] will open a new incognito window.
====

== Menu

Show readers how to select a menu item with the menu macro.

.Menu UI macro
----
To save the file, select menu:File[Save].

Select menu:View[Zoom > Reset] to reset the zoom level to the default setting.
----

.Result
====
To save the file, select menu:File[Save].

Select menu:View[Zoom > Reset] to reset the zoom level to the default setting.
====
