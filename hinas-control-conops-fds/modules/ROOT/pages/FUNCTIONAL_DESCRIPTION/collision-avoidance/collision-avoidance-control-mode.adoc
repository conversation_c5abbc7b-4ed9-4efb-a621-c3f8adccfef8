// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images


=== CAC Mode Operation

In CAC mode, HiNAS follows a suggested safe path after it is confirmed by the operator.
// HiNAS Control Standard
ifdef::build_target_std[]
When CAC mode is active with Route Tracking and CAA mode, the following limitations apply:
endif::build_target_std[]
// HiNAS Control
ifdef::build_target_nstd[]
When CAC mode is active with Route Tracking function and CAA function, the following limitations apply:
endif::build_target_nstd[]

[NOTE]
====
* Applying a new route via Voyage Planning is not permitted.
* Collision Avoidance settings cannot be modified.
====


==== Starting Condition
Starting conditions for CAC ensure that the CAC function can be initiated properly.
All the starting conditions listed below should be met to activate CAC.

// HiNAS Control Standard
ifdef::build_target_std[]
[[table_starting_condition_cac]]
[cols="3,^1,^1,^1", options="header"]
|===
| Starting Condition | System setting | Operator setting | Note
| Route Tracking is required to be Active to enable CAC. | - | - | -
| CAA is required to be Active to enable CAC. | - | - | -
| Newly proposed CA path is required confirmation of the operator before use. | - | - | -
| Distance to the start WPT is required to be less than the Preset Enter Distance to activate CAC. | Preset Enter Distance | - | -
| The angle between the ownship's heading and the track course is required to be less than the Preset Enter Angle to activate CAC. | Preset Enter Angle | - | -
| HiNAS HS-Mode requires both autopilot and BMS connections / HiNAS H-Mode requires an autopilot connection. | - | Control mode | -
| CAC is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode. | - | Control mode | -
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
[[table_starting_condition_cac]]
[cols="3,^1,^1,^1", options="header"]
|===
| Starting Condition | System setting | Operator setting | Note
| Route Tracking conditions is required to be Active to enable CAC. | - | - | -
| CAA is required to be Active to enable CAC. | - | - | -
| Newly proposed CA path is required confirmation of the operator before use. | - | - | -
| Distance to the start WPT is required to be less than the Preset Enter Distance to activate CAC. | Preset Enter Distance | - | -
| The angle between the ownship's heading and the track course is required to be less than the Preset Enter Angle to activate CAC. | Preset Enter Angle | - | -
| HiNAS HS-Mode requires both autopilot and BMS connections / HiNAS H-Mode requires an autopilot connection. | - | Control mode | -
| CAC is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode. | - | Control mode | -
|===
endif::build_target_nstd[]


==== Activation of CAC Mode

After ensuring all the starting conditions are met, CAC mode can be activated by clicking [CA Control] button by the operator in Safe Path Info box.
Toggling On the CAA toggle switch in the Function Activation area.


===== Preparation  

* Confirm that Route Tracking function is active as shown in <<fig_cac_activation>>.A.
* Confirm that CAA is active (CA Assistance toggle is On)
* If Collision Danger exists, Collision Danger warning is issued as shown in <<fig_cac_activation>>.B and target icon is blinking as shown in <<fig_cac_activation>>.C.
* Suggested safe path is displayed as shown in <<fig_cac_activation>>.D with Safe Path Info box shown in <<fig_cac_activation>>.F.
* Confirm the suggested safe path is appropriate.

[[fig_cac_activation]]
.How to activate CAC mode - preparation for CAC activation
image::FUNCTIONAL_DESCRIPTION/collision-avoidance/activation_of_cac_v2.png[width=600,align="center"]


===== Not Following a Suggested Safe Path

The suggested safe path can be rejected through the following process:

* Click [Not Follow] button in Safe Path Info as shown in <<fig_cac_activation_not_follow>>.F.
* A confirmation window is displayed as shown in <<fig_cac_activation_not_follow>>.G.
* Then Safe Path Info modal disappears.
* The suggested safe path is rejected, but it remains visible.


[NOTE]
====
* To open the Safe Path Info after selecting [Not Follow], deactivate CAA and then activate it again.
* The timer continues to run in the background even after CAA is deactivated.
====


[[fig_cac_activation_not_follow]]
.How to activate CAC mode - not following a safe path
image::FUNCTIONAL_DESCRIPTION/collision-avoidance/safe_path_not_follow_v2.png[width=600,align="center"]


===== Following a Suggested Safe Path

The suggested safe path can be followed through the following process:

* Check CA Control is `Ready`.
* Click [CA Control] button in Safe Path Info as shown in <<fig_cac_activation_ask_follow>>.F.
* A confirmation window is displayed as shown in <<fig_cac_activation_ask_follow>>.G.
* Selecting [Agree] activates CAC mode.


[[fig_cac_activation_ask_follow]]
.How to activate CAC mode - asking to follow a safe path
image::FUNCTIONAL_DESCRIPTION/collision-avoidance/safe_path_follow_ask_v2.png[width=600,align="center"]

* When following the suggested safe path, display changes as shown in <<fig_cac_activation_follow>>.
* CA Control is `Active` as shown in <<fig_cac_activation_follow>>.A.
* The suggested safe path is followed as shown in <<fig_cac_activation_follow>>.D.
* Decision timer and [decision] buttons are not displayed as shown in <<fig_cac_activation_follow>>.F.
* Route Info displays CA Path as shown in <<fig_cac_activation_follow>>.G.


[[fig_cac_activation_follow]]
.How to activate CAC mode - following a safe path
image::FUNCTIONAL_DESCRIPTION/collision-avoidance/safe_path_following_v2.png[width=600,align="center"]


==== Failure Condition of CAC Mode
Failure conditions define scenarios where the CAC function must be deactivated to prevent unsafe behavior.
These are monitored continuously during operation.

// HiNAS Control Standard
ifdef::build_target_std[]
[[table_cac_mode_failure_conditions]]
[cols="3,^1,^1,^1", options="header"]
|===
| Failure Condition | System Setting | Operator Setting | Note

| All Route Tracking conditions are required to function properly for CAC to work properly.
| -
| -
| 

| All CAA conditions are required to function properly for CAC to work properly.
| -
| -
| 

| HiNAS HS-Mode requires both autopilot and BMS connections / HiNAS H-Mode requires an autopilot connection.
| -
| Control mode
| CAC is deactivated if required device connections are not established.

| CAC is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode.
| -
| Control mode
| 
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
[[table_cac_mode_failure_conditions]]
[cols="3,^1,^1,^1", options="header"]
|===
| Failure Condition | System Setting | Operator Setting | Note

| All Route Tracking conditions are required to function properly for CAC to work properly.
| -
| -
| 

| All CAA conditions are required to function properly for CAC to work properly.
| -
| -
| 

| HiNAS HS-Mode requires both autopilot and BMS connections / HiNAS H-Mode requires an autopilot connection.
| -
| Control mode
| CAC is deactivated if required device connections are not established.

| CAC is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode.
| -
| Control mode
| 
|===
endif::build_target_nstd[]


===== When a Failure Occurs
If any failure condition is met, CAC mode is automatically deactivated, and a warning with visual indication is issued.

// HiNAS Control Standard
ifdef::build_target_std[]
When CAA failure conditions are violated:

* CAC is deactivated
* The most recently confirmed safe path is removed from the display
* A corresponding alert is issued

When Route Tracking failure conditions are violated:

* CAC is deactivated
* The most recently confirmed safe path is removed from the display
* Appropriate actions for Route Tracking failure handling should be taken
* A corresponding alert is issued
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
When CAA failure conditions are violated:

* CAC is deactivated
* The most recently confirmed safe path is removed from the display
* A corresponding alert is issued

When Route Tracking failure conditions are violated:

* CAC is deactivated
* The most recently confirmed safe path is removed from the display
* Appropriate actions for Route Tracking failure handling should be taken
* A corresponding alert is issued
endif::build_target_nstd[]


To clear a failure state, ensure that all starting conditions are restored.


==== Low Performance Conditions

Low performance conditions define situations where CAC may operate, but its output may be unreliable or degraded.
These conditions are listed below:

// HiNAS Control Standard
ifdef::build_target_std[]
[[table_cac_mode_low_performance_conditions]]
[cols="3,^1,^1,^1", options="header"]
|===
| Low Performance Condition | System setting | Operator setting | Note
| Route Tracking is in Low Performance state. | - | - | Any Route Tracking condition in low performance state causes CAC Low Performance.
| CAA is in Low Performance state. | - | - | Any CAA condition in low performance state causes CAC Low Performance.
| The safe path being followed is outdated. | - | - | If CAA proposes a new safe path due to situation change, the operator must accept the new safe path to avoid this condition.
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
[[table_cac_mode_low_performance_conditions]]
[cols="3,^1,^1,^1", options="header"]
|===
| Low Performance Condition | System setting | Operator setting | Note
| Route Tracking is in Low Performance state. | - | - | Any Route Tracking condition in low performance state causes CAC Low Performance.
| CAA is in Low Performance state. | - | - | Any CAA condition in low performance state causes CAC Low Performance.
| The safe path being followed is outdated. | - | - | If CAA proposes a new safe path due to situation change, the operator must accept the new safe path to avoid this condition.
|===
endif::build_target_nstd[]

===== When Low Performance Occurs

If a low performance condition is violated, CAC remains active but its output may not be fully trusted.
For example, collision avoidance performance may be unreliable, or the safe path being followed may become outdated.


===== Clearing Low Performance Status

The following table describes how to recover from low performance conditions:

// HiNAS Control Standard
ifdef::build_target_std[]
[cols="<50%,<50%", options="header"]
|===
| Low Performance Condition | Remedy Action
| Route Tracking is in Low Performance state. | Ensure all Route Tracking conditions are restored to normal operation.
| CAA is in Low Performance state. | Ensure all CAA conditions are restored to normal operation.
| The safe path being followed is outdated. | Accept the newly suggested safe path or wait for dangerous targets to clear if 'Not Follow' was selected.
|===
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
[cols="<50%,<50%", options="header"]
|===
| Low Performance Condition | Remedy Action
| Route Tracking is in Low Performance state. | Ensure all Route Tracking conditions are restored to normal operation.
| CAA is in Low Performance state. | Ensure all CAA conditions are restored to normal operation.
| The safe path being followed is outdated. | Accept the newly suggested safe path or wait for dangerous targets to clear if 'Not Follow' was selected.
|===
endif::build_target_nstd[]


==== How to Deactivate CAC Mode
CAC mode can be deactivated by deactivation of CAA or Route Tracking.