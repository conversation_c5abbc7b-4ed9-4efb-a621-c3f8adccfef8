// HTML, PDF conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== CAA Mode Operation

In CAA mode, HiNAS evaluates collision risks and suggests a safe path to mitigate them, without steering the vessel or controlling speed.

[NOTE]
====
* Applying a new route through Voyage Planning is prohibited.
* Collision avoidance settings cannot be changed.
====


==== Starting Condition
Starting conditions for CAA ensure that the CAA function can be initiated properly.  
All the starting conditions listed below should be met to activate the CAA function.

[[table_starting_condition_caa]]
[cols="3,^1,^1,^1", options="header"]
|===
| Starting Condition | System setting | Operator setting | Note
| All HiNAS software modules functions properly for CAA. | - | - | -
| Position data is mandatory for CAA. | - | - | -
| COG data is mandatory for CAA. | - | - | -
| SOG data is mandatory for CAA. | - | - | -
| RADAR connection is mandatory for CAA. | - | - | -
| AIS connection is mandatory for CAA activation. | - | - | -
| CAA function cannot be activated if an obstacle or vessel is detected at close range. | - | Safety radius, ship length | -
| CAA should generate a safe path in high-risk collision situation. | - | - | -
| CAA is available in the open sea. | - | - | -
| CAA is not supported in HiNAS S-Mode. It is available only in HiNAS H-Mode and HS-Mode. | - | Control mode | -
|===

[NOTE]
====
For the open sea condition, CAA cannot be activated if a waypoint in the applied route has an XTD limit below 2.0 NM. Every waypoint's XTD limit is required to exceed 2.0 NM on both port and starboard sides.
====


==== Activation of CAA Mode

After ensuring all the starting conditions are met, CAA mode can be activated by toggling on the CAA toggle switch in the Function Activation area.


// HiNAS Control Standard
ifdef::build_target_std[]
==== CAA Mode with Route Tracking Off
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
==== CAA Mode with Route Tracking Off
endif::build_target_nstd[]

If only CAA mode is On, HiNAS suggests a safe path; however, the operator should manually determine and steer the vessel based on their judgment.

// HiNAS Control
ifdef::build_target_nstd[]
[[fig_caa_without_tcs_act]]
.CAA Mode with Route Tracking Function Off
image::FUNCTIONAL_DESCRIPTION/collision-avoidance/caa_mode_without_tcs_act_v2.png[width=500,align="center"]
endif::build_target_nstd[]

// HiNAS Control Standard
ifdef::build_target_std[]
[[fig_caa_without_tcs_act]]
.CAA Mode with Route Tracking Off
image::FUNCTIONAL_DESCRIPTION/collision-avoidance/caa_mode_without_tcs_act_v2.png[width=500,align="center"]
endif::build_target_std[]

The following table explains the figure above during this mode:

[[table_caa_mode_ui]]
[cols="^1,^2,3", options="header"]
|===
| Label | Item | Description

| A
| Function Activation Area
a|
* Displays the current CA mode.
* Autopilot and BMS are in Manual Mode.
* CAA is active as the Route Tracking function is turned off. CA Control status is displayed as `Not Ready`.

| B
| Blinking Target
| Dangerous targets, indicated in red, blink in CAA mode.

| C
| Alert Display
| An alert (Collision Danger warning) is generated when dangerous targets are detected.

| D
| Suggested safe path
| Shows a collision-free path suggested by HiNAS. WPTs of the safe path are indicated as `SG X` and are visually distinct from the preplanned route.

| E
| PDL  
image:collision-avoidance/edited_day_CAC_pdl_closeup.png[scaledwidth=100%]
| If the position of the ownship is within the PDL, the safe path is considered valid.

| E
| Alert
| Alerts are shown when dangerous targets are detected.  
|===


// HiNAS Control Standard
ifdef::build_target_std[]
==== CAA Mode with Route Tracking On
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
==== CAA Mode with Route Tracking On
When Route Tracking is active and all CAC starting conditions are satisfied, the system enters CA Control `Ready` state and also provides Safe Path information, which includes the suggested safe path and a UI for the operator to choose [CA Control] or [Not Follow] to shift to CAC mode.
endif::build_target_nstd[]

// HiNAS Control Standard
ifdef::build_target_std[]
[[fig_caa_mode_with_tc_on]]
.CAA Mode with Route Tracking activated
image::FUNCTIONAL_DESCRIPTION/collision-avoidance/caa_mode_with_tc_on_v2.png[width=500,align="center"]
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
[[fig_caa_mode_with_tc_on]]
.CAA Mode with Route Tracking activated
image::FUNCTIONAL_DESCRIPTION/collision-avoidance/caa_mode_with_tc_on_v2.png[width=500,align="center"]
endif::build_target_nstd[]
The following table explains the figure above during this mode:

[[table_caa_mode_with_tc_on]]
[cols="^1,^2,3", options="header"]
|===
| Label | Item | Description

// HiNAS Control Standard
ifdef::build_target_std[]
| A
| Function Activation / CA Assistance / CA Control
| Indicates the current CA mode. CAA and Route Tracking are active, and all CAC conditions are met, enabling CAC readiness.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
| A
| Function Activation / CA Assistance / CA Control
| Indicates the current CA mode. CAA and Route Tracking are active, and all CAC conditions are met, enabling CAC readiness.
endif::build_target_nstd[]

| B
| Alert
| Alerts are shown when dangerous targets are detected.

| C
| Suggested safe path
| Shows a collision-free path suggested by HiNAS. WPTs of the safe path are indicated as `SG X` and are visually distinct from the preplanned route.

| D
| Safe path info. 
a|
When CAC is ready, the Safe Path Info is displayed. This includes:

* A timer to select whether to follow the suggested CA path. If it expires, a new path may be proposed.
* Overall summary of the suggested safe path (e.g., distance, ETA).
* Collision avoidance settings related to the current safe path.
* Option to [Not Follow] the suggested path.
* Option to activate [CA Control] and follow the path.
|===


==== Failure Conditions

Failure conditions define scenarios where the CAA function must be deactivated to prevent unsafe behavior.  
These are monitored continuously during operation.

[[table_caa_mode_failure_conditions]]
[cols="3,^1,^1,^1", options="header"]
|===
| Failure Condition | System Setting | Operator Setting | Note

| All HiNAS software modules should function properly for CAA to work properly.
| Module failure time limit
| -
| CAA is deactivated if any module fails to communicate within the time limit.

| Position data is mandatory for CAA.
| Sensor failure time limit
| - 
| CAA is deactivated if position sensor fails.

| COG data is mandatory for CAA.
| Sensor failure time limit
| - 
| CAA is deactivated if COG sensor fails.

| SOG data is mandatory for CAA.
| Sensor failure time limit
| -
| CAA is deactivated if SOG sensor fails.

| RADAR should be connected for CAA to work properly.
| Sensor failure time limit
| -
| CAA is deactivated if RADAR connection is lost.

| CAA function is deactivated if there is an obstacle or vessel in close range.
| -
| Safety radius, ship length
| CAA cannot operate safely with nearby targets.
|===

===== When a Failure Occurs

If any failure condition is met, CAA mode is automatically deactivated, and a warning with visual indication is issued.  

===== Clearing a Failure Status

To clear a failure state, ensure that all starting conditions are in ready state.

==== Low Performance Conditions

Low performance conditions define situations where CAA may operate, but its output may be unreliable or degraded.  
These conditions are listed below:


[[table_caa_mode_low_performance_conditions]]
[cols="3,^1,^1,^1", options="header"]
|===
| Low Performance Condition | System Setting | Operator Setting | Note
| AIS must be available and functioning correctly.
| Sensor failure time limit
| - 
| Loss of AIS targets reduces CAA performance. CAA results may be untrustworthy.

| CAA should generate a safe path when new risks are detected.
| - 
| - 
| Failure to generate a path may force navigation near dangerous targets or beyond the planned route's XTL.
|===

===== When Low Performance Occurs

If a low performance condition is violated, CAA remains active but its output may not be fully trusted.

For example, risk assessments may be unreliable,  
or a suggested safe path may not be generated or displayed.

In these cases, CAA issues a caution or warning with a corresponding indication.  
Refer to <<Alert List of HiNAS Control>> and <<Operation Detail>> for more information.

===== Clearing Low Performance Status

The following table describes how to recover from low performance conditions:

[[table_caa_mode_low_performance_clear]]
[cols="1,1, options="header"]
|===
| Low Performance Condition | Remedy Action
| AIS is available and functioning correctly.
| Ensure AIS target data is being received and AIS receiver is properly connected.

| CAA should generate a safe path when new risks are detected.
| Wait for CAA to generate a new safe path or adjust collision avoidance settings if path generation continues to fail.
|===

<<<