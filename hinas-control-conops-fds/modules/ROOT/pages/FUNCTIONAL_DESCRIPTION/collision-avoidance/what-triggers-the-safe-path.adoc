// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Safe Path Generation

In CAA mode, when a collision danger exists, HiNAS suggests a safe path along with an appropriate Collision Danger warning.  
An operator then decides whether to follow the suggested safe path.  
The following sub-sections describe how dangerous targets are determined for safe path generation and alert conditions.


==== Dangerous Target

HiNAS determines whether a target is dangerous based on the calculated CPA and the applicable reference TCPA configured in <<Collision Avoidance Settings>>.  
Different reference TCPA values are applied depending on the encounter situation:

* TCPA-GW (Reference TCPA - Giveway): 60 minutes (default)
* TCPA-SO (Reference TCPA - Stand-on): 40 minutes (default)
* Other: 60 minutes
* If the previous state was dangerous: 180 minutes

If the TCPA is within the reference value and the CPA is less than the defined safety distance configured in <<Collision Avoidance Settings>>, the target is classified as dangerous.


==== Determining Dangerous Targets

HiNAS evaluates the ownship's collision danger based on the following assumptions and calculations:

* The velocity vectors of both the ownship and the targets are assumed to be constant.

* PDL:
** PDL is the maximum allowable deviation from the planned position at a certain time, based on the safe path and its schedule.
** Essentially, PDL represents the positional uncertainty of the ownship and is clearly distinct from safety domains; PDL is not considered a safety domain.
** The size of the PDL accounts for the following factors: position sensor errors, maneuverability uncertainties, environmental disturbances, system latency, etc.

* Safety distance:
** Safety distance represents the minimum safe distance that an operator intends to maintain from a target.
** It is recommended that the safety distance be no less than 0.5 NM. One of the largest vessels in the world is approximately 400 meters long. In extreme cases, it can be assumed that the CCRP is located at the very end of the vessel.
** Therefore, the distance between two CCRPs should exceed approximately 800 meters for safety.
Since 800 meters is roughly 0.431 NM, it is recommended that the safety distance be at least 0.5 NM or more to allow for an additional margin.
** The safety radius can be configured by the operator, as described in <<Collision Avoidance Settings>>.

* Invader:
** A vessel predicted to intrude into an existing safe path. This prediction is based on the target’s speed at the time the safe path was calculated.
** If CAC is active, the evaluation is based on the following safe path; otherwise, it is based on the suggested safe path.

* CPA refers to the estimated point at which the distance between two moving objects reaches its minimum.  
  TCPA and CPA are used to assess the risk of a potential collision.

By comparing TCPA and CPA against predefined thresholds, HiNAS determines whether a target is considered dangerous, as detailed in <<Dangerous Target>>.


==== Collision Danger Alert

HiNAS generates a collision danger alert when any of the following conditions are met:

* No safe path exists.
* Dangerous targets are detected.
* The ownship deviates beyond the PDL or invaders are detected.

<<<