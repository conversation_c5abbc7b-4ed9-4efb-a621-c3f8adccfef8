// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Alert

[.lead]
==== Alert

The alerts are announcing abnormal situations and conditions requiring attention.  
An alert provides information about how to announce this event in a defined way to the system and the operator.

===== Alert Priority

The alerts are divided in four priorities: Emergency alarm, Alarm, Warning, and Caution.  
The priority of an alert is indicated by the color of the alert and the sound of the alert.  
It is also indicated by the way the alert is displayed on the screen.

[options="header",cols="1,4",frame="all",grid="all"]
|===
|Priority 
|Description

|Emergency alarm
|Highest priority of an alert. Alarms which indicate immediate danger to human life or to the ship and its machinery exits and require immediate action.

|Alarm
|An alarm is a high-priority alert. Condition requiring immediate attention and action by the bridge team, to maintain the safe navigation of the ship.

|Warning
|Condition requiring immediate attention, but no immediate action by the bridge team. Warnings are presented for precautionary reasons to make the bridge team aware of changed conditions which are not immediately hazardous, but may become so if no action is taken.

|Caution
|Lowest priority of an alert. Awareness of a condition which does not warrant an alarm or warning condition, but still requires attention out of the ordinary consideration of the situation or of given information.
|===

===== Alert Category

The alerts are divided in three categories: A, B, and C.

[options="header",cols="1,5",frame="all",grid="all"]
|===
|Category |Description
|A
|Category A alerts for which graphical information at the task station directly assigned to the function generating the alert is necessary, as decision support for the evaluation of the alert related condition.

|B
|Category B alerts where no additional information for decision support is necessary.

|C
|Category C alerts that cannot be acknowledged on the bridge but for which information is required about the status and treatment of the alert.

|===

==== Alert Presentation and Handling

When an alert is generated, the alert is displayed on the dedicated area of the screen with buzzer sound. 

===== Dedicated Area

Dedicated area located in the header, contains the number of the active alerts, alert list/history button, silence button, and display for the highest priority of active alert.


[[fig_dedicated_area]]
.Dedicated area
image::FUNCTIONAL_DESCRIPTION/Dedicated_Area.png[align="center", width=700]

The number of the alerts which priority of alarm, warning, and caution are shown in the left side of the dedicated area. 
With the alert list/history button, the operator can see the list of the alerts and the operator can temporarily stop the buzzer sound with the silence icon button.
The highest priority of active alert is shown in the right side of the dedicated area with state icon, and the acknowledge button will be shown when the highest priority of active alert is alarm or warning.

About handling the alerts (acknowledge, silence), see <<Alert Handling>>.

<<<

===== Alert State Icon

The table below shows the alert state icons representing the status of the alerts.

[options="header",cols="1,1,3,3,^1",frame="all",grid="all"]
|===
|No. |Priority |Alert State |Description |Icon graphic
|1 .5+|Alarm |Active - Unacknowledged | A flashing red triangle with a symbol of loudspeaker |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-2.svg[]
|2 |Active - Silenced | A flashing red triangle with a symbol of loudspeaker overlayed with a diagonal line. |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-3.svg[]
|3 |Active - Acknowledged | A red triangle with an exclamation mark |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-8.svg[]
|4 |Active - Responsibility transferred | A red triangle with an arrow pointing to the right |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-9.svg[]
|5 |Rectified - Unacknowledged | A flashing red triangle with a tick mark |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-6.svg[]
|6 .5+|Warning |Active - Unacknowledged | A flashing yellowish orange circle with a symbol of loudspeaker |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-4.svg[]
|7 |Active - Silenced | A flashing yellowish orange circle with a symbol of loudspeaker overlayed with a diagonal line. |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-5.svg[]
|8 |Active - Acknowledged | A yellowish orange circle with an exclamation mark |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-10.svg[]
|9 |Active - Responsibility transferred | A yellowish orange circle with an arrow pointing to the right |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-11.svg[]
|10 |Rectified - Unacknowledged | A flashing yellowish orange circle with a tick mark |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-7.svg[]
|11 |Caution |Active | A yellow square with an exclamation mark |image:FUNCTIONAL_DESCRIPTION/alert-state-icon-12.svg[]
|===

===== Alert List

The list of the alerts is shown when the operator clicks the alert list/history button. 

// alert list image
[[fig_alert_list_history]]
.Alert list and history page
image::FUNCTIONAL_DESCRIPTION/Alert_List.png[width=700,align="center"]

The list of the alerts is shown in the order of the priority of the alerts. 
The contents of the alert which is shown in the list are:

* Priority and status of the alert (with icon)
* Alert ID
* Alert title
* Alert description
* Alert occurrence UTC time
* Alert acknowledge UTC time (if the alert is acknowledged)

If the alert description is too long and gets cut off, the operator can see the full description by hovering the mouse pointer over the alert. 
The alert description includes the situation consequence, the action to be taken and further information about the remedy.
The alert list shows the maximum of 20 alerts in each page, and the operator can see the other pages by clicking the next/previous button, or page number.

The order of the alerts in the list is determined by the priority of the alerts.
Details of the order of the alerts are as follows:

[options="header",cols="1,2,4,2,2,2",frame="all",grid="all"]
|===
|Order |Priority |State |Display after acknowledgement |Display after rectifying |Display after responsibility transfer
|1 |Alarm |Active - Unacknowledged + 
& Active - Silenced | 5 | 3 | 6
|2 |Warning |Active - Unacknowledged + 
& Active - Silenced | 7 | 4 | 8
|3 |Alarm |Rectified - Unacknowledged | 10 | - | -
|4 |Warning |Rectified - Unacknowledged | 11 | - | -
|5 |Alarm |Active - Acknowledged | - | 10 | -
|6 |Alarm |Active - Responsibility transferred | 5 | 10 | -
|7 |Warning |Active - Acknowledged | - | 11 | -
|8 |Warning |Active - Responsibility transferred | 7 | 11 | -
|9 |Caution |Active | - | 12 | -
|10 |Alarm |Normal(Not display) | - | - | -
|11 |Warning |Normal(Not display) | - | - | -
|12 |Caution |Normal(Not display) | - | - | -
|===

If the order of the alerts is the same, the newest alert is shown at the top of the list.

===== Alert Handling

The alarm and warning priority alerts require the attention (and action) of the operator.
There are two ways to handle the alerts: acknowledge and silence as explained in <<Alert Acknowledge for Warning and Alarm>> and <<Alert Silence>>, respectively. 
Optionally, if the central alert management system(CAMS) or the device which have the revaluate function are available, the responsibility of the alert can be transferred to them.

====== Alert Acknowledge for Warning and Alarm

The operator can acknowledge the alert by clicking the acknowledge button in both the dedicated area and the alert list.
The highest priority alert will be acknowledged by default when the operator clicks the acknowledge button. 
However, the operator can acknowledge the other alerts by selecting them in the list and clicking the acknowledge button.
Acknowledged alerts will change their state icons to *Active - Acknowledged* state icon as shown in <<Alert State Icon>>.

====== Alert Silence

The operator can temporarily stop the buzzer sound by clicking the silence button in both the dedicated area and the alert list.
The silence will be applied to all the active alerts, and it will last for 30 seconds.
During the silence period, the silenced alerts will change their state icon to the *Active - Silenced* state icon as shown in <<Alert State Icon>>.
The alert which generates during the silence period will not be silenced.

====== Alert Responsibility Transfer Warning and Alarm

When the central alert management system(CAMS) or the device which the revaluate function are connected to HiNAS, they can take over the responsibility of the alert. 
The category B alerts can be transferred, and the category A alerts can be optionally transferred only when the devices are possible to provide the enough graphical information instead of HiNAS.
The responsibility transferred alerts will change their state icon to the *Active - Responsibility transferred* state icon as shown in <<Alert State Icon>>. 
The priority of the alert or even the alert itself can be revaluated by the CAMS or the device.

====== Normalize Alert

The alerts are normalized when the alert is rectified. For the alarm and warning priority alerts, acknowledgement from the operator is required additionally. In the case of the responsibility transferred alerts, even if the alert priority is alarm or warning, the alert will be normalized with only the rectification.

===== Alert History

The alert history stores the information of the alerts which have occurred in the past. The history last for 2 months, and the operator can see the history by clicking the alert history button in the alert list.

[[fig_alert_history_filtering]]
.Alert history and filtering
image::FUNCTIONAL_DESCRIPTION/Alert_History.png[width=700,align="center"]

The contents of the alert history which is shown in the list are:

* Alert ID
* Alert title
* Alert description
* Alert occurrence UTC time
* Alert acknowledge UTC time
* Alert rectify UTC time

The operator can filter the alert history by the date which the alert occurred with calendar picker.
The alert history shows the maximum of 20 alerts in each page, and the operator can see the other pages by clicking the next/previous button, or page number.

The order of the alerts in the history is determined by the occurrence time of the alerts.

==== Alert Escalation

The alert escalation is the process of refreshing the attention of the operator to the alert.
The alert escalation is applied to the alarm and warning priority alerts.
The alert escalation is applied when the alert is not acknowledged within a certain period.

===== Warning Priority Alert Escalation
The warning priority alerts are escalated in two ways: repeating the warning priority alert and escalating to the alarm priority alert.
The repeating warnings has their own repeating period, and the alert will be repeated as a warning until the alert is acknowledged.
The default repeating period of the alerts repeating as a warning cannot be exceeded 5 minutes.
Escalating to tha alarm priority alert is applied when the warning priority alert is not acknowledged within the 30 seconds after the alert is generated.
Not every warning priority alert is escalated to the alarm priority alert, and details of the escalation are described in *alert list* as shown in <<Alert List of HiNAS Control>>.

===== Alarm Priority Alert Escalation
Unlike the warning priority alerts, the alarm priority alerts require the action of the operator for the safety of the ship and the crew.
So the alarm priority alerts are always escalated to the back-up navigator alarm which call the back-up navigator to the bridge.
The back-up navigator alarm is transfer to the BNWAS (Bridge Navigational Watch Alarm System) when the alarm priority alerts are not acknowledged by the operator within the 30 seconds after the alert is generated.

==== Alert List of HiNAS Control
Alert list of HiNAS is provided in <<table_alert_list_of_hinas_control>>.

<<<