// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images

=== Cursor Reading and Presentation

HiNAS provides a cursor reading feature along with a display of the current settings, both of which are located at the bottom of the screen.

[[fig_cursor_reading_and_presentation]]
.Cursor reading and presentation  
image::FUNCTIONAL_DESCRIPTION/cursor-reading-and-presentation_v0.png[align="center",600,400]

[[table_cursor_reading_and_presentation]]
.Components of cursor reading and presentation at the bottom of the screen  
[cols="^,3,5", options="header"]
|===
| No. | Name | Description

| 1  
| Version  
| Displays the software version

| 2  
| Presentation  
a| 
* WGS 84: Datum used  
* Mercator: Map projection  
* Non-ECDIS Presentation: Indicates that the chart does not follow ECDIS standards  
* GND STAB: Ground stabilization mode  

| 3  
| Cursor Reading  
a| 
* LAT: Latitude of the cursor position  
* LON: Longitude of the cursor position  
* DIST: Distance from ownship to cursor position  
* BRG: True bearing from ownship to the cursor position  

| 4  
| Target Settings  
a| 
* AIS Class: Applied AIS class filter (Both / A / B)  
* Target Association Priority: Applied target association priority (AIS+RADAR / AIS / RADAR)  
* Target Monitoring Distance: Range for monitoring targets  
* Lost Target Warning: Applied setting for lost target alerts  
|===