// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Map Display and Related Functions

The map display area presents ownship and target ship symbols, and provides controls for adjusting chart scale and filtering target information.

[[fig_map_display_area_and_related_func]]
.Map display area and related functions
image::FUNCTIONAL_DESCRIPTION/map_display_area_v0.png[align="center",600,400]

The elements of the map display and their corresponding functions are described in the table below.

[[table_map_display_area_and_related_func]]
.Components of the map display area and their functions
[cols="^,4,5", options="header"]
|===
| No. | Button or Item | Description

| A | Ownship Symbols | displays ownship status, including position, SOG (Speed Over Ground), HDG (Heading), heading line, beam line, past track, and more.
| B | Planned Route Symbols | shows the planned route labeled as W XX, imported from an ECDIS.
| C | Target Symbols | indicates the status of target ships.
| D | Target Filter | allows selection of AIS categories and prioritization of target information.
| E | Home Button | centers the map view on the ownship’s current position.
| F | Relative Mode Control | enables the relative motion display mode.
| G | Chart Scale Control | adjusts the map’s zoom level.
| H | Chart Scale/Presentation | displays the active chart scale and presentation mode (e.g., North-up).
|===