// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Menu Overview
This section provides a brief description of the HiNAS Control menu structure.
HiNAS Control consists of several main menus, each corresponding to a specific function or feature.


==== Header
The header bar is located at the top of the display.
The header provides the Main Tab, which includes Voyage Planning, Operation Details, Settings, Alert Box, Brilliance Control, Display Mode Control, Time, and User Profile.


==== Route Information
The route information box provides the current following information prepared by operators generated by HiNAS.


==== Function Activation
HiNAS provides toggle switches for activating Route Tracking. Additionally, related information is displayed near the activation switch.


==== Ship Status and Environment
HiNAS provides toggle switches for activating Route Tracking. Additionally, related information is displayed near the activation switch.


==== Map Display and Functions
The map display area shows ownship and target-ship symbols and also provides control over chart scales and filtering of target information displayed.


==== Camera Display
Camera Display area shows EO/IR camera views, HDG/COG, Next Turn, Target-Ship AR Symbol, Planned/Actual Info., and more.


==== Cursor Reading and Presentation
HiNAS provides a cursor reading function along with a display of the applied settings, both of which are located at the bottom of the view.


==== Admin and Authenthication
This page is only can be managed by admin user.
The admin can manage user id and basic information of the system such as user information, ship dynamics model, and ship information.