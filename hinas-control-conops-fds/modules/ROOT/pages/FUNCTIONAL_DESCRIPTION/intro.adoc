// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

HiNAS Control autonomous navigation solution enables users to achieve the ‘Autonomous Shipping’. 
This function description is written to provide the user with instruction of HiNAS Control (Hyundai intelligent Navigation Assistant System  Control). 
HiNAS Control is an intelligent autonomous navigation solution service for autonomous ship. HiNAS Control collects various data from on-board devices and systems.
The collected data is analyzed, stored, and inferred for safe and comfort navigation for certified users (i.e., certified officer or captain).
HiNAS Control has been developed in collaboration with HHI the world best ship builder. With HHI’s the best ship domain technology and IT technology of AVIKUS, it enables total integration of navigational equipment and achieve the autonomous navigation. 
The autonomous navigation scope is ‘plan – recognize – decide – control’ loop in the designated operation envelope.
And more, it can assist the users to safe and comfort navigation by reducing human error and automating the navigation and maneuvering task of certified users.
In conclusion, HiNAS Control enables users to achieve ‘autonomous navigation’.  
To implement HiNAS Control system, <<table-function-and-part-list-of-hinas-control>> below describes the function and part list of the system. 


[[table-function-and-part-list-of-hinas-control]]
.Description of interfaced equipments
[cols="2,3,4", options="header"]
|===
| Category
| Function
| Description

| HiNAS Control S/W
| Camera display
a|
* EO/IR camera display on web browser  
* Vision stitching (from three camera images into one)

| 
| Map display
a|
* Display of ENC (minimum) chart data  
* Display own ship and nearby traffic on the map  
* Update displayed map area based on own ship position offset

| 
| Voyage planning
a|
* Speed optimization based on weather forecast with validated route via *From ECDIS* or *From File* menu  
* Apply planned route to system

| 
| Target detection
a|
* AIS target detection  
* Radar target detection (if user tracks the target)  
* Vision-based target detection  
* Target association between AIS and ARPA

| 
| Navigation accident risk evaluation
a|
* Collision risk evaluation for detected targets  

| 
| Autonomous route tracking
a|
* Calculate heading command for route tracking  
* Command autopilot and BMS for autonomous route and speed tracking

| 
| Autonomous collision avoidance (CAA/CAC)
a|
* Determine vessel encountering situation (based on COLREG)  
* Decide whether avoidance is needed (risk assessment)  
* Calculate safe path for collision avoidance  
* Display safe path and speed command  
* Command autopilot and BMS for autonomous collision avoidance

| 
| Status (interface) monitoring
a|
* Monitor signals from interfaced equipment  
* Evaluate health status of interfaced devices and internal modules  
* Display health status and interfaced signals  
* Decide availability of autonomous navigation mode

| 
| Alert
a|
* Provide audible alert based on alarm classification  
* Provide visual alert based on alarm classification  
* Alert acknowledgment  
* Alert history lookup  
* Share alert info with other systems (BNWAS, BAMs if applicable)

| 
| Interfaced information display
a|
* Display interfaced data (GPS, STW, etc.) on display unit

| HW supply scope of HiNAS Control
| Server (HiNAS Control)
a|
* Processes all system functions

| 
| Server (Client)
a|
* Client-side display unit for user interaction

| 
| EO Camera
a|
* Vision sensing of external environment (about 180° FOV)

| 
| IR Camera
a|
* Infrared sensing of external environment (about 140° FOV)

| 
| Single board computer
a|
* Handles image streaming and stitching from cameras

| 
| Network switch
a|
* Interfaces internal and external devices via Ethernet

| 
| Firewall
a|
* Secure interface with external equipment

| 
| Serial to ethernet
a|
* Converts serial communication to Ethernet communication

| 
| UPS
a|
* Maintains power supply during blackout for a designated period

| Interfaced devices with HiNAS Control
| AIS
a|
* Detects position, speed, and course of AIS-equipped ships

| 
| GPS
a|
* Detects own ship’s position and course

| 
| LOG
a|
* Measures speed through water

| 
| Anemometer
a|
* Measures wind speed and direction

| 
| ECDIS
a|
* Validated route provider

| 
| RADAR
a|
* Detects objects scanned by radar

| 
| BMS (Bridge Maneuvering System)
a|
* Controls speed based on system command

| 
| Loading computer
a|
* Detects ship’s loading condition (draft)

| 
| BNWAS
a|
* Provides navigator alerts from HiNAS Control

| 
| CAMS
a|
* Manages and displays alerts

|===