// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Autopilot Operation

// HiNAS Control Standard
ifdef::build_target_std[]
The following subsections describe the method for activating the interfaced autopilot required for the Track Control function.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
The following subsections describe the method for activating the interfaced autopilot required for the Route Tracking function.
endif::build_target_nstd[]
HiNAS can interface with the following models:

* TOKYO KEIKI PR-9000  
* YOKOGAWA PT900  


==== TOKYO KEIKI PR-9000

The following diagram shows the configuration for the TOKYO KEIKI PR-9000:

[[fig-autopilot-config-tkm]]
.Configuration of Autopilot (TOKYO KEIKI PR-9000)
image::FUNCTIONAL_DESCRIPTION/tcs-operation/autopilot_configuration.png[width=400,align="center"]

<<<

[[fig-heading-controller-unit-tkm]]
.Heading controller unit (TOKYO KEIKI PR-9000)
image::FUNCTIONAL_DESCRIPTION/tcs-operation/autopilot_heading_controller.png[width=500,align="center"]

<<<

==== Activation of TOKYO KEIKI PR-9000

// HiNAS Control Standard
ifdef::build_target_std[]
After toggling On the Track Control switch, the heading controller display panel of the PR-9000 shows `E.HC RDY`, as shown in <<fig-ext-hc-rdy-tkm>>:
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
After toggling On the Route Tracking switch, the heading controller display panel of the PR-9000 shows `E.HC RDY`, as shown in <<fig-ext-hc-rdy-tkm>>:
endif::build_target_nstd[]

[[fig-ext-hc-rdy-tkm]]
.External heading control ready (TOKYO KEIKI PR-9000)
image::FUNCTIONAL_DESCRIPTION/tcs-operation/autopilot_ready.png[width=500,align="center"]

Press and hold the `Route Tracking/HC` key on the heading controller for 1–2 seconds.
HiNAS will then take control, and the display will change from `E.HC RDY` to `EXT.HC`, as shown in <<fig-ext-hc-act-tkm>>.

[[fig-ext-hc-act-tkm]]
.External heading control activated (TOKYO KEIKI PR-9000)
image::FUNCTIONAL_DESCRIPTION/tcs-operation/autopilot_steering_mode.png[width=300,align="center"]

<<<

==== YOKOGAWA PT900

The following diagram shows the configuration for the YOKOGAWA PT900:

[[fig-autopilot-config-ydk]]
.Configuration of Autopilot (YOKOGAWA PT900)
image::FUNCTIONAL_DESCRIPTION/tcs-operation/PT900_overview.png[width=500,align="center"]

<<<

[[fig-heading-controller-unit-ydk]]
.Heading controller display panel (YOKOGAWA PT900)
image::FUNCTIONAL_DESCRIPTION/tcs-operation/PT900_display.png[width=600,align="center"]

<<<

==== Activation of YOKOGAWA PT900

After toggling on the Route Tracking switch, the PT900 automatically changes from `AUTO` to `NAS-HC` with an audible sound, as shown in <<fig-ext-hc-act-ydk>>.

[[fig-ext-hc-act-ydk]]
.External heading control activated (YOKOGAWA PT900)
image::FUNCTIONAL_DESCRIPTION/tcs-operation/pt900_activation_v1.png[width=600,align="center"]

<<<