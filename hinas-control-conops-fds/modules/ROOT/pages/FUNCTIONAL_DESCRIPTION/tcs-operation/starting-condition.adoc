// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

=== Starting Conditions

// HiNAS Control Standard
ifdef::build_target_std[]
All starting conditions should be satisfied before activating Track Control.
These conditions include the status of sensors, ownship state, and the operational readiness of software modules required for Track Control activation.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
All starting conditions should be satisfied before activating Route Tracking.
These conditions include the status of sensors, ownship state, and the operational readiness of software modules required for Route Tracking activation.
endif::build_target_nstd[]
For more details, refer to <<Starting Condition>>.