// html, pdf conversion support //
:imagesdir: images   
// Preview Mode //
// :imagesdir: ../../images

// HiNAS Control Standard
ifdef::build_target_std[]
=== Precautions for using the Track Control Function

When using Track Control function, the following precautions should be observed:
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
=== Precautions for using the Route Tracking Function

When using Route Tracking function, the following precautions should be observed:
endif::build_target_nstd[]

* Transition to Turning mode: When the vessel passes the WOL, the system adjusts the course toward the Next WPT and enters Turning mode.

* Staying within XTL: Route Tracking function maintains the vessel's position within the defined XTL. If XTE increases, the system issues heading commands to return the vessel to the planned track.

* RPM adjustment in HS mode: In HS mode, RPM changes are applied only on straight tracks after exiting Turning mode.

* Route tracking function may stop automatically: Route Tracking may deactivate depending on vessel conditions. If a deactivation alert appears, the operator should immediately take manual control.

// HiNAS Control Standard
ifdef::build_target_std[]
* Related parameters: Configuration parameters related to the Route Tracking function can be found in <<Track Control Settings>>.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
* Related parameters: Configuration parameters related to the Route Tracking function can be found in <<Route Tracking Settings>>.
endif::build_target_nstd[]

[NOTE]
====
System performance may be affected by factors such as vessel condition and environmental influences (e.g., currents and waves).  
Constant monitoring by the operator is required to ensure safe navigation.
====



// HiNAS Control Standard
ifdef::build_target_std[]
=== Track Control Function Related State
Once all starting conditions for activating the Track Control function are satisfied, the operator can proceed with activation.

Track Control function and its related states are displayed in the Function Activation area, as shown in <<fig-function-act-box>>.
Each labeled item is described in the following table.

[[fig-function-act-box]]
.Function Activation area displaying Track Control function status
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
=== Route Tracking Function Related State
Once all starting conditions for activating the Route Tracking function are satisfied, the operator can proceed with activation.

Route Tracking function and its related states are displayed in the Function Activation area, as shown in <<fig-function-act-box>>.
Each labeled item is described in the following table.

[[fig-function-act-box]]
.Function Activation area displaying Route Tracking function status
endif::build_target_nstd[]
image::FUNCTIONAL_DESCRIPTION/tcs-operation/function_act_init_v1.png[width=300, align="center"]

[[table_function_activation_area]]
[cols="^1,^2,3", options="header"]
|===
| Label
| Name
| Description 

| A
| Route Tracking Activation
| Toggle switch for activating the Route Tracking function

| B
| Control Mode
| displays the currently selected control mode

| C
| Autopilot State
| shows the current status of the autopilot

| D
| BMS State
| shows the current status of the BMS

| E
| Operation Detail
| opens the Operation Detail page, which indicates the Route Tracking function’s Ready/Not Ready state
|===

Details for each area are provided in the following subsections:



==== Route Tracking Activation States
The activation process includes four distinct stages, as illustrated below:

[[table_route_tracking_act_state]]
[cols="^1,^1,3", options="header"]
|===
| Icon | State  | Description 

| image:FUNCTIONAL_DESCRIPTION/tcs-operation/Toggle_Disable.png[Blocked State, width=50]
| Blocked
| Starting conditions are not satisfied; Route Tracking cannot be activated.

| image:FUNCTIONAL_DESCRIPTION/tcs-operation/Toggle_Off.png[Inactive State, width=50]
| Inactive
| Starting conditions are met; Route Tracking is available for activation.

| image:FUNCTIONAL_DESCRIPTION/tcs-operation/Toggle_On_Turning on.png[Request State, width=50]
| Requested
| Activation has been requested; the system is awaiting responses from navigation equipment.

| image:FUNCTIONAL_DESCRIPTION/tcs-operation/Toggle_On.png[Activate State, width=50]
| Activated
| Route Tracking is active; HiNAS has control over the navigation equipment.
|===


==== Control Mode
Displays the current control mode, which can be one of three options (HS, H, or S) as defined in <<Control Mode>>.


==== Autopilot State
Autopilot state indicates the current status of the autopilot interfaced with HiNAS.

[[table_autopilot_state]]
[cols="^1,^1,3", options="header"]
|===
| State
| Color (Validity)
| Description

| `**`
| Red
| Connection is not established; HiNAS cannot receive autopilot signals.

| NFU
| Green
| Autopilot is in NFU mode.

| Manual
| Green
| Autopilot is in manual steering mode.

| StandAlone
| Green
| Autopilot is in heading control mode.

| HC (HiNAS)
| Green
| Autopilot is controlled by HiNAS in heading control mode.

| HC-Turn (HiNAS)
| Green
| Autopilot is controlled by HiNAS and is in turning mode.
|===



==== BMS State

The BMS state indicates the current operational status of the BMS interfaced with HiNAS.

[[table_bms_state]]
[cols="^1,^1,3", options="header"]
|===
| State
| Color (Validity)
| Description

| `—`
| Red
| Disconnected: HiNAS cannot communicate with the BMS.

| Manual
| Green
| The BMS is in manual mode; HiNAS does not have control authority.

| Auto (HiNAS)
| Green
| The BMS is under HiNAS control.
|===


=== Activation of Route Tracking Function

If Route Tracking activation toggle in the Function Activation area is in Inactive state, an operator can initiate the Route Tracking function.  
Toggling the switch to the On position changes its state to Requested, as shown in <<fig_route_tracking_act_requested>>.  
This state remains until the navigation equipments acknowledge the request and transitions to HiNAS-controlled states.

[[fig_route_tracking_act_requested]]
.Route Tracking function - Requested
image::FUNCTIONAL_DESCRIPTION/tcs-operation/route_tracking_act_requested_v0.png[width=400,align="center"]

Once HiNAS successfully takes control of all navigation equipment, the toggle state changes to Activated, as shown in <<fig_route_tracking_activated>>.

[[fig_route_tracking_activated]]
.Route Tracking function - Activated
image::FUNCTIONAL_DESCRIPTION/tcs-operation/route_tracking_activated_v1.png[width=400,align="center"]

Note that prior to activating the Route Tracking function in HS mode, the autopilot and BMS states are typically StandAlone and Manual, respectively, as shown in <<fig_route_tracking_act_requested>>.  
In H mode or S mode, the autopilot and BMS operate independently and are not both governed by the Route Tracking function.

[NOTE]
====
Once Route Tracking function is activated, the following actions are restricted:

* Changing To WPT is not allowed.
* Sensor switching (e.g., GPS1 → GPS2) is prohibited.
* Route modification or re-application is not permitted during Route Tracking.
====


=== Dectivation of Route Tracking Function
Route Tracking function can be deactivated under the following conditions:


==== Deactivation Requested by an Operator
The operator can manually request deactivation of Route Tracking function by toggling the switch to the Off position.
A confirmation dialog will then appear.
// HiNAS Control Standard
ifdef::build_target_std[]
Clicking the [Agree] button deactivates the Route Tracking function and issues a TRK Control Stop warning.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
Clicking the [Agree] button deactivates the Route Tracking function and issues a Route Tracking Stop warning.
endif::build_target_nstd[]


==== Forced Deactivation from Failure
The activated Route Tracking function is automatically deactivated if a failure occurs.
// HiNAS Control Standard
ifdef::build_target_std[]
When this happens, a TRK Control Stop warning is issued to notify the operator.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
When this happens, a Route Tracking Stop warning is issued to notify the operator.
endif::build_target_nstd[]
For more details on possible failures and how to respond, refer to <<Operation Detail>>.


=== Role of Look Ahead Coefficient

// HiNAS Control Standard
ifdef::build_target_std[]
Look Ahead Coefficient, defined in <<Advanced Track Control Settings>>, determines how the vessel aligns with the track.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
Look Ahead Coefficient, defined in <<Advanced Route Tracking Settings>>, determines how the vessel aligns with the track.
endif::build_target_nstd[]
It specifies the distance ahead of the vessel where the track is targeted.

* Large coefficient: Targets a point farther ahead on the track, resulting in slower but smoother convergence.
* Small coefficient: Targets a closer point, leading to quicker and sharper convergence.

[[fig-approach-coefficients]]
.Effect of the approach coefficient, with the circle in the ownship symbol indicating the CCRP
image::FUNCTIONAL_DESCRIPTION/tcs-operation/approach_parameter.png[width=300,align="center"]


=== Limiting Sudden Heading Changes: Off-Heading-Limit

// HiNAS Control Standard
ifdef::build_target_std[]
To prevent abrupt heading changes during navigation, the off-heading-limit is applied, as defined in <<Track Control Settings>>.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
To prevent abrupt heading changes during navigation, the off-heading-limit is applied, as defined in <<Route Tracking Settings>>.
endif::build_target_nstd[]
This constraint ensures that the difference between the vessel’s current heading and the commanded heading remains within a predefined threshold.

Example:

* Current heading of the vessel: 0°
* Desired heading command: 330°
* Angle difference: 30°
* If the off-heading-limit is set to 15°, the heading command is restricted to 345° to avoid exceeding the limit.

This mechanism supports smooth transitions and stable navigation while keeping the vessel close to the intended track.

[[fig-effect-of-off-heading-limit]]
.Effect of off heading limit, with the circle in the ownship symbol indicating the CCRP
image::FUNCTIONAL_DESCRIPTION/tcs-operation/off_heading_limit.png[width=300,align="center"]