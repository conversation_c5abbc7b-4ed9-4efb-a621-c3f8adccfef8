// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

==== Image Stitch

*Geometric Alignment*: The goal of the image stitch is to generate one image from multiple cameras.
The most common method for image stitching is to estimate the 2D perspective transformation between the images and transform the image to another.
The problem is that it is impossible to generate 180-degree image because if the field of view is close to 180-degree, the image becomes extremely bigger.
Geometrically, the 3D points located perpendicular to the optical axis cannot be mapped to the image plane and are projected to infinity.
The alternative way to generate the panorama image is to use the cylindrical coordinate system.

[[fig-concept_of_image_stitch]]
.Concept of image stitch
image::FUNCTIONAL_DESCRIPTION/concept_of_image_stitch_v0.png[align="center",500,300]


*Photometric Alignment (Color Adjustment)*: As shown in figure below, even though the images are aligned geometrically, there exists the color difference at the stitched boundary.
This is because each camera has difference white balance, contrast, or brightness values. Moreover, the incident angle of the light ray is different at each camera, so it is natural each image have different colors.
One simple way to solve this problem is to compute the average values of the overlapped region at RGB channel in each image and adjust colors so that the average values from two images has same color.
More advanced technique is to compute the color difference at several local patches in the overlapped region and apply multiple adjustment values throughout the image.

[[fig_color_mismatching]]
.Color mismatching
image::FUNCTIONAL_DESCRIPTION/color_mismatching_v0.png[align="center",500,300]