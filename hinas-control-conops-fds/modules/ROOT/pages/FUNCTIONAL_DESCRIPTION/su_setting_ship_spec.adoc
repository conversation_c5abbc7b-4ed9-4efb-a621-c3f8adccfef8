// 1.1. HTML and PDF Conversion Support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

==== Ship Specification

Ship Specification page displays essential ship information such as IMO Number and Hull Number.
Operators can set the ship's dimensions, configure the RPM command range allowed by the BMS, and select manufacturers of interfaced navigation and communication instruments, as shown in the following figure.

[[fig_su_setting_ship_spec]]
.Ship specification setting page
image::FUNCTIONAL_DESCRIPTION/su_setting_ship_spec_v1.png[align="center",500,300]

The following table describes each parameter.

[[table_su_setting_ship_spec]]
.Ship specification setting parameters 
[cols="4,5", options="header"]
|===
| Parameter  
| Description  

| IMO Number
| IMO number of ownship 

| Hull Number
| Hull number of ownship 

| LBP [m]
| LBP of ownship 

| B [m]
| Breadth length of ownship

| Available RPM Range [rpm]
| RPM command range allowed by the BMS

| Autopilot Maker
| Autopilot Maker

| ECDIS Maker
| ECDIS Maker

| BMS Maker
| BMS Maker

| AMS Maker
| AMS Maker
|===