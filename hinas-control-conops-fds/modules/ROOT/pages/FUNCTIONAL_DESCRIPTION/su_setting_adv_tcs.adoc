// 1.1. HTML and PDF Conversion Support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

==== Advanced Track Control

Advanced Track Control page provides settings configuration for Route Tracking.

[[fig_su_setting_adv_tcs]]
.Advanced track control setting page
image::FUNCTIONAL_DESCRIPTION/su_setting_adv_tcs_v1.png[align="center", width=500, height=300]

The following table outlines each parameter's function, recommended values, and allowable ranges available on the Advanced Track Control page.

[[table_su_setting_adv_tcs]]
.Advanced track control parameters
[cols="3,4,^2,^2", options="header"]
|===
| Parameter
| Description
| Recommended value
| Setting range

| Look Ahead Coefficient (Ballast/Half/Full)
| Lower values result in quicker track adherence.
| 20/30/40
| -

| Reach Length Coefficient (Ballast/Half/Full)
| Distance forward before initiating a curved track.
| 1.5/1.8/2.3 [LBP]
| -

| ROT Gain
| Proportional gain (P gain) for XTE during turns.
| 5
| 5 - 20

| ROT Counter Gain
| Derivative gain (D gain) minimizing overshoot from XTE.
| 5
| 5 - 20

| Turning ROT Reducer Filter
| Manages ROT deceleration during turns (0: Off; higher values increase deceleration).
| 4
| 1 - 6

| Turning Weather Compensation
| Gain compensating for turning influenced by weather, calculated from STW and SOG differences.
| 7
| 0 - 15

| Nominal ROT [deg/min]
| ROT maintained during straight paths and within corridors.
| 20
| -

| Spike Filter Threshold [NM]
| Threshold for filtering GPS spikes; spikes below this value are ignored.
| 0.05
| -

| Spike Filter Count [s]
| Time frame to monitor GPS spikes.
| 10
| -
|===