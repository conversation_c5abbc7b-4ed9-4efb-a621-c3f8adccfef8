// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Route Information

Route information box provides the details of route information for selected To WPT.
The displayed information is generated by Voyage Planning menu.
Details of the provided information is expained below.


[[fig_route_info_box]]
.Route information box and display of the related information.
image::FUNCTIONAL_DESCRIPTION/route_info_v0.png[,800,300]


[[table_route_info_box]]
.Details of route information box.
[cols="^,4,5", options="header"]
|===
|No. | Control                         | Description                  

|A   | TO-WPT Number                   | displays the selected WPT number of the To-WPT chosen by operators.  
|B   | Leg Information                 | displays detailed information related to the current LEG.  
|C   | NEXT-WPT                        | displays the NEXT-WPT, including NCRS and NRPM.  
|D   | Select TO-WPT Shortcut Button   | provides a shortcut to Select TO-WPT.  
|E   | Select TO-WPT                   | allows the selection of a TO-WPT. If Other WPTs is pressed, the UI for selecting a TO-WPT with related route information is displayed.  
|F   | Select WPT Button               | updates the TO-WPT to the selected TO-WPT when the Select WPT button is pressed.  
|G   | Route with Information in the Maps Display Area | displays the route and alternative routes along with related route information.  
|===


// [[fig_main_menu_voyage_planning]]
// .Voyage planning menu
// image::FUNCTIONAL_DESCRIPTION/main_menu_voyage_planning_v0.png[align="center",400,300]