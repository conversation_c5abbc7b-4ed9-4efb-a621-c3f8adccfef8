// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

==== Target Association

HiNAS tracks the target by applying target association technology using AIS, X/S-band RADAR, Electric Optical (EO) camera, and Thermal camera.
AIS, X-band radar, and S-band radar can provide information about the position, course, speed, and size of other vessels in the vicinity, while electric optical camera and thermal camera can provide information about the appearance and temperature of the vessels and their surroundings.
By combining the data from these multiple sensors, it is possible to get a more comprehensive understanding of the environment and to improve the situational awareness and safety of the vessel.
For example, AIS can provide detailed information about the identity, position, and speed of other vessels, while X-band and S-band radars can provide information about the range and bearing of other vessels.
The electric optical camera can provide a visual representation of the vessel, while the thermal camera can provide a visual representation of the vessel information by using the temperature of objects.
By combining these multiple sources of information, it is possible to get a more complete picture of the environment and to reduce the risk of accidents.
The use of target association in this manner can also help to overcome the limitations of individual sensors, such as being affected by weather conditions or being limited to a single dimension of information.
By combining the data from multiple sensors, it is possible to provide a more robust and reliable representation of the environment, and to improve the performance and safety of the vessel.


[[fig-concept-of-sensor-fusion]]
.Concept of target associator
image::FUNCTIONAL_DESCRIPTION/concept_of_sensor_fusion_v0.png[align="center",500,300]


Target association can be thought of as a process that involves several key steps, including:

*Data collection*:
The first step in the target association process is to collect data from each of the individual sensors.
This might involve reading the raw data from the sensors or receiving the data over a network connection.

*Pre-processing*:
The next step is to pre-process the raw data from each of the sensors. 

* AIS, RADAR: This step might involve removing noise and outliers, formatting the data in a manner that is suitable for association, and synchronizing the data

* EO, IR Image: Technology using images estimates the location of obstacles using the following two main technologies. The deep learning-based obstacle detection technique provides a fast and efficient way to detect obstacles in images, while the Augmented Reality technique provides a way to estimate the position of the obstacles in the real-world environment.
** Deep learning-based obstacle detection:
A method of detecting obstacles in images taken with an Electric Optical (EO) camera or an Thermal camera using deep learning algorithms such as Convolutional Neural Networks (CNN). This is explained in detail in the previous section.
** Augmented Reality technique:
This method involves overlaying digital information on the real-world view captured by the EO camera or IR camera. The Augmented Reality technique can be used to calculate the position of the obstacle by estimating the 3D location of the obstacle in the camera's field of view. This is explained in detail in the previous section.

*Association algorithm*: After the data has been pre-processed, it is then combined using an association algorithm.
HiNAS uses a Kalman filter-based target association algorithm.
The specific details of the algorithm will depend on the requirements of the application, including the mathematical model of the system, the measurement and control inputs, and the noise characteristics of the sensors.
However, the overall goal of the Kalman filter-based target association algorithm is to provide an accurate and robust estimate of the state of the system over time, based on data from multiple sensors.

* Target association is a technique used in multi-object tracking to identify which measurement corresponds to which target. In other words, it is the process of matching a set of measurements with a set of targets or objects. Data association is a crucial step in multi-object tracking, as it allows the tracker to determine which measurements are associated with which objects over time. To solve this problem, below two-staged strategies are applied. 
** 1st step: This step associates AIS and radar target data in Latitude-Longitude plane. If the target data is precepted from devices, (x,y) coordinates and its velocity can be calculated. Based on the formulae in <<fig-concept-of-data-association-weighted-sum>>, the distance weight between target A and B, the velocity weight between target A and B can be calculated. If the value is calculated equal or larger than 0.95, target A and target B is assumed to be same target. Result of 1st step is mapped into pixel plane based on distance bearing. 
** 2nd step A (Association with bounding box): This step associate information between bounding box, which is result of object detection by camera device. Based on the formulae 2nd step A in <<fig-concept-of-data-association-weighted-sum>>, the information of bounding box is associated. 
** 2nd step B (~2nd step A): This step associates 1st step result and object detected by camera in pixel plane. If bearing and distance in (x,y) plane is calculated, it can be mapped into pixel plane. To match bounding box information and AIS, Radar information the association formula follows 2nd step B in <<fig-concept-of-data-association-weighted-sum>>. 
** 2nd step A and 2nd step B is calculated iteratively.

* Model definition: The first step in the Kalman filter-based target assoication algorithm is to define a mathematical model of the system being monitored. This model should describe the dynamic relationships between the system's states, inputs, and outputs. For example, the model might describe the position and velocity of an object over time, or the orientation of a vessel.
* State prediction: The next step is to use the mathematical model to predict the state of the system at the next time step, based on the current state and inputs.
* Measurement update: The next step is to incorporate measurement information from the sensors into the state estimate. This is done by using the Kalman gain matrix, which is calculated based on the measurement noise covariance matrix and the process noise covariance matrix from the mathematical model.


[NOTE]
====
* 2nd step is for associating the target information in front view. 
* Since camera only detected targets have no information of distance and bearing, those targets cannot be mapped into chart plane 
* Note that regarding the performance test of target association, the test described in IEC62388 10.8.2 is conducted and passed for AIS and Radar target association.
* Note that AIS data is from onboard AIS interface. 
====


[[fig-concept-of-data-association-weighted-sum]]
.Concept of data association - weighted sum
image::FUNCTIONAL_DESCRIPTION/concept_of_data_association_weighted_sum_v0.png[align="center",500,300]


*Estimation*: The next step is to use the fused data to estimate the state of the environment. This might involve estimating the position, velocity, and orientation of objects in the environment, as well as other parameters.


[[fig-formulae_for_sensor_fusion_kalman_filter_based]]
.Formulae for target association - Kalman filter-based approach
image::FUNCTIONAL_DESCRIPTION/formulae_for_sensor_fusion_kalman_filter_based_v0.png[align="center",500,300]


[[fig-block_diagram_of_sensor_fusion_algorithm]]
.Block diagram of target association algorithm
image::FUNCTIONAL_DESCRIPTION/block_diagram_of_sensor_fusion_algorithm_v0.png[align="center",500,300]