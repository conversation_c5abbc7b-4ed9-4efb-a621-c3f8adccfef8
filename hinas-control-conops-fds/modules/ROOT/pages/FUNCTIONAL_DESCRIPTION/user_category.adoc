// 1.1. HTML and PDF Conversion Support
:imagesdir: images
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode
// :imagesdir: ../../../images

==== User Category

HiNAS Control automates voyage operations by issuing commands to the autopilot and BMS.
Users are divided into three categories based on their system privileges.
Access rights for each category are summarized in <<table_user_category_and_authority>>.

* Admin
** can full access to all HiNAS Control functions, including system configuration and administration.

* Navigator
** has full operational access except for Admin Settings.
** can perform voyage planning, function activation, and alert management.

* Viewer
** can access to voyage planning (calculate, export) and monitoring features.
** can view but cannot acknowledge alerts.

[[table_user_category_and_authority]]
.User category and authority
[cols="4,^2,^2,^2", options="header"]
|===
| Menu                                        3+| User Category
|                                               | Admin | Navigator |Viewer

| Voyage Planning – Calculate (SPD optimization)| O    | O       | O
| Voyage Planning – Apply plan                  | O    | O       | X
| Voyage Planning – Route cancel                | O    | O       | X
| Voyage Planning – Export route file           | O    | O       | O

// HiNAS Control Standard
ifdef::build_target_std[]
| Function Activation – TC                      | O    | O       | X
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
| Function Activation – Route Tracking          | O    | O       | X
endif::build_target_nstd[]
| Function Activation – CAA                     | O    | O       | O
| Function Activation – CAC                     | O    | O       | X

| Monitoring                                    | O    | O       | O
| Display change                                | O    | O       | O

| Alert – View                                  | O    | O       | O
| Alert – ACK                                   | O    | O       | X

| Setting                                       | O    | O       | X
| Admin Setting                                 | O    | X       | X
|===

[NOTE]
====
* O: Can access/control freely
* X: Cannot control
====