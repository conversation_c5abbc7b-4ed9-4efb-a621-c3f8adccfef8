// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Camera Display and Related Functions

The *Camera Display* area presents the following items:

* EO/IR camera views  
* HDG/COG indicators  
* Target-ship AR symbols  
* Planned/actual navigation information  

In addition, it provides the following controls:

* Toggling the Target-ship AR symbol on or off  
* Selecting between EO and IR camera views  

[[fig_cam_display_area_and_related_func]]
.Camera Display Area and Related Functions  
image::FUNCTIONAL_DESCRIPTION/cam_display_area_v1.png[align="center",400,300]

The components and their functions are described in the following table:

[[table_cam_display_area_and_related_func]]
.Components of the Camera Display Area  
[cols="^,4,5", options="header"]
|===
| No. | Name | Description

| 1 | Camera display area (background) | displays the EO or IR camera view.  
| 2 | EO/IR | selects the camera source (EO or IR).  
| 3 | HDG/COG gauge | indicates the ownship’s heading (HDG) and course over ground (COG).  
| 4 | EO/IR | displays the current camera mode (EO or IR) and ownship's HDG/COG.  
| 5 | Target-ship AR symbol | provides CPA, TCPA, distance (DIST), and bearing (BRG) of the target ship, positioned accordingly in the camera view.  
| 6 | Target-ship AR on/off | toggles the display of the Target-ship AR symbol.  
| 7 | Rudder angle indicator | visually displays the current rudder angle.  
| 8 | Planned/Actual info. | shows planned and actual RPM, SOG, and rudder angle (planned rudder angle not included).  
|===
