// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Voyage Planning Functions

The primary function of voyage planning is to optimize the voyage based on a given passage plan.  
HiNAS suggests the optimal RPM required to reach the destination according to the estimated schedule or to pass each WPT efficiently.  
RPM values are selected based on weather forecasts to minimize fuel consumption while ensuring timely arrival.


=== Voyage Optimization

HiNAS offers two types of voyage optimization:

* RPM Optimization: determines the optimal RPM for each WPT, enabling the vessel to minimize FOC while still meeting the ETA.
* Route Optimization: calculates both the optimal route and corresponding RPMs based on a passage plan prepared by the operator.

The optimization server stores high-resolution weather forecast data (less than 0.25°), which includes information on currents, wind, waves, water temperature, and salinity.  
Forecasts are updated four times daily, every six hours.  
Depending on the data type, the forecast coverage ranges from six to sixteen days.


=== Re-optimization

As weather forecasts are updated periodically, HiNAS allows operators to reflect the latest weather data even after mode activation through the re-optimization function.  
Re-optimization can also be used to adjust the ETA after HiNAS modes have been activated.


=== Limitations

* Machine learning models are employed to recommend optimal RPMs that minimize fuel consumption while meeting ETA.These models require sufficient training data, which may not be available initially. To address this, a margin parameter is used. If the margin is positive, the same RPM yields a lower SOG; if negative, it yields a higher SOG. This parameter is configured during sea trials in consultation with navigators and can be adjusted by navigators during voyage planning. As voyage data accumulates, the model’s accuracy improves.

ifdef::build_target_std[]
* In Route Tracking mode, the RPM of the current leg cannot be changed. Thus, any optimization based on updated weather data applies from the next waypoint onward.
endif::build_target_std[]

ifdef::build_target_nstd[]
* In Route Tracking mode, the RPM of the current leg cannot be changed. Thus, any optimization based on updated weather data applies from the next waypoint onward.
endif::build_target_nstd[]

[NOTE]
====
* Route optimization is an experimental feature and not included in the official release.
====



=== Importing Route

HiNAS provides two methods for importing a route planned by the operator:

* From ECDIS: imports the route currently monitored by the ECDIS.
* From Files: imports a route stored in an .RTZ file.

==== Importing Route from ECDIS

When the connection between the ECDIS and HiNAS is functioning properly, the route under monitoring mode on the ECDIS is shared with HiNAS.  
This shared route can be imported by selecting Voyage Planning > From ECDIS.  
WPTs of the imported route are displayed in the map area, as shown in <<fig_from_ecdis_menu>>.

[[fig_from_ecdis_menu]]
.Importing route using From ECDIS menu
image::FUNCTIONAL_DESCRIPTION/from_ecdis_import_route_v0.png[width=600,align="center"]

==== Importing Route from File

HiNAS supports route import using files in the RTZ format.  
The RTZ file should be stored on a USB memory stick.  
To import, navigate to Voyage Planning > Choose File, as shown in <<fig_from_file_choose_file>>.  
WPTs from the selected file are then displayed in the map area, as shown in <<fig_from_file_import_route>>.

[[fig_from_file_choose_file]]
.Navigation of route file
image::FUNCTIONAL_DESCRIPTION/from_file_choose_file_v0.png[width=600,align="center"]

[[fig_from_file_import_route]]
.Imported route using From File menu
image::FUNCTIONAL_DESCRIPTION/from_file_import_route_v0.png[width=600,align="center"]


=== Selecting Optimization Engine

HiNAS supports the following two optimization engines:

[cols="^3,^3,^3", options="header"]
|===
| Engine       | RPM Optimization | Route Optimization 

| HiNAS        | O                | X

| Oceanwise    | O                | O
|===

The operator can select the desired optimization engine by choosing from the available options, as shown in <<fig_select_opt_engine>>.

[[fig_select_opt_engine]]
.Selecting optimization engine
image::FUNCTIONAL_DESCRIPTION/select_opt_engine_v0.png[width=600,align="center"]

[NOTE]
====
* Route optimization provided by the Oceanwise engine is an experimental feature and is not part of the official release.
====


=== Inputs for Optimization

Once a route is calcualted using the From ECDIS or From File menu, the operator can configure the input parameters used for optimization, as shown in <<fig_inputs_for_opt>>.
After setting the parameters, the operator can runs optimization by clicking the [Calculate] button.  
Detailed explanations for each parameter are provided in the tables below.

[[fig_inputs_for_opt]]
.Input UI for optimization
image::FUNCTIONAL_DESCRIPTION/rpm_optimization_input_conditions_v0.png[width=600,align="center"]

[cols="^1,2,3,2", options="header"]
|===
| Label
| Item 
| Description
| Example 

| A 
| Name 
| Name assigned to the route
| 62065_sen4

| B
a|
* Start WPT
* End WPT
a| 
Start/End waypoints within the route to be used for optimization
a|
* WPT 1
* WPT 20

| C
a|
* Departure Time
* Arrival Time
a|
* Estimated departure time from Start WPT
* Estimated arrival time at End WPT
a|
* 2025-01-16T08:30:00Z
* 2025-01-17T14:00:00Z

| D
a| 
* Fwd. Draft
* Aft. Draft
a|
* Draft measured at the forward end of the vessel  
* Draft measured at the after end of the vessel
a|
* 8.5 [m]
* 9.0 [m] 

| E
| Margin 
| adjusts the SOG relative to RPM. A positive margin value decreases SOG at the same RPM, while a negative value increases it.
| 5 [%]

| G
| Optimization Range
| Visually highlighted section on the route between Start and End WPTs
| -
|===

Route type, user-defined speed plan, and XTL (labeled as F in <<fig_inputs_for_opt>>) are defined below:

[cols="1,3,1", options="header"]
|===
| Name
| Description
| Example

| Route Type
| RL for Rhumb Line, GC for Great Circle
| RL

| Apply User's SPD Plan
| whether to apply the user-defined speed plan
| 6 [kn]

| Turn Radius
| Radius of the vessel’s turning circle during maneuvers
| 0.5 [NM]

| XTL (PORT/STBD)
| XTL – allowable deviation from planned route
| 0.5 [NM]
|===




=== Optimization Results

Once the optimization is completed, a summary of the results is displayed, as shown in <<fig_optimization_result>>.A.  
This includes FOC, average speed, and average RPM.  
If the calculation fails, a notification indicating the cause of failure is shown, as illustrated in <<fig_optimization_result>>.B, to help the operator correct the input parameters.

If more detailed information is needed, the operator can expand the view by clicking the toggle, as shown in <<fig_optimization_result_detail>>.A.  
To export the optimization result, press the [Export] button, as shown in <<fig_optimization_result_detail>>.B.  
The exported route file can be saved only to a connected USB memory device.

[[fig_optimization_result]]
.Optimization result
image::FUNCTIONAL_DESCRIPTION/optimization_calculation_result_v0.png[width=600,align="center"]

[[fig_optimization_result_detail]]
.Detailed optimization result
image::FUNCTIONAL_DESCRIPTION/optimization_calculation_result_detail_v0.png[width=600,align="center"]

If [Route Opt] is selected using the Oceanwise engine, the optimization result includes optimized waypoint positions and corresponding RPM commands, as shown in <<fig_route_optimization_result>>.  
Note that the waypoints may differ in both position and quantity from the original route.

[[fig_route_optimization_result]]
.Route optimization result
image::FUNCTIONAL_DESCRIPTION/route_optimization_calculation_result_v0.png[width=600,align="center"]

// HiNAS Control Standard
ifdef::build_target_std[]
Pressing the [Apply] button applies the optimization result to the HiNAS system, setting it as the monitoring route and enabling it for track control.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
Pressing the [Apply] button applies the optimization result to the HiNAS system, setting it as the monitoring route and enabling it for route tracking.
endif::build_target_nstd[]

[NOTE]
====
* If the user's speed plan is applied, the average RPM and average speed reflect the input values used in the calculation.  
* FOC is not affected by the user's speed plan.  
* If [Route Opt] is selected using the Oceanwise engine, the optimized WPTs are shared with the ECDIS.
====


=== Applied Route

Once the optimized route is applied, the relevant information is displayed on the [Applied Route] page, as shown in <<fig_applied_route>>.  
This page shows the selected optimization engine and optimization type in <<fig_applied_route>>.A.  
WPT information that the HiNAS system will follow (To WPT) is clearly presented, as shown in <<fig_applied_route>>.B.  
Note that information for the NWPT is shown in a dimmed style to distinguish it from the To WPT.

[[fig_applied_route]]
.Applied route
image::FUNCTIONAL_DESCRIPTION/applied_route_v0.png[width=600,align="center"]

Past WPTs are displayed only when [Display Past WPTs] is checked, as shown in <<fig_applied_route_feature>>.A.  
These past WPTs are shown in a subdued (barely visible) style.

The [Applied Route] page can also display Sub-WPTs, but they are hidden by default to reduce visual complexity.  
To view Sub-WPTs, use the [Sub-WPTs] toggle, as shown in <<fig_applied_route_feature>>.B.

[[fig_applied_route_feature]]
.Features of the Applied Route page
image::FUNCTIONAL_DESCRIPTION/applied_route_feature_v0.png[width=600,align="center"]



=== Reoptimization Operation

As explained in <<Re-optimization>>, weather forecasts are updated periodically.  
HiNAS enables operators to reflect the latest forecast data even after a mode has been activated by using the re-optimization function.  
This function can also be used to adjust the ETA after HiNAS modes have been initiated.

The [Reoptimization] button is available on the Applied Route page.  
When pressed, the Reoptimization page appears, as shown in <<fig_reoptimization_menu>>.

The start point for re-optimization is indicated as `2(1)` in <<fig_reoptimization_menu>>.A.  
This marks the first waypoint at which changes to RPM CMD or HDG CMD may occur based on the updated data.  
The `To WPT (To Sub-WPT)` for re-optimization is automatically selected by HiNAS.

The end point of re-optimization, shown in <<fig_reoptimization_menu>>.B, defines the last WPT included in the re-optimization range and can be modified by the operator.

ETA (UTC), shown in <<fig_reoptimization_menu>>.C, is set by default to the ETA from the original optimization but may be adjusted as needed.

Loading Condition and Margin settings, shown in <<fig_reoptimization_menu>>.E, can also be modified by the operator.

Once all required parameters are set, pressing the [Calculate] button initiates the re-optimization process.  
If the re-optimization is successful, a summary is provided as shown in <<fig_reoptimization_results>>.A.  
If it fails, a failure message along with the cause is displayed, as shown in <<fig_reoptimization_results>>.B.

[[fig_reoptimization_menu]]
.Reoptimization menu
image::FUNCTIONAL_DESCRIPTION/reoptimization_menu_v0.png[width=600,align="center"]

[[fig_reoptimization_results]]
.Reoptimization result
image::FUNCTIONAL_DESCRIPTION/reoptimization_results_v0.png[width=700,align="center"]

If the re-optimization result is applied, the affected WPTs are marked as `RE-OPT` on the Applied Route page.  
WPTs that were not included in re-optimization remain marked as `OPT`, as shown in <<fig_applied_route_after_reopt>>.

[[fig_applied_route_after_reopt]]
.Applied Route page after re-optimization
image::FUNCTIONAL_DESCRIPTION/applied_route_after_reopt_v0.png[width=400,align="center"]


[NOTE]
====
* The [Reoptimization] button is disabled and re-optimization is not available:
** during CAC mode  
** when no route is applied  
** when following the last WPT  
====