// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

==== Vision Based Object Detection

The goal of target detection is to establish detection criteria to avoid obstacles by recognizing ships or boats in a marine environment through deep learning.
To use this function, an object detection deep learning model is trained using more than tens of thousands of image datasets taken from ships.
To train the deep learning-based network, we deployed CNN based “YOLO network”.
Once the images are received from camera device, the input(image) is preprocessed to fit the trained deep learning network. And by slicing the wide images to small parts, the deep learning network find the objects.

[[fig_concept_of_vision_object_detection]]
.Concept of vision-based object detection
image::FUNCTIONAL_DESCRIPTION/concept_of_vision_object_detection_v0.png[align="center",500,300]

[[fig_vision_based_object_detection_result]]
.Vision-based object detection result
image::FUNCTIONAL_DESCRIPTION/vision_based_object_detection_result_v0.png[align="center",500,300]


===== Data Training
The purpose of data training is to improve performance of object detection in maritime scene.
As shown in <<fig_taxonomy_for_object_detection>>, the trained model classifies object to three classes consisting of vessel, boat and navigation aid. 


[[fig_taxonomy_for_object_detection]]
.Taxonomy for object detection
image::FUNCTIONAL_DESCRIPTION/taxonomy_for_object_detection_v0.png[align="center",500,300]


The data driven model deployed in HiNAS Control is deep learning based model. To train model, the required number of minimum data is about three thousand (3,000) but AVIKUS has more than seven hundred thousand (700,000) image data.
For training the data driven model, train – validation – test data is splitted by “9: 1: New Income Data” ratio. For the data training whole collected data is used for training and validation data set.
The newly transmitted data from the onboard is tested for validation of the existing trained model, and after testing it is used to train the model to improve cognitive performance.

[[fig_data_split_rule_for_training]]
.Data split rule for training
image::FUNCTIONAL_DESCRIPTION/data_split_rule_for_training_v0.png[align="center",500,300]



===== Model Evaluation
To evaluate model performance, object detection module deploys mAP(mean Average Precision)metric.
That means the mean of model prediction precision for each class. 
The precision in object detection is defined as the ratio of “True Positive(TP) / (True Positive + False Positive), which means the ratio of the number of true object detection and total number of object detection. To identify the “True Positive” for object detection, IOU(Intersection Over Union) is used.
The IOU measures the overlapping area between the predicted object area and ground-truth object area.
Once the IOU is over the certain threshold, that prediction is considered true positive.
In HiNAS Control model training, IOU threshold is 0.3. 
If the trained model mAP performance is better than the former model, that model is accepted as a new model.
And the model acceptance criteria in mAP performance is set to be 0.5. 
<<fig_model_training_result>> shows the model train result for object detection. 

[[fig_model_training_result]]
.Model training result
image::FUNCTIONAL_DESCRIPTION/model_training_result_v0.png[align="center",500,300]



===== Model Deploy
Once model is better model is trained, that model should be deployed.
The model is updated to the HiNAS Control product as per the model deployment policy described below.

[[table-data-driven-model-deployment-policy]]
.Data driven model deployment policy

* Data input must be inspected by authorized personnel to check the data quality and integrity.
* The object detection module should be retrained every six months and its performance should be traceable. 
* The improved inference model should be updated to the product with software minor version release or six months, whichever comes earlier.
* The improved model/module should be updated if and only if the product shows poor performance (critical comment by customer) or by owner’s requirement with extra cost (1 update free).


[[fig_model_version_record]]
.Model version record
image::FUNCTIONAL_DESCRIPTION/model_version_record_v0.png[align="center",500,300]