// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Ship Status and Environment 

HiNAS provides sensor data along with its status. Additionally, if multiple sensor channels are available, the operator can select the desired source.

As shown in the figure below, the Ship Status displays the ownship’s sensor data.
The Ship Status panel is divided into three columns:

* First column: Data type
* Second column: Selected source by the operator
* Third column: Sensor value

Environment provides wind and current information:

* First column: Data type
* Second column: T (True)
* Third column: Sensor value (or True value)

[[fig_ship_status_and_env]]
.Function activation menu
image::FUNCTIONAL_DESCRIPTION/ship_status_and_environment_v0.png[align="center",300,200]

The color of indicators and sensor labels in the Ship Status and Environment panels changes according to the status of the sensor data.
The table below summarizes the corresponding color codes and their meanings.

[[table_sensor_status_color_indicators]]
.Sensor status color indicators
[cols="2,2,2,2,2", options="header"]
|===
| Color of data type
| Color of data source
| Color of sensor data
| format
| Description                  

| Green | Green | Green | 888.88 | Sensor is normal
| Green | Green | Green | `**`   | No signal
| Red   | Red   | Red   | `**`   | Data is invalid
|===