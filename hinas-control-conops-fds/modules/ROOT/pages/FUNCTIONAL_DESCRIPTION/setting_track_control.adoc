// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

==== Track Control Setting

Track Control Setting page, as shown in the figure below, provides access to configuration options for the following parameters:

* Route Tracking starting conditions
* Limits  
* Operational range  
* Mode selection  
* Loading condition selection  

[[fig_track_control_setting_menu]]
.Track control setting menu  
image::FUNCTIONAL_DESCRIPTION/track_control_setting_page_v1.png[align="center",600,400]

The following table describes the function, recommended value, and setting range for each track control setting parameter.

[[table_track_control_setting_menu]]
.Track control setting parameters  
[cols="2,5,2,2", options="header"]
|===
| Parameter  
| Description  
| Recommended Value  
| Setting Range  

| Preset Enter Distance [NM]  
| Route Tracking can only be initiated when the distance from the starting waypoint is less than the specified value.
| 1  
| 0 – 1  

| Preset Enter Angle [deg]  
| Route Tracking can only be initiated when the angle between own ship and the track is less than the specified value.
| 15  
| 1 – 60  

| Preset Time to WOL [s]  
| Route Tracking can only be initiated when the time to the Waypoint of Leg (WOL) is less than the specified value.
| 30  
| 0 – 60  

| Off Heading Limit [deg]  
| Maximum heading control angle for straight-track navigation provided to the autopilot (only applicable to and operational with Yokogawa autopilot).  
| 60  
| –  

| Max Rudder [deg]  
| Maximum rudder angle provided to the autopilot (only applicable to and operational with Yokogawa autopilot).  
| 15  
| –  

| ROT Max [deg/min]  
| Maximum rate of turn (ROT) value that can be commanded in Turning Mode.  
| –  
| –  

| Control Mode  
| Control mode selection: HS Mode (Heading and Speed Control), H Mode (Heading Control only).  
| HS Mode  
| –  

| RPM Range [RPM]  
| RPM range permitted for HiNAS Control, as defined by the BMS maker.  
| –  
| –  

| Loading Condition  
| Ownship loading condition (Ballast / Half / Full). This parameter should be updated regularly by the navigator.  
| –  
| –  
|===