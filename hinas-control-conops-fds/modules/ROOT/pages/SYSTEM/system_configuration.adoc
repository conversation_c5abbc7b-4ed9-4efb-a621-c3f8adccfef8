// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images

=== System Configuration
This section deals with the basic system description such as data input, system specification and system configuration.

==== Hardware
The hardware configuration is illustrated in <<fig_interace_diagram_of_hinas_control>>.  
As shown in the figure, the navigational equipment required for HiNAS is individually connected to a Serial-to-Ethernet converter and a network switch.  
Sensors (IEC 61162-1 network) are interfaced through a firewall.  
Connections to the autopilot and BMS are bidirectional, enabling both the transmission and reception of control commands.


==== Requirements for Interfaced Devices
To ensure safe and autonomous navigation, all interfaced devices must provide sufficient accuracy and reliability.  
The specific requirements for HiNAS interfaced devices are listed in <<table_requirement_of_interfaced_devices>>.

==== Software
The software architecture is categorized into three level.
Frontend component, backend component and middleware component.
Each component plays role as follows.

* Frontend: HMI component for user action. Displays data and users can activate and set the control function
* Backend: Data handling component for frontend. Storing, streaming and dump data by using data base system 
* Middleware: Data handling from the external devices. Deals with data interface health status and streaming data from the external devices. Also, send control command to the control unit.

[[fig_sw_conf_of_hinas]]
.Software configuration of HiNAS
image::SYSTEM/sw_conf_of_hinas_v0.png[align="center",700,400]