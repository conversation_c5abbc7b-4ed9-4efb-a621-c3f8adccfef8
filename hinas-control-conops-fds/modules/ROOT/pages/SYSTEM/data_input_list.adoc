// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images

=== Data Input List

The input data used in HiNAS  Control is being collected in various individual navigation equipment such as GPS, speed log and some other devices.
There are about 30 raw data acquired almost every second (it may vary with system), from individual sensor equipment and sensor network (IEC 61162-1 network), and they can be added or subtracted according to the specifications of the ship (single/twin screw).
The detailed data information is listed in <<table_input_data_list_of_hinas>>.  


[[table_input_data_list_of_hinas]]
.Input data list of HiNAS
[cols="3,3,2", options="header"]
|===
| Source | Description | Input Category (Critical/Auxiliary)

| GPS | Latitude | Critical
| GPS | Longitude | Critical
| GPS | UTC | Critical
| GPS | Local time zone | Auxiliary
| GPS | Course over ground | Critical
| GPS | Speed over ground | Critical
| Echo Sounder | Water depth | Auxiliary
| Speed Log | Longitudinal water speed | Critical
| Speed Log | Transverse water speed | Critical
| Gyro | Heading angle | Critical
| Gyro | Rate of turn | Critical
| Rudder Indicator | Set rudder angle | Critical
| Rudder Indicator | Actual rudder angle | Critical
| Anemometer | Wind speed | Critical
| Anemometer | Wind angle | Critical
| Main Engine (Shaft power meter) | Main engine rpm | Auxiliary
| Main Engine (Shaft power meter) | Main engine power | Auxiliary
| Main Engine (Shaft power meter) | Main engine torque | Auxiliary
| EO Camera | Camera image | Auxiliary
| IR Camera | Camera image | Auxiliary
| AIS | Target ship position | Critical
| AIS | Target ship speed | Critical
| AIS | Target ship course | Critical
| AIS | Target ship type | Auxiliary
| AIS | Target ship id | Auxiliary
| Radar | Target object distance | Critical
| Radar | Target object bearing angle | Critical
| Radar | Target object speed | Critical
| Radar | Target object id | Auxiliary
| Radar | Target object CPA | Auxiliary
| Radar | Target object DCPA | Auxiliary
| ECDIS | Planned route information | Critical
| Speed (RPM) Optimizer | Optimized route information | Auxiliary
| Autopilot | Set heading | Critical
| Autopilot | Auto mode ready | Critical
| Autopilot | HiNAS mode activation | Critical
| BMS | Set rpm | Critical
| BMS | Auto mode ready | Critical
| BMS | HiNAS mode activation | Critical
| Loading Computer | Forward draft | Auxiliary
| Loading Computer | After draft | Auxiliary
| Loading Computer | Mid. starboard draft | Auxiliary
| Loading Computer | Mid. portside draft | Auxiliary
|===
