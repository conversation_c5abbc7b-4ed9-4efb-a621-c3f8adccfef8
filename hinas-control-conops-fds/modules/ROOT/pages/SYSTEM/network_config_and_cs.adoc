// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images


=== Network Configuration and Cyber Security

==== Network Configuration
<<fig_interace_diagram_of_hinas_control>> shows the network configuration of HiNAS Control.
Basically, all the individual sensor equipment is connected by NMEA 0183 protocol serial - IEC 61162-1 Network and the connection for the control command or alarm is connected through physical firewall device. 

==== Cyber Security
For the cyber security, HiNAS  Control is equipped with four devices for software wise and hardware wise view. 
Below <<table_cyber_security_method_for_hinas>> describes the methods used in HiNAS  Control configuration.
The five methods are:

* Hardware firewall
* Software firewall
* USB port blocking device
* LAN port blocking device
* 3rd party management policy

From using those five methods, HiNAS Control ensures cyber security level of *Cyber Security System* defined by ABS. 

[[table_cyber_security_method_for_hinas]]
.Cyber security methods for HiNAS
[cols="2,2,4", options="header"]
|===
| Device
| Location
| Purpose 

| Hardware Firewall
| Between Server and Serial to Ethernet Converter
| To ensure the cyber security for outward signal from HiNAS (control command, alert)

| Malware scanner/ Software firewall
| Inside the server/ client PC
| To ensure cyber security inward signal to HiNAS from external connection 

| USB port blocking device
| Servers
| To ensure physical cyber security related to USB related devices

| Third party management policy
| Third officer is responsible for third party management
| Supervise third party engineering maintanence
|===