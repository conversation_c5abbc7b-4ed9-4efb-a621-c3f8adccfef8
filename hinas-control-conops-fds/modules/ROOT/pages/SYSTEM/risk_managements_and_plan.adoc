// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images


=== Risk Management and Plan
As recommended from ABS, the risk assessment is conducted according to the method presented by Osrisis A. Vales Banda.
Below contents are the steps for the risk assessment. 


==== Accident Types and Identification of Hazard
Since the function of HiNAS Control is for autonomous voyaging and collision avoidance, the possible accidents and hazards are limited to the collision to certain objects or grounding.
The possible accident and hazardas are listed as below <<table_identified_acciendent_and_hazard_by_hinas>>.

[[table_identified_acciendent_and_hazard_by_hinas]]
.Identified accident and hazard by HiNAS
[cols="2,6", options="header"]
|===
| Accident | Hazards

| 1. Collision with another vessel
a|
* H1. Object detection sensor error (AIS, radar, camera)  
* H2. S/W failure  
* H3. H/W (server/client processor) failure  
* H4. Control unit (autopilot/BMS) failure  
* H5. Position/state reference equipment fault (GPS, speed log …)  
* H6. Heavy weather / adverse current  
* H7. HiNAS shut down

| 2. Collision with a small moving target (e.g., canoe, surfboard)
a|
* H1. Object detection sensor error (AIS, radar, camera)  
* H2. S/W failure  
* H3. H/W (server/client processor) failure  
* H4. Control unit (autopilot/BMS) failure  
* H5. Position/state reference equipment fault (GPS, speed log …)  
* H6. Heavy weather / adverse current  
* H7. HiNAS shutdown

| 3. Collision with a fixed object (e.g., buoys, beacons)
a|
* H1. Object detection sensor error (AIS, radar, camera)  
* H2. S/W failure  
* H3. H/W (server/client processor) failure  
* H4. Control unit (autopilot/BMS) failure  
* H5. Position/state reference equipment fault (GPS, speed log …)  
* H6. Heavy weather / adverse current  
* H7. HiNAS shutdown

| 4. Grounding
a|
* H1. Object detection sensor error (AIS, radar, camera)  
* H2. S/W failure  
* H3. H/W (server/client processor) failure  
* H4. Control unit (autopilot/BMS) failure  
* H5. Position/state reference equipment fault (GPS, speed log …)  
* H6. Heavy weather / adverse current  
* H7. HiNAS shutdown
|===


==== Mitigation Strategy for Each Hazard
In this section, the detailed description of hazards identified in previous section and proper mitigation strategy is provided.
Since the concept operation of HiNAS is returning the control authority to the user if the one of hazard occur, it can be concluded that HiNAS is not a potential cause of accident nor a leading factor of accident. 

[[table_hazard_object_detection]]
.Hazard 1. Object detection sensor error (AIS, radar, camera)
[cols="2,4,2,2", options="header"]
|===
| Hazard
3.+|
H1. Object detection sensor error

| Hazard effect / Description
3.+|
In case of object detection sensor error, the information about objects around the vessel is not reliable and thus the vessel may not be able to navigate safely and avoid collisions with moving objects according to the rules of the road or collisions with fixed objects.
Since the hazard may cause negative impact on people and ship, HiNAS requires the operators to monitors system periodically. And we recommend the operators to conduct maintenance periodically, also it is notified to the ship owner that the responsibility of accident caused by this hazard belongs to the ship operator.
If both of AIS and radar system are turned to be error, HiNAS returns the control authority to user not to lead accident.


| Causal factors
3.+a|
* Loss of power 
* Cable disconnection 
* Dirt
* Incorrect sensor setting or positioning of sensors
* Targets impossible to detect
* Complete equipment failure
* Overheating


| Mitigation strategy | Item               | Cost / difficulty | Priority (1-4)
|   | Autonomous integrity monitoring      | Low               | 2
|   | Proper cooling system                | Medium            | 3
|   | Proper maintenance program           | Low               | 3
|   | Proper control authority take over   | Low               | 3
|   | Proper commissioning test procedure  | Medium            | 3
|   | Proper object detection device maintenance   | Low       | 3
|   | UPS (radar)                          | Low               | 3
|===


[[table_hazard_software_failure]]
.Hazard 2. S/W failure
[cols="2,4,2,2", options="header"]
|===
| Hazard
3.+|
H2. S/W failure

| Hazard effect / Description
3.+|
In the event of a software failure, the vessel may be unable to navigate safely or comply with the rules of the road.  
Such a failure can result in a collision, allision, grounding, or bottom touching.  
Because this hazard can negatively impact both the people and the vessel, HiNAS Control requires operators to monitor the system periodically.  
Operators are also advised to monitor the decisions made by the software, and the ship owner must be notified that any accidents resulting from this hazard are the responsibility of the ship operator.  
If any software module returns an error, HiNAS Control will transfer control authority back to the operator to prevent accidents caused by failure conditions.

| Causal factors
3.+a|
* Architecture design failure
* Loss of power 
* Algorithm failure 
* S/W version incorrect
* Computer failure 
* Unknown situation to program
* Overheating

| Mitigation strategy | Item                                   | Cost / difficulty | Priority (1-4)
|   | Through planning, testing and commissioning software     | High   | 4
|   | UPS                                                      | Low    | 3
|   | Proper software version control                          | High   | 3
|   | Proper cooling system for computer                       | Low    | 2
|   | Proper system design and maintenance process             | High   | 3
|   | Proper control authority take over                       | Low    | 3
|   | S/W health monitoring (monitoring of S/W abnormal)       | Medium | 3
|   | Proper QA test                                           | High   | 4
|===


[[table_hazard_hardware_failure]]
.Hazard 3. H/W failure
[cols="2,4,2,2", options="header"]
|===
| Hazard
3.+|
H3. H/W failure

| Hazard effect / Description
3.+|
In case of a hardware failure a vessel may not be able to navigate safely or follow the rules of the road.
Hardware failure may lead to collision, allision, grounding or bottom touching.
Since the hazard may cause negative impact on people and ship, HiNAS Control requires the operators to monitors system periodically.
And we recommend the operators to monitors the status of H/W, also it is notified to the ship owner that the responsibility of accident caused by this hazard belongs to the ship operator.
If the hardware is in failure, the control unit such as BMS and auto pilot should take over the control authority according to the control concept.

| Causal factors
3.+a|
* Loss of power
* Unknown failure of H/W
* Overheating
* Connection failure 


| Mitigation strategy | Item                                   | Cost / difficulty | Priority (1-4)
|   | Through planning, testing and commissioning software     | High   | 4
|   | UPS                                                      | Low    | 3
|   | Proper software version control                          | Low    | 3
|   | Proper cooling system for computer                       | High   | 2
|   | Proper system design and maintenance process             | High   | 3 
|   | Proper control authority take over                       | Low    | 3 
|===


[[table_hazard_control_unit_failure]]
.Hazard 4. Control unit (autopilot/BMS) failure
[cols="2,4,2,2", options="header"]
|===
| Hazard
3.+|
H4. Control Unit (autopilot/BMS) failure

| Hazard effect / Description
3.+|
If control unit equipment fails or gives incorrect information, vessels cannot navigate safely.
This may lead to collision with a fixed object or moving object, grounding. 
Since the hazard may cause negative impact on people and ship, HiNAS Control requires the operators to monitors system periodically.
And we recommend the operators to monitors alarm of S/W.
Also, if the position signal failed, HiNAS return control authority to the operator, and it is notified to the ship owner that the responsibility of accident caused by this hazard belongs to the ship operator.
If any control unit returns the alarm or error signal to the HiNAS, the system returns the control authority to the user not to lead accident.

| Causal factors
3.+a|
* Loss of power
* Equipment malfunction
* Connection failure (disconnect)
* Inappropriate maintenance

| Mitigation strategy | Item                                   | Cost / difficulty | Priority (1-4)
|   | Through installation and commissioning of equiptment set | Medium | 3
|   | Appropriate and continuoust onboard maintenace program   | Low    | 3
|   | Autonomous integrity monitoring                          | Low    | 2
|   | Proper control authority take over                       | Low    | 3
|   | UPS                                                      | Low    | 2 
|===


[[table_position_state_reference_equipment_fault]]
.Hazard 5. Position reference equipment failure
[cols="2,4,2,2", options="header"]
|===
| Hazard
3.+|
H5. Position reference equipment failure

| Hazard effect / Description
3.+|
If the position reference equipment fail or give incorrect information, vessels cannot navigate safely.
This may lead to collision with a fixed object or moving object, grounding. 
Since the hazard may cause negative impact on people and ship, HiNAS Control requires the operators to monitors system periodically.
And we recommend the operators to monitors alarm of S/W. Also, if the position signal failed, HiNAS return control authority to the operator, and it is notified to the ship owner that the responsibility of accident caused by this hazard belongs to the ship operator.
If the equipment for ship state reference measurement is in failure, HiNAS Control returns the control authority to the user not to lead accident. 


| Causal factors
3.+a|
* Loss of power
* Satellite position system jamming
* Satellite position system spoofing
* Effect of weather (rain, fog)
* Targets impossible to detect
* Dirt (on local position system sensor)
* Equipment malfunction
* Inappropriate maintenance

| Mitigation strategy | Item                                   | Cost / difficulty | Priority (1-4)
|   | Sensor redundancy                                        | High   | 3
|   | Combination of local sensor (gyro)                       | High   | 3
|   | Jamming detection/ anti-jamming func.                    | Low    | 2
|   | Thorough installation and commissioning of equipment set | Medium | 3
|   | Appropriate and continuous on-board maintenance program  | Low    | 2 
|   | Autonomous integrity monitoring                          | Low    | 2 
|   | Proper control authority take over                       | Low    | 2 
|   | UPS                                                      | Low    | 2 
|===



[[table_heavy_weather_adverse_current]]
.Hazard 6. Heavy weather/ adverse current
[cols="2,4,2,2", options="header"]
|===
| Hazard
3.+|
H6. Heavy weather/ adverse current

| Hazard effect / Description
3.+|
If the weather or sea conditions caused by wind, gusts, waves, swell, thunder or weather fronts are too heavy for the vessel she may come to the limits of her ability to manoeuvre and steer in a controlled way. This may lead to collision with a fixed object, allision with a pier, grounding, or bottom contact. 
Since the hazard may cause negative impact on people and ship, HiNAS Control requires the operators to monitors system periodically. Also, if the weather condition or current condition is outside the operation envelope, HiNAS return control authority to the operator not to lead accident, and it is notified to the ship owner that the responsibility of accident caused by this hazard belongs to the ship operator. 


| Causal factors
3.+a|
* Unexpected change of condition. 
* Lack of operational limits or incorrect operational limits 
* Poor weather monitoring
* Local conditions differ from the surrounding areas
* Inaccurate weather forecast


| Mitigation strategy | Item                                   | Cost / difficulty | Priority (1-4)
|   | Correctly set and followed operational limits            | Low               | 4
|   | Weather routing and constant weather and sea state monitoring | Medium       | 3
|   | Constant monitoring and predictios of vessels' capacity       | Low          | 2
|   | Proper control authority take over                       | Low               | 3
|===


[[table_hinas_control_shut_down]]
.Hazard 7. HiNAS Control shut down
[cols="2,4,2,2", options="header"]
|===
| Hazard
3.+|
H7. HiNAS Control Shut down

| Hazard effect / Description
3.+|
If the HiNAS  Control program is shut down, it may lead to collision with a fixed object, allision with a pier, grounding, or bottom contact. 
Since the hazard may cause negative impact on people and ship, HiNAS Control requires the operators to monitors system periodically. Also, if the system is shut down with any reason such as shut down by user or power loss, HiNAS return control authority to the operator not to lead accident, and it is notified to the ship owner that the responsibility of accident caused by this hazard belongs to the ship operator. 


| Causal factors
3.+a|
* Power loss 
* Manual shut down by operator 


| Mitigation strategy | Item                           | Cost / difficulty | Priority (1-4)
|   | UPS                                              | Low               | 3
|   | Proper control authority take over               | Low               | 3
|===



==== Distribution of the Safety Control Types Used
From the previous section, the safety control strategy has been analyzed.
Below <<table_distribution_of_the_safety_control_types_used>> is the distribution of safety control types used in HiNAS. 
From the table, it can be concluded that HiNAS Control has twenty-five safety control strategy.
And those are composed of 11.5% of priority 4 strategy, 68.5% of priority 3 strategy and 20% of priority 2 strategy.

[[table_distribution_of_the_safety_control_types_used]]
.Distribution of the safety control types used
[cols="4,2", options="header"]
|===
| Safety control mitigation approach
| Safety controls defined 

| Attempt to completely eliminate the hazard (priority 4)
| 4

| Attempt to reduce the likelihood that the hazard will occur (priority3)
| 26

| Attempt to reduce the likelihood that the hazard results in an accident (priority 2)
| 7

| Attempt to reduce the damage if the accident occurs (priority 1)
| 0
|===


==== Risk Mitigation Plan

Based on the accidents and hazards identified in the previous section, the table below describes the detailed failure modes and corresponding risk mitigation actions. <<table_risk_mitigation_plan_for_failure_mode>>

[[table_risk_mitigation_plan_for_failure_mode]]
.Risk mitigation plan for failure modes
[cols="^2,^2,3,4", options="header"]
|===
| Failure Component
| Failure Mode
| Potential Consequence After Failure
| Risk Mitigation (Action)

| Object Detection Sensor
| Camera Power Loss
| Camera data unavailable
a|
* Check power connection

| 
| Camera Module Overheating
| Camera data unavailable
a|
* Restart the module  
* Check the cooling system

| 
| Camera Module Disconnection
| Camera data unavailable
a|
* Restart the module  
* Inspect the connection

| 
| Dirt on Camera Lens
| Camera data unavailable
a|
* Clean the lens  
* Conduct periodic maintenance

| 
| AIS Disconnection / Power Loss
| AIS target data unavailable
a|
* Perform regular maintenance  
* Check the connection  
* Issue repeated warnings; stop CAA if needed

| 
| Radar Disconnection / Power Loss
| Radar data unavailable
a|
* Perform regular maintenance  
* Check the connection  
* Issue repeated warnings; stop CAA if needed

| Software Module
| Algorithm Error
| Control command cannot be issued to the control unit
a|
* Report the issue to the manufacturer  
* System alert triggered via software health check  
* Transfer control authority to the operator with alert

| 
| Unexpected Situation
| Decreased object detection accuracy
a|
* Report the issue to the manufacturer

| Hardware Module
| Power Loss
| All processes and functions inoperative
a|
* UPS provides temporary backup  
* Transfer control authority to the operator with alert

| 
| Unknown Hardware Failure
| All processes and functions inoperative
a|
* Transfer control authority to the operator with alert

| 
| Connection Failure
| Functionality loss depends on signal type
a|
* Auxiliary Signal: Show alert, check connection, restart  
* Critical Signal: Transfer control authority to operator with alert, check connection, restart

| Control Unit
| Power Loss
| Control commands cannot be issued
a|
* Transfer control authority to the operator with alert

| 
| Malfunction
| Control commands cannot be issued
a|
* Transfer control authority to the operator with alert

| 
| Disconnection
| Control commands cannot be issued
a|
* Check the connection  
* Transfer control authority to the operator with alert

| Position Reference Equipment
| Power Loss
| All processes and functions inoperative
a|
* Check equipment status  
* Transfer control authority to the operator with alert

| 
| Signal Freeze / Disconnection
| Signal degraded or lost; system becomes non-functional
a|
* Transfer control authority to the operator with alert

| HiNAS System
| System Shutdown
| Control commands not issued
a|
* Transfer control authority to the operator and Autopilot/BMS with alert
|===


==== Risk Mitigation by Switch
HiNAS assigns control commands to the autopilot, making the safety plan critically important.

For autopilots already equipped with Track Control System (Route Tracking System) via ECDIS, a hardware switch is provided to toggle control between ECDIS and HiNAS Control.
If the autopilot is not connected to a separate Route Tracking System from ECDIS, the control mode can be changed using the switch on the autopilot itself.

In a fallback condition, the autopilot automatically switches to HC (HiNAS Control) mode, and the heading command is maintained based on the last command issued by HiNAS.

For the BMS, the `AUTO MODE` button on the BMS mimic panel activates automatic control.  
Auto control from HiNAS can be canceled by toggling off the Track Control switch.

In a fallback condition, the BMS switches to stand-alone mode, and the RPM command remains fixed at the last value issued by HiNAS.

To manually abort auto mode on the BMS, the user must press the `AUTO MODE` button on the panel (see <<fig_nabtesco_bms_panel>>).  
This deactivates the mode when the system is operating in HS mode, as explained in <<Concept of Control Mode>>.