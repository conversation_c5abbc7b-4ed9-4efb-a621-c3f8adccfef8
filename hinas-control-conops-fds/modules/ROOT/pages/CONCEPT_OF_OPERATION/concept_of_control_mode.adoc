// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Control Mode

The control functions of HiNAS are managed through the autopilot for heading control and the BMS for speed control.
Since control requirements may vary depending on the user or ship owner, HiNAS Control supports two control modes:

* HS Mode: controls both heading and speed.
* H Mode: controls only heading via autopilot.

The control mode can be selected on the settings page of HiNAS Control as shown in <<table_track_control_setting_menu>>.
It can only be changed when Route Tracking mode is deactivated.

[[concept-of-control-mode]]
.Concept of control modes
[cols="2,4", options="header"]
|===
| Control Mode
| Description

| HS Mode (Default)
| controls both heading and speed using the autopilot and BMS.

| H Mode
| controls only heading using the autopilot.
|===