// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Target Associator

The target associator integrates and processes data from multiple sensors, including AIS, RADAR, and cameras, to create a comprehensive and real-time understanding of the vessel's surroundings.
Target associator consists of submodules Data processing, Sensor tracking, Association and Track management, and the functions of each submodule are as follows.

==== Data Processing
Data from multiple sensors are pre-processed by data processing module. Inputs in this submodule are own ship data, AIS, RADAR, and inference data by EO, IR cameras. The processing for each input data is as follows.

===== Ownship Data

* Verify that the input is null value.
* Verify that parsed data is updated data.

===== AIS

* Verify that the input is null value.
* Verify the valid range of latitude and longitude.
* Verify that parsed data is updated data.
* Set EO and IR coordinate of super wide view calculated by projection model.
 
===== RADAR

* Verify that the input is null value.
* Verify the valid range of distance and bearing.
* Verify that parsed data is updated data.
* Set EO and IR coordinate of super wide view calculated by projection model.
 
===== Inference Data from CBD Module
* Verify the valid range of attributes values.
* Policies of Inference measurement activation.
** Activate all inference data
** Inactivate overlapped inference data
** Inactivate all inference data
** Inactivate fully occluded inference data
* Filtering criteria
** Size and shape of bbox.
** Image coordinate of bottom of bbox.
* Set EO or IR coordinate of super wide view.
* Set class information of detected object.

==== Sensor Tracking

Pose informations of onw ship, AIS-based tracks (tracks containing AIS), and RADAR-based tracks (tracks containing RADAR but not AIS) are predicted based on existing kinematics using sensor informations of previous frames.

==== Association

The sensor measurement associator determines the association between existing “Fusion Tracks” and “Measurement” when sensor measurement comes in.
This module matches measurements to the corresponding tracks based on cost minimization, then verifies the validity of matching.
This procedure depends on the type of track and measurement.
Once the matching verified, the track would be updated using the matched measurement based on Extended Kalman Filter (EKF).
The detailed policies of the association are described in <<table_sensor_fusion_type_and_policy>> & <<target-association-policy>>.

==== Track Management

Track management module controls the state of tracks containing age, association type, and risk level.
This module determines the initiation and termination of tracks, and detailed policies of initiation and termination are described in <<Target Association>> and <<Target Association Policy>>, respectively. 
In the case of tracks associated by multiple sensors, the association type could be adjusted when the measurement has not been associated for a while. It also determines the risk level of each track based on TCPA and DCPA. 


==== Target Association Policy

<<table_sensor_fusion_type_and_policy>> describes the types of associated targets applied and their corresponding policies.

In particular, targets detected solely by the camera device (Association Type 4) have low accuracy in estimating distance and bearing.
Such targets only trigger a caution-level alert if they are detected below the horizon.

<<<

[[table_sensor_fusion_type_and_policy]]
.Target association type and policy
[cols="^1,^2,^2,^2,^2,^2,^2,^2,^2,^2", options="header"]
|===
| Association type
| Device 
| Position priority source
| Speed priority source
| Course priority source
| Bearing priority source
| Camera Display(O,X)
| Map Display(O,X)
| Input for CA
| Alert level when danger

| 1
| AIS
| AIS
| AIS
| AIS
| AIS
| O
| O
| O
| Warning

| 2
| Radar
| Radar
| Radar
| Radar
| Radar
| O
| O
| O
| Warning


| 3
| AIS + Radar
| AIS
| AIS
| AIS
| AIS
| O
| O
| O
| Warning

| 4
| Camera
| -
| -
| -
| -
| O
| X
| X
| Caution

| 5
| AIS + Cam
| AIS
| AIS
| AIS
| AIS
| O
| O
| O
| Warning

| 6
| Radar + Cam
| Radar
| Radar
| Radar
| Radar
| O
| O
| O
| Warning


| 7
| AIS + Radar + Camera
| AIS
| AIS
| AIS
| AIS
| O
| O
| O
| Warning
|===

Target associator integrates and processes data from multiple sensors, including AIS, RADAR, and cameras, to create a comprehensive and real-time understanding of the vessel's surroundings.
Target associator consists of submodules Data processing, Sensor tracking, Association and Track management, and the functions of each submodule are as follows.

For target association, HiNAS follows the policy described in <<target-association-policy>>.
The table outlines the target association rules when a new target is detected, based on AV(top view or front view), ACP, and PS used if the target is associated.

<<<
    
[[target-association-policy]]
.Target Association Policy
[cols="1,1,2,2,2"]
|===
2+|  
3+| New Target Measurement

2+| 
| AIS
| Radar
| Cam

.9+| Existing Track
| AIS
a|
* AV: Top view
* ACP: MMSI and ship name
* PS: AIS
a| 
* AV: Top view
* ACP: Distance, COG, and SOG
* PS: AIS
a| 
* AV: Front view
* ACP: Pixel distance
* PS: AIS

| Radar
a|
* AV: Top view
* ACP: Distance, COG, and SOG
* PS: AIS
a| 
* AV: Top view
* ACP: Distance, COG, and SOG
* PS: Radar
a| 
* AV: Front view
* ACP: Pixel distance
* PS: Radar

| AIS + Radar
a|
* AV: Top view
* ACP: MMSI and ship name
* PS: AIS
a| 
* AV: Top view
* ACP: Distance, COG, and SOG
* PS: AIS
a| 
* AV: Front view
* ACP: Pixel distance
* PS: AIS

| CAM
a|
* AV: Front view
* ACP: Pixel distance
* PS: AIS
a| 
* AV: Front view
* ACP: Pixel distance
* PS: Radar
a| 
* AV: Front view
* ACP: Pixel distance
* PS: Cam

| AIS + CAM
a|
* AV: Top view
* ACP: MMSI and ship name
* PS: AIS
a| 
* AV: Top view
* ACP: Distance, COG, and SOG
* PS: AIS
a| 
* AV: Front view
* ACP: Pixel distance
* PS: AIS

| Radar + CAM
a|
* AV: Top view
* ACP: Distance, COG, and SOG
* PS: AIS
a| 
* AV: Top view
* ACP: Distance, COG, and SOG
* PS: Radar
a| 
* AV: Front view
* ACP: Pixel distance
* PS: Radar

| AIS + Radar + CAM
a|
* AV: Top view
* ACP: MMSI and ship name
* PS: AIS
a| 
* AV: Top view
* ACP: Distance, COG, and SOG
* PS: AIS
a| 
* AV: Front view
* ACP: Pixel distance
* PS: AIS 
|===

<<<