// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Starting and Failure Conditions

==== Starting Condition
// HiNAS Control Standard
ifdef::build_target_std[]
All starting conditions must be satisfied before Route Tracking or Collision Avoidance Assistance/Control (CAA/CAC) functions can be activated. These conditions ensure safe and reliable operation of the autonomous navigation system.
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
All starting conditions must be satisfied before Route Tracking or Collision Avoidance Assistance/Control (CAA/CAC) functions can be activated. These conditions ensure safe and reliable operation of the autonomous navigation system.
endif::build_target_nstd[]

===== Starting Conditions for Route Tracking

[cols="6,10,5", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | All HiNAS software modules must function properly for Route Tracking activation. System integrity check is performed during startup. |
| Position Sensor | Position data is mandatory for Route Tracking. GPS or other positioning systems must provide valid position information. |
| Heading Sensor | Heading data is mandatory for Route Tracking. Gyrocompass or magnetic compass must provide accurate heading information. |
| Speed Sensor | SOG (Speed Over Ground) and STW (Speed Through Water) data are required to activate Route Tracking for proper speed control. |
| Autopilot Mode | Autopilot must be in Heading Control mode (Tokyo-keiki: HC mode/ Yokogawa: AUTO mode) to enable Route Tracking integration. |
| Autopilot Connection | Autopilot connection is required for Route Tracking. Communication interface must be established and verified. |
| Autopilot Signal Validity | Valid autopilot signal is required for Route Tracking. Signal quality and data integrity must meet operational standards. |
| BMS Ready | In HS-Mode, Bridge Management System (BMS) must be in ready status to activate Route Tracking for engine control integration. | In HiNAS HS-Mode only
| BMS Signal Validity | In HiNAS HS-Mode, a valid BMS signal is required for Route Tracking to ensure proper engine control communication. | In HiNAS HS-Mode only
| Own Ship RPM | In HiNAS HS-Mode, the difference between HiNAS command RPM and actual RPM must be less than 10 to activate Route Tracking. | In HiNAS HS-Mode only
| RPM Range | In HiNAS HS-Mode, HiNAS command RPM must be within the predefined 'RPM Range' for safe Route Tracking operation. | In HiNAS HS-Mode only
| Back-up Navigator Alarm | All back-up navigator alarms must be acknowledged to activate Route Tracking, ensuring operator awareness of system status. |
| Voyage Planning | Voyage Planning must be completed and route must be applied to activate Route Tracking. Valid waypoints and track segments are required. |
| XTD Limit | Cross Track Error (XTE) must be within the user-defined XTD (Cross Track Distance) limit to activate Route Tracking. |
| Preset Enter Distance | Distance to start waypoint must be less than the 'Preset Enter Distance' to activate Route Tracking, ensuring proper track acquisition. |
| Preset Enter Angle | The angle between the own ship's heading and the track course must be less than the 'Preset Enter Angle' for safe Route Tracking activation. |
| Min Turn Radius | Turning radius (RAD) of applied route must exceed the 'Min Turn Radius' to ensure safe maneuvering capabilities. |
| Max Course Change | Course difference between all consecutive waypoints must be less than the 'Max Course Change' to prevent excessive course alterations. |
| ROT Max | Rate of Turn (ROT) of applied route must be less than the 'ROT Max' to ensure safe turning performance. |
| Preset Time to WOL | Time to Wheel Over Line (TWOL) must be greater than the 'Preset Time to WOL' to allow adequate preparation for course changes. |
| End of Track | Route Tracking cannot be activated at the end of the track to prevent system operation beyond planned route. |
| Position Monitoring | Secondary position sensor is mandatory for Route Tracking to provide redundancy and ensure navigation safety. |
| Heading Monitoring | Secondary heading sensor is mandatory for Route Tracking to provide backup heading information. |
| RPM Optimization | Speed control is available only when the planned RPM is optimized through voyage planning calculations. |
|===


===== Starting Conditions for CAA

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | All HiNAS software modules must function properly for CAA activation. System health monitoring ensures operational readiness. |
| Position Data | Position data is mandatory for CAA. Accurate vessel position is required for collision risk assessment and safe path generation. |
| COG Data | Course Over Ground (COG) data is mandatory for CAA. Current vessel course information is essential for collision prediction. |
| SOG Data | Speed Over Ground (SOG) data is mandatory for CAA. Current vessel speed is required for accurate collision risk calculations. |
| RADAR Connection | RADAR connection must be established for CAA. RADAR provides primary target detection and tracking capabilities. |
| Target Detection | Sufficient target detection capability must be available through RADAR and/or AIS for effective collision avoidance. |
| Safe Operating Area | CAA function requires adequate sea room and safe operating conditions for maneuvering. |
|===

===== Starting Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
// HiNAS Control Standard
ifdef::build_target_std[]
| Route Tracking Status | All Route Tracking conditions must be in Active state to enable CAC. Route Tracking provides the foundation for collision avoidance control. |
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
| Route Tracking Status | All Route Tracking conditions must be in Active state to enable CAC. Route Tracking provides the foundation for collision avoidance control. |
endif::build_target_nstd[]
| CAA Status | All CAA conditions must be in Active state to enable CAC. Collision Avoidance Assistance must be operational for control functions. |
| Safe Path Confirmation | A validated safe path must be confirmed by the operator before CAC activation. Human oversight ensures safe autonomous operation. |
| Preset Enter Distance | Distance to the start waypoint must be within acceptable limits for CAC activation. |
| Preset Enter Angle | The angle between vessel heading and track course must be within acceptable limits for safe CAC operation. |
| Device Connection | Required navigation equipment connections (autopilot, BMS) must be established based on operational mode. |
| HiNAS H/HS-Mode | CAC is available only in HiNAS H-Mode (heading control) and HS-Mode (heading and speed control). Not supported in S-Mode. |
|===





==== Low Performance Condition
After activation of Route Tracking and CAA/CAC, the system can continue to operate but with reduced performance or reliability. Low performance conditions indicate degraded operational capability that may affect system effectiveness. When any low performance condition is detected, corresponding warnings and indications are raised to alert the operator.

===== Low Performance Conditions for Route Tracking

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| Speed Sensor | Loss of SOG (Speed Over Ground) or STW (Speed Through Water) data reduces Route Tracking performance and affects speed control accuracy. |
| COG Sensor | Loss of COG (Course Over Ground) data reduces Route Tracking performance and affects course keeping accuracy. |
|===

===== Low Performance Conditions for CAA

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| Target Tracking | Degraded target tracking capability due to sensor limitations or environmental conditions affects collision risk assessment accuracy. |
| Environmental Conditions | Adverse weather conditions (heavy rain, fog, sea state) may reduce sensor effectiveness and CAA performance. |
| Limited Maneuvering Space | Restricted waters or traffic density may limit CAA's ability to generate optimal safe paths. |
|===

===== Low Performance Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| Route Tracking Status | Route Tracking in Low Performance state causes CAC Low Performance. Any Route Tracking condition in low performance directly affects CAC capability. |
| CAA Status | CAA in Low Performance state causes CAC Low Performance. Degraded collision avoidance assistance affects control decisions. |
| Safe Path Status | The safe path being followed is outdated or suboptimal. Updated safe path proposals require operator confirmation to maintain optimal performance. |
|===



==== Failure Condition
Route Tracking/CAC functions are automatically deactivated when critical failure conditions occur, ensuring the safety and stability of the vessel. This fail-safe mechanism prevents potentially dangerous autonomous operation under compromised conditions. When deactivation occurs, appropriate warnings (TRK Control Stop) are issued to notify the operator, and manual control must be resumed immediately.

===== Failure Conditions for Route Tracking

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | Critical software module failure or system integrity compromise causes immediate Route Tracking deactivation to prevent unsafe operation. |
| Position Sensor | Loss of primary position data for extended period causes Route Tracking deactivation. Accurate positioning is fundamental for safe navigation. |
| Heading Sensor | Loss of primary heading data for extended period causes Route Tracking deactivation. Reliable heading information is essential for course control. |
| Autopilot Override | Manual override of autopilot (TKM: Ext.HC to Manual/HC mode, YDK: AUTO to Manual mode) immediately deactivates Route Tracking to ensure operator control priority. |
| Autopilot Ext.HC | Failure to establish External Heading Control mode within 60 seconds of Route Tracking activation causes automatic deactivation for safety. | For TKM autopilot only
| Autopilot Connection | Loss of autopilot communication link causes Route Tracking deactivation as steering control cannot be maintained safely. |
| Autopilot Signal Validity | Invalid or corrupted autopilot signals cause Route Tracking deactivation to prevent erroneous steering commands. |
| BMS Override | Manual override of BMS auto mode (ON to OFF) in HiNAS HS-Mode causes Route Tracking deactivation to maintain operator control authority. | In HiNAS HS-Mode only
| BMS Ready | BMS not ready condition in HiNAS HS-Mode causes Route Tracking deactivation as engine control integration cannot be maintained. | In HiNAS HS-Mode only
| BMS Signal Validity | Invalid BMS signals in HiNAS HS-Mode cause Route Tracking deactivation to prevent unsafe engine control commands. | In HiNAS HS-Mode only
| RPM Range | HiNAS command RPM exceeding safe operating range in HS-Mode causes Route Tracking deactivation to prevent engine damage or unsafe operation. | In HiNAS HS-Mode only
| BMS(HiNAS) | Failure to establish BMS HiNAS mode within 60 seconds in HS-Mode causes Route Tracking deactivation for operational safety. | In HiNAS HS-Mode only
| Position Monitoring | Loss of secondary position sensor causes Route Tracking deactivation due to insufficient redundancy for safe autonomous navigation. |
| Heading Monitoring | Loss of secondary heading sensor causes Route Tracking deactivation due to insufficient redundancy for reliable course control. |
| Critical System Alarm | Activation of critical navigation system alarms causes immediate Route Tracking deactivation to ensure operator intervention. |
|===


===== Failure Conditions for CAA

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
| HiNAS SW Status | Critical software module failure causes CAA deactivation to prevent unreliable collision avoidance assistance. |
| Position Data | Loss of position data for extended period causes CAA deactivation. Accurate vessel position is essential for collision risk assessment. |
| COG Data | Loss of COG data for extended period causes CAA deactivation. Course information is critical for collision prediction calculations. |
| SOG Data | Loss of SOG data for extended period causes CAA deactivation. Speed information is essential for accurate collision risk assessment. |
| RADAR Connection | Loss of RADAR connection for extended period causes CAA deactivation. Primary target detection capability is compromised. |
| Near Target | CAA function is deactivated if there is an obstacle or vessel in close range. CAA cannot operate safely with nearby targets that prevent safe maneuvering. |
|===

===== Failure Conditions for CAC

[cols="3,5,3", options="header"]
|===
| Title| Description| Note
// HiNAS Control Standard
ifdef::build_target_std[]
| Route Tracking Status | Route Tracking deactivation immediately causes CAC deactivation. Route Tracking provides the foundation for collision avoidance control functions. |
endif::build_target_std[]

// HiNAS Control
ifdef::build_target_nstd[]
| Route Tracking Status | Route Tracking deactivation immediately causes CAC deactivation. Route Tracking provides the foundation for collision avoidance control functions. |
endif::build_target_nstd[]
| CAA Status | CAA deactivation immediately causes CAC deactivation. Collision Avoidance Assistance is prerequisite for autonomous control decisions. |
| Device Connection | Loss of required device connections (autopilot, BMS) causes CAC deactivation as control authority cannot be maintained safely. |
| HiNAS H/HS-Mode | CAC is automatically deactivated if system is not in H-Mode or HS-Mode. CAC requires appropriate control mode for safe operation. |
|===