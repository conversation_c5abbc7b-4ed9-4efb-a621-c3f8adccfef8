// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Dangerous Target and Collision Danger Alert

HiNAS evaluates ownship's collision danger relies on the following assumptions and calculations:

* The velocity vectors of both the ownship and the target are assumed to be constant.

* PDL is the maximum allowable deviation from the planned position at a certain point of time from the safe path and safe path schedule. In essence, PDL represents the positional uncertainty of the ownship and is clearly distinct from safety domains; PDL is not considered a safety domain. The size of PDL should consider the following factors: position sensor errors, manoeuvrability uncertainties, environmental disturbances, system latency, etc. 

* Safety distance: Safety distance represents the minimum safe distance that the user intends to maintain from the target It is recommended that the safety distance be no less than 0.5 NM. The length of one of the largest vessels in the world is approximately 400 meters. In extreme cases, we may assume that the CCRP is located at the very end of the vessel. Distance between two CCRPs should exceed approximately 800 meters for safety. Since 800 meters is roughly 0.431 NM, it is recommended that the safety distance be at least 0.5 NM or larger, allowing for an additional margin. The safety radius can be configured by the operator, as described in <<Collision Avoidance Setting>>.

* Invader: A vessel predicted to intrude into an existing safe path. This prediction is based on the target’s speed at the time the safe path was calculated. If CAC is active, the evaluation is based on the control path; otherwise, it is based on the suggested safe path.

* CPA refers to the estimated point where the distance between two objects—at least one in motion—reaches its minimum. Time to CPA (`stem:[t_{\text{CPA}}]`) and distance at CPA (`stem:[d_{\text{CPA}}]`) are used to assess the risk of potential collision. CPA and TCPA are calculated as follows, where `stem:[p_A]` and `stem:[p_B]` denote the position vectors of the ownship and the target, respectively. `stem:[v_A]` and `stem:[v_B]` represent their corresponding velocity vectors.

[[eq_description_of_tcpa]]
[latexmath]
++++
t_{CPA} = \frac{(p_B - p_A)\cdot (v_B - v_A)}{\left\| v_A - v_B \right\| },
++++

[[eq_description_of_cpa]]
[latexmath]
++++
d_{\text{CPA}} = \left\| (p_A + v_A t_{CPA}) - (p_B + v_B t_{CPA}) \right\|.
++++

By comparing `stem:[t_{\text{CPA}}]` and `stem:[d_{\text{CPA}}]` against predefined thresholds, HiNAS determines whether a target is considered dangerous, as detailed in <<Dangerous Target>>.

==== Dangerous Target

HiNAS determines whether a target is dangerous based on the calculated CPA and the applicable reference TCPA configured in <<Collision Avoidance Setting>>.  
Different reference TCPA values are applied depending on the encounter situation, as shown in <<fig_encounter_situation_classification>>:

* TCPA-GW: 60 minutes (default)
* TCPA-SO: 40 minutes (default)
* Other: 60 minutes
* If the previous state was dangerous: 180 minutes

If the TCPA is within the reference value and the CPA is less than the defined safety distance, the target is classified as dangerous.

[NOTE]
====
* Target association type 4 in <<table_sensor_fusion_type_and_policy>> is not applicable to this concept.  
* For risk indication related to type 4, refer to <<Concept of Camera only Detected Target Alert>>.
====

==== Collision Danger Alert

HiNAS generates a collision danger alert when any of the following conditions are met:

* No safe path exists and a dangerous target is detected.
* During CAC:
** The ownship deviates beyond the PDL.
** One or more invaders are detected.