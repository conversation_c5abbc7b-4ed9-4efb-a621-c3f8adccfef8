// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Introduction
[concept-of-operation-introduction]
HiNAS Control is designed to assist certified officers and captains by automating key elements of navigation and maneuvering—specifically planning, recognition, decision-making, and control.  
The system's primary function is to follow the planned route and speed of the voyage plan, avoid dangerous targets (such as vessels, boats, navigational aids, and land), and return to the planned route after executing collision avoidance maneuvers.  
As the system serves as an assistant system, the responsibility for its operation remains with the certified officers or captain on board. Additionally, all functions operate independently within the vessel and are not interfaced with external systems.

HiNAS Control limits its functions to monitoring, planning, recognition, decision-making, and control within a defined operational envelope.
These functions can be activated according to the operation modes described in <<System Operation Mode>>.

* Monitoring: Monitor voyage status through video streaming and geoinformation display.
* Planning: Voyage planning (via imported route plans from ECDIS or officer-verified data).
* Recognition: Detect dangerous targets using vision sensors, AIS, Radar
* Decision: Assess detected targets to determine danger and decide avoidance strategies or continue following the planned route.
* Control: Automatically issue commands to the autopilot and BMS for route tracking and collision avoidance (Route Tracking, CAC).

[NOTE]
====
Control does not imply direct manipulation of the rudder or main engine. Instead, it means assigning desired commands to respective control units, replacing human input (e.g., Autopilot, BMS).
====


HiNAS Control operates in four modes:

* N: All CA function and Route Tracking are deactivated. Monitoring mode.
* CAA mode: CAA is activated, while Route Tracking and CAC are deactivated.
The user retains control mode, but the safe path proposed by CAA is displayed. The proposed safe path may change without user intervention.
* Route Tracking mode: Route Tracking is activated, while CAA and CAC are deactivated.
HiNAS assumes control mode and follows the preplanned route. The safe path proposed by CAA is not displayed.
* (Route Tracking/CAA mode): Route Tracking and CAA are activated, while CAC is deactivated.
HiNAS assumes control mode, follows the preplanned route, and displays the safe path proposed by CAA. The proposed safe path may change without user intervention but does not influence the vessel's maneuvering.
* CAC mode: Route Tracking, CAA, and CAC are all activated.
HiNAS assumes control mode, displays the safe path proposed by CAA, and follows it. The proposed safe path affects the vessel’s maneuvering, and any change to the path requires user confirmation.


See <<fig-mode-operation-diagram>> for more details.

[NOTE]
====
* In CAA mode, while safe paths are suggested to the operator, they are not automatically applied.
* Autopilot and BMS control conform to IEC 62065 (Ed. 2.0).
====

[[fig-mode-operation-diagram]]
.Mode Operation Diagram
image::CONCEPT_OF_OPERATION/mode_operation_diagram_v0.png[align="center",600,400]

Activation of Route Tracking and CAC is strictly limited to cases where starting conditions are not satisfied.
And Route Tracking/CAC are automatically deactivated in case of system failure to ensure vessel safety and stability.

See <<Concept of Starting and Failure Conditions>> for more details.