// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Recognition

The main role of ODTC is to generate TT for use in CA by fusing targets detected from sensors with different characteristics (AIS, RADAR, Camera).
ODTC consists of a CBD module, which handles camera-based obstacle detection, and a target associator called Sensor Fusion (SF), which fuses information from each sensor, as illustrated in <<fig_block_diagram_of_odtc>>.

[[fig_block_diagram_of_odtc]]
.Block diagram of ODTC
image::CONCEPT_OF_OPERATION/block_diagram_of_odtc_v0.png[align="center",600,300]

The CBD module receives images from HiNAS's camera system and infers objects around the ship.  
The prediction results from this module are used as input to the target associator to help recognize nearby objects.  
Target associator associates target information detected by AIS, RADAR, and CBD.

To enable camera-based object detection, HiNAS employs deep learning technology, allowing automatic recognition of nearby targets within a limited detection range.  
Due to this short range, the camera serves only as an auxiliary sensor for target detection.
CBD consists of sub-modules of pre-processing, deep learning model, and post-processing, and the functions of each sub-module are as follows.

==== Preprocessing
Preprocessing involves receiving super-wide-view images generated from the EO/IR camera system and preprocessing them, and then sending the data to the deep learning model. Since the input data format of a deep learning model is fixed, the preprocessing process involves changing the input image to the corresponding format.

==== Deep Learning Model
In the deep learning model prediction process, preprocessed data is input into the deep learning model to predict the location and type of objects in the image. Predicted values are generated by calculating input values using the weights of a deep learning model learned with large-scale training data.

==== Post-processing
In the post-processing process, the prediction results obtained through the deep learning model prediction process are formatted so that they can be used in the target association. This process may involve further operations on the deep learning model's predictions to produce more stable results.


HiNAS also applies target association techniques to combine and refine data from AIS, RADAR, and Camera, ensuring the accuracy and relevance of information provided to the operator.  
Because the recognition sensors operate independently, they may generate inaccurate or unnecessary information.  
The target associator addresses this by filtering and consolidating data before presentation.

It is important to note that camera-only detected targets cannot provide distance or bearing information and, therefore, must not be used for RA.

The following points outline the required and recommended operator actions:

* Radar targets are not detected automatically. Operators must manually designate targets on the radar display to interface radar target information with the system.
* Camera-only detected targets cannot be displayed on the map display; they are shown exclusively in the camera display area.
* Camera-based detectors aim to detect the following types of objects (<<fig_taxonomy_for_object_detection>> shows the camera-based object detection policy):
** Ship  
** Motor-Boat  
** Navigation Aid
* Camera-based detectors process real-time image input from the camera system:
** Videos above 5 FPS are processed without delay.

[NOTE]
====
* Ships, motor-boats, and navigation aids are classified internally, but they are only shown as a single type of detected target.
====

[[table_recongnition_system]]
.Recognition system
[cols="2,4,2", options="header"]
|===
| Device | Purpose | Notes

| AIS
a|
* Long/short-range ship detection
| Used in target association

| RADAR
a|
* Long/short-range ship detection 
* Detection of geo objects
| Used in target association

| Camera
a|
* Object detection using deep learning (limited range)  
* Provides target classification
| Auxiliary sensor only
|===