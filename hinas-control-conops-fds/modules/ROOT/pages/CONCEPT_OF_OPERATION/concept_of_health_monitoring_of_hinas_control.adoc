// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Health Monitoring of HiNAS Control

HiNAS Control monitors the health status of signals, internal software modules, and control devices through its status monitoring function.
To implement the health monitoring function, the following items are checked:

* Interfaced input data
* Internal software output
* Control I/O data (as described below)

==== Interfaced Input Data Check

To ensure the quality of interfaced data used by HiNAS Control, the system applies two data quality checks:

1. Timestamping each data point with UTC.
2. Performing spike and stuck tests for numerical data.

<<table_data_quality_control_method_and_purpose>> summarizes the methods and purposes of these data quality control measures.
If critical data is identified as abnormal, appropriate alarms will be generated according to the alarm system (<<ALERT SYSTEM OF HINAS CONTROL>>).

[[table_data_quality_control_method_and_purpose]]
.Data quality control methods and purposes
[cols="^2,4,2", options="header"]
|===
| Method | Purpose | Data Type

| Spike / Stuck Test
| Detects sensor faults in interfaced equipment (e.g., speed, position)
| IEC 61162-1 protocol data

| UTC Timestamp
| Monitors the health status of internal modules (robustness of data processing)
a|
* IEC 61162-1
* Image data
|===

[NOTE]
====
* MODBUS TCP/IP data quality is checked using a timeout test for request-response communication.
* Temporal spikes can be filtered as described in <<table_su_setting_adv_tcs>>.
====

==== Internal Software Output Check

For internal software health checks, the following two tests are performed:

* UTC timestamp check
* Schematic validation check

The UTC timestamp check ensures the software module is running by verifying the inclusion of a UTC timestamp in its output.
The schematic validation test verifies that the output JSON data contains all required keys and that the corresponding values are within normal ranges.
Both checks are continuously performed to ensure the normal operation of HiNAS Control's internal software modules.

==== Control I/O Test

Control I/O tests are performed by checking the consistency between control input signals and their corresponding echo signals.  
According to <<Concept of Autopilot Control>> and <<Concept of BMS Control (Ship Speed Control by RPM Assignment)>>, HiNAS interfaces with the autopilot and BMS.

For the autopilot:

* The system sends an `HTC` (heading command) sentence and expects an `HTD` echo sentence.
* It also sends a `PAVK--` sentence and expects a `PAPL` response.
* The system checks the periodicity and integrity of `HTD` and `PAPL` to verify proper autopilot control.

For the BMS:

* The system uses the Modbus TCP/IP protocol to perform request-response checks.
* The transmission and reception of these signals are monitored to ensure correct BMS control operation.