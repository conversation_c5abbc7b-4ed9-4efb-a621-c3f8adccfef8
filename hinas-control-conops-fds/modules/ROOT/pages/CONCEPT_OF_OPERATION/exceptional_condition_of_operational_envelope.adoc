// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Exceptional Condition of Operational Envelope

The difference of COG of own-ship and the planned route, XTE, and environmental factors (current speed and wind speed) of a sailing ship can fluctuate to an unstable range under certain maneuvering statuses of the own-ship: during waypoint number change, after activation of the collision avoidance mode, during return to the planned route after collision avoidance.
As these conditions should not be classified out of the operation envelope, the internal core algorithms of HiNAS Control require handling of some exceptional cases as described in <<table-envelope-categories-table>>, e.g., till the own-ship returns to the planned route after finishing avoidance of obstacles, or during turning status.
Detailed ranges or coverages defined in the core algorithms are described in the following sub-sections (a) and (b). Depending on results of QA (Quality Assurance) tests, additional factors can be added in addition to the defined items.


[[table-envelope-categories-table]]
.Table: Items required to be handled by internal algorithms of HiNAS Control (during Turning Mode, return to the planned route, during avoidance of obstacles)
|===
|Envelope Category  |Description                  
|Own-ship status    |The difference of COG of own-ship and the planned route course
|Environment        |Current speed, Wind Speed
|===


==== Coverage Criteria of Turning Mode
The coverage criteria for turning mode are shown in <<fig-description-of-cpa>>.
WP(Waypoints) and R(Turning Radius) are planned in a passage plan.
R and WOL(Wheel Over Line) are determined by the core algorithm of HiNAS Control.
Status from (2) to (4), the navigation status is found to be turning mode.

[#navi-status-according-to-leg-sec]
.Description of navigation status according to the leg section.
image::CONCEPT_OF_OPERATION/navi-status-according-to-leg-sec-v0.png[align="center",400,300]


==== Coverage Criteria of Collision Avoidance Mode
The coverage criteria for collision avoidance depends on the Closest Point of Approach(CPA) which consider spatial–temporal factors.
In <<fig-description-of-cpa>> Description of Closest point of approach, the CPA is described.
The CPA is the point where ships are closest from each other when the own ship and other ship maintain their current course and speed.
The time taken to reach the CPA is defined as TCPA(`stem:[t_{\text{CPA}}]`) and the distance between each ship on the CPA is defined as DCPA(`stem:[d_{\text{CPA}}]`).
When TCPA and DCPA satisfy equation <<eq-description-of-cpa>> the collision avoidance mode is activated. (`stem:[t_{\text{max}}]`) and (`stem:[d_{\text{min}}]`)are determined by core algorithms of HiNAS Control.


[[fig-description-of-cpa]]
.Description of Cloest point of approach.
image::CONCEPT_OF_OPERATION/description-of-cpa-v0.png[align="center",400,300]

:eqnumbers:
[[eq-description-of-cpa]]
//.Eq. {counter:equation}: Description of CPA
[latexmath]
++++
0 \le t_{\text{CPA}} \le t_{\text{max}}, d_{\text{CPA}} \le d_{\text{min}}
++++
