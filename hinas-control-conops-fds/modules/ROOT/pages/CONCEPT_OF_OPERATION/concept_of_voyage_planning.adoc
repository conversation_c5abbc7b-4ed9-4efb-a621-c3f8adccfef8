// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Voyage Planning (Global Route)

To activate Route Tracking and CAA/CAC, voyage planning should first be applied in the system.
HiNAS interfaces with the voyage optimization module to receive and apply optimized plans.
During the voyage planning stage, HiNAS provides two modes: From ECDIS and From File.

Note that all route information created in either mode must be verified by a certified ECDIS.  
Route data and files must be downloaded only after validation and safety checks have been completed using the ECDIS.

==== From ECDIS Mode
Since HiNAS is interfaced with the ECDIS module, routes planned in ECDIS can be directly communicated to the system.
Based on this route information, the system performs speed optimization for the voyage.
Using the preplanned route and the optimized speed plan, HiNAS executes the planned voyage.

==== From File Mode
In this mode, route information is obtained by uploading a route file (.RTZ) exported from the ECDIS.
The user defines the ETA and loading conditions, and the system optimizes the speed plan accordingly.
This function is intended to assist voyage planning, while the mandatory route validation and safety checks are still expected to be performed within the ECDIS.

[NOTE]
====
* Safe path generation by CAA is performed within the XTL of the planned route.
* All routes should be pre-validated to ensure they are clear of any navigational dangers.
====

[[fig_voyage_planning_of_hinas]]
.Voyage Planning of HiNAS Control
image::CONCEPT_OF_OPERATION/voyage_planning_of_hinas_v0.png[align="center",600,300]