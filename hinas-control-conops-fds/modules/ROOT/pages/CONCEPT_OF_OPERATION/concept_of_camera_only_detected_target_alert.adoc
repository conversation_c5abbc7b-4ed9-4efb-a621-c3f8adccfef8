// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Camera only Detected Target Alert

As described in <<Concept of Target Associator>>, the estimated distance and bearing by using camera detection has very low accuracy and HiNAS uses camera only detected target as caution level alert if the estimated distance between own ship and detected target is less than 4 NM.
The distance is calculated based on below formulae and conceptual figures.

* For alert information, refer to <<ALERT SYSTEM OF HINAS CONTROL>>.
* Note that the camera detected target related alert is provided as common alert regardless of classification result described in <<fig_taxonomy_for_object_detection>>.

* The red line indicates the infinite distance from ownship.
* `stem:[d_{\text{1}}]`, `stem:[d_{\text{2}}]`, and `stem:[d_{\text{3}}]` represents distances from ownship, respectively.
* The black lines indicate bearing from ownship's heading.
* `stem:[u, v]`: Image coordinate
* `stem:[f]`: Focal length of the camera (HiNAS: approximately 1090 [pix])
* `stem:[(cu, cv)]`: Principal point of the camera (HiNAS: approximately (944, 550)[pix])
* `stem:[(d)]`: Distance from ownship to target ships
* `stem:[(h)]`: Height from the surface of the sea to the camera

NOTE: Actual value of `stem:[f]` and `stem:[(cu, cv)]` is achieved in camera calibration stage, presented value above is default value in factory.

From the formulae from <<calculation-method-of-distance-from-camera-geometry>>, the estimated distance from camera and its relevant pixel example is as <<estimated-distance-and-pixel>>. And if the target is detected within the 4 NM corresponding pixel, the caution is provided from alert function. Actual pixel for 4 NM is decided by ship specification.


[[distance-line-and-bearing-in-front-view]]
.Distance line and Bearing in Front View
image::CONCEPT_OF_OPERATION/distance-line-and-bearing-in-front-view-v0.png[align="center",300,300]


[[distance-line-and-bearing-in-top-view]]
.Distance Line and Bearing in Top View
image::CONCEPT_OF_OPERATION/distance-line-and-bearing-in-top-view-v0.png[align="center",300,300]


[[calculation-method-of-distance-from-camera-geometry]]
.Calculation Method of Distance from Camera Geometry
image::CONCEPT_OF_OPERATION/calculation-method-of-distance-from-camera-geometry-v0.png[align="center",300,300]


[[estimated-distance-and-pixel]]
.Estimated distance and pixel
[cols="3,2,2,2,2,2"]
|===
| Height [m] 5+|50
| Focal [pix] 5+| 1090
| cv [pix] 5+| 550

| Distance [m]
| 500
| 1000
| 2000
| 5000
| 7568 (4 NM)

| image_v [pix]
| 659.00
| 604.50
| 577.25
| 560.90
| 557.20
|===

[NOTE]
====
* Depending on the camera installation location, there is blind area as shown in <<blind-area-of-camera>>.
* The largest blind area exists in the bow direction, which is calculated as follow. The blind area may vary with ship specification, and it is decided by general arrangement drawing of the ship (design condition).
====

* `stem:[d_1]`: distance between the projected point from the camera to the surface and the intersected point between the line `stem:[l]` and the surface.
* `stem:[d_2]`: maximum distance of blind area
* `stem:[h_1]`: height from the surface of the sea to the camera

[latexmath]
++++
d_2 = d_1 \times \frac{h_2}{h_1}
++++

[[blind-area-of-camera]]
.Blind area of camera
image::CONCEPT_OF_OPERATION/blind-area-of-camera-v0.png[align="center",300,300]