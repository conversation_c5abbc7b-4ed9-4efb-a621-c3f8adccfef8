// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Function Flow of HiNAS Control

The overall user experience flow is illustrated in <<fig_function_flow_of_hinas_control>>.

When the system starts, HiNAS checks the health status of both the interface and internal software modules.  
If the start condition does not meet the requirements for CAA or Route Tracking, a notification is shown in the Operation Detail panel indicating that only monitoring mode is available.

The function begins with the voyage planning stage.  
HiNAS Control is interfaced with the ECDIS and the speed optimizer.  
Operators can plan and optimize voyage speed (RPM) using HiNAS Control and apply the plan to the system.

Once the voyage plan is applied, operators monitor and conduct the voyage.  
If the conditions for activating Route Tracking are satisfied (i.e., the start condition is met), the Route Tracking toggle becomes active, and operators can initiate autonomous navigation by toggling it on.
In Route Tracking mode, the system performs autonomous route and speed tracking.

A background watch system continuously monitors voyage conditions and alerts to determine whether autonomous operation should continue.  
If a failure condition is detected, Route Tracking is deactivated, and control is returned to the control units (i.e., autopilot and BMS) with both visual and audible alerts.
The system then switches back to monitoring mode.

In addition to Route Tracking, operators may activate CAC when CAA is enabled.
In this mode, the system autonomously suggests a safe path and follows it if the operator approves.
If a failure condition occurs, the system returns to Route Tracking mode when the CAA failure condition is satisfied, or to monitoring mode if the Route Tracking failure condition is met.

CAA can be activated independently of Route Tracking constraints; therefore, its function flow is not described in this section.

For more information on the background watch system (i.e., system health monitoring), refer to <<Concept of Health Monitoring of HiNAS Control>>.

These parameters can be monitored via the Operation Detail display.

For details on function activation, see <<Function Activation>>.


[[fig_function_flow_of_hinas_control]]
.Function flow of HiNAS Control
image::CONCEPT_OF_OPERATION/function_flow_of_hinas_control_v1.png[align="center",600,300]