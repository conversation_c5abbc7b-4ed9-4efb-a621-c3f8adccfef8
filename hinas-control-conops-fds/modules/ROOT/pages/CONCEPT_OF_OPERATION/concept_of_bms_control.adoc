// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of BMS Control (Ship Speed Control by RPM Assignment)

As described in <<Concept of Control Mode>>, BMS control is only applicable in HS mode.
BMS control is permitted only when the voyage condition satisfies the starting condition for Route Tracking and should follows the control concept and sequence described below.

BMS is designed to manage and control the ship's propulsion system, including the main engine and associated components.
It allows navigator to start, stop, and adjust the engine speed through various control modes, such as direct control in the engine control room or remote control on the bridge.

HiNAS Control communicates with the BMS unit via the Modbus TCP/IP protocol, using the signals specified in <<table-required-signal-for-bms-control>>.
If the BMS encounters an abnormal condition, the first digit of *Signal 1* changes from 'ready' (1) to 'not ready' (0).
Similarly, if HiNAS detects an abnormal or 'not ready' condition, the first or third digit of *Signal 3* will change accordingly.

BMS control can only be initiated when both *Signal 1* and *Signal 3* indicate ready and normal conditions, as shown in <<fig-control-build-up-concept-of-bms>>.

[NOTE]
====
HiNAS 2.0 or NAS2.0 and Auto-mode in the following figures are HiNAS Control and Route Tracking, respectively.
====

[[table-required-signal-for-bms-control]]
.Required signals for BMS control
[cols="2,3,4", options="header"]
|===
| Device
| Signal
| Description

| BMS
| Route Tracking Ready/Activation (Signal 1)
a|
* indicates if BMS is in auto mode and activated.  
* expressed as two-digit binary:  
** 1st digit: 1 (ready), 0 (not ready)  
** 2nd digit: 1 (activated), 0 (not activated)

| 
| Setting RPM (Signal 2)
a|
* indicates the commanded RPM.  
** multiply received value by 0.1 (e.g., 123 → 12.3)

| HiNAS Control
| Route Tracking Ready / Activation / Abnormal (Signal 3)
a|
* indicates HiNAS mode readiness, activation, and abnormal status.  
* expressed as three-digit binary:  
** 1st digit: 1 (ready), 0 (not ready)  
** 2nd digit: 1 (activated), 0 (not activated)  
** 3rd digit: 1 (abnormal), 0 (normal)

| 
| RPM Command
a|
* RPM command to BMS.  
* Expressed as three-digit binary (same as above).
|===

[[fig-control-build-up-concept-of-bms]]
.BMS control build-up concept
image::CONCEPT_OF_OPERATION/fig-control-build-up-concept-of-bms-v0.png[align="center",600,300]

=== Conditions for Control Abort between BMS and HiNAS Control

BMS control can be aborted under the following three conditions:

[[table-three-conditions-abort-bms]]
.Conditions for BMS control abort by HiNAS
[cols="1,3,4", options="header"]
|===
| No. | Case | Description

| 1
| Control Abort by BMS
| Triggered when the user switches to manual mode (Signal 1: 11 → 01).

| 2
| Control Abort by HiNAS
a|
* User deactivates Route Tracking of HiNAS (Signal 3: 011 → 001).
* triggered by abnormal conditions (Signal 3: 011 → 111).
* triggered by Route Tracking failure (Signal 3: 011 → 010).

| 3
| Control Abort due to BMS Abnormal Condition
| triggered by BMS abnormal conditions, such as RPM falling below 'half sea ahead' (Signal 1: 11 → 10 → 00).
|===

=== Control Override and Constraint Policy

In the event of user intervention or abnormal conditions in autonomous mode, the following override policies apply:

[[table-control-override-constraint-policy]]
.Control override/constraint policy
[cols="1,4", options="header"]
|===
| No. | Description

| 1
| In dual engine configurations, *Lead-Follow* mode of BMS must be activated. (Mode name may vary by manufacturer. HiNAS Control only commands the Lead BMS.)

| 2
| To prevent human error in autonomous mode, RPM must not be changed manually via the BMS lever.  
The lever should always match the RPM commanded by HiNAS.

| 3
| In emergency situations, if the user maintains manual lever movement for more than 10 seconds, BMS will recognize this as a mode change to manual and issue an alert.

| 4
| In normal operations, to change the engine RPM, users must first switch from autonomous to manual mode.

| 5
| If autonomous mode is aborted, the RPM set by HiNAS will be maintained until the user makes adjustments.
|===

=== Control Build-up and Abort Sequence

The BMS control build-up sequence is shown below:

[[fig-bms-control-build-up-v0]]
.BMS control build-up
image::CONCEPT_OF_OPERATION/bms-control-build-up-v0.png[align="center",600,300]

For control abort cases, refer to <<table-three-conditions-abort-bms>>.  
The corresponding sequences are illustrated in the figures below:

[[fig-bms-control-abort-by-bms-mode-change]]
.BMS control abort by BMS mode change
image::CONCEPT_OF_OPERATION/bms-control-abort-by-bms-mode-change-v0.png[align="center",600,300]

[[fig-bms-control-abort-by-hinas-control]]
.BMS control abort by HiNAS
image::CONCEPT_OF_OPERATION/bms-control-abort-by-hinas-control-v0.png[align="center",600,300]

[[fig-bms-control-abort-by-abnormal-condition-of-bms]]
.BMS control abort by abnormal condition of BMS
image::CONCEPT_OF_OPERATION/bms-control-abort-by-abnormal-condition-of-bms-v0.png[align="center",600,300]