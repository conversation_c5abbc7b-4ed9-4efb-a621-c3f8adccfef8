// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Collision Avoidance

This section describes the collision avoidance (CA) concept of HiNAS Control.
Target ship information is provided by the target associator (see <<Concept of Target Associator>>).
The overall CA function flow is illustrated in <<fig_flowchart_ca_algorithm>>.

In the initial phase, target ship data and route information are obtained.
Note that obstacles detected solely by the camera sensor are not considered in this process.

After CAA activation, the CA module evaluates the risk of collision (RA) to determine whether a collision avoidance maneuver is necessary.
The evaluation is based on method defined in <<Concept of Dangerous Target and Collision Danger Alert>> and the COLREG encounter situation classification algorithm.

If the situation is assessed as dangerous, a Collision Danger Warning is issued, and local path planning algorithms are activated to generate a collision-free path (SPG) for avoidance maneuvering.
A path validity checker ensures that the generated safe path remains within the cross-track limit (XTL).
If the local path planning algorithm fails to generate a collision-free path, No CA Path warning is issued.

Users are required to decide whether to follow the generated safe path.
To assist in decision-making, the display provides safe path information, including DIST (distance), expected time-to-go, and CA-related settings.
To follow the generated safe path, HiNAS must be in Route Tracking mode, with CAC operating on top of Route Tracking and in accordance with applicable standards.
If the user selects `Not Follow`, the CA path will be updated periodically.

Once CAC is activated, HiNAS remains in the CAC loop, as shown in the figure, until failure conditions occur.
If the current safe path becomes invalid, the system returns to the SPG state.
The CA path will be terminated when the hazardous situation is resolved, and the system safely returns to the global route.

[[fig_flowchart_ca_algorithm]]
.Flowchart of collision avoidance algorithm
image::CONCEPT_OF_OPERATION/flowchart_of_ca_algorithm_v1.png[align="center",600,300]

For detailed information on collision avoidance, refer to the following documents listed in <<Normative References>>:

* HiNAS Control CA Verification, Validation & Test Methodology
* HiNAS Control Design Specification and Requirements
* HiNAS Control Operator Manual

Also, see <<FUNCTION DESCRIPTION>>.