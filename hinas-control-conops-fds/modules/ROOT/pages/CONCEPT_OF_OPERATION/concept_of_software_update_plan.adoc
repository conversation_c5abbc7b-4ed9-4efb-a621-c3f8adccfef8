// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Software Update Plan



To ensure the reliability of the autonomous functions, software updates are generally not recommended unless necessary to address critical or unknown fatal errors.
Accordingly, once the system is installed on a vessel, software updates are not performed during voyages unless explicitly requested by the ship owner.

However, updates may be required under the following conditions:

* Quality management and improvement activities (e.g., defect correction, hardware upgrades)
* Customer requests due to changing requirements (e.g., market trends, feature enhancements)

Generally, the PM maintains and manages the user requirements that are deemed to need modification, along with internal issue reports and improvement reports, in the backlog. These reports are created and tracked as tickets in the development management system. 

There are three levels of the version change: major, minor and hotfix.
Below are our versioning rules and meanings.

====
v{X}.{Y}.{Z}

* X: Major change version number
* Y: Minor change version number
* Z: Hotfix version number
====


*HiNAS Control Change Plan* and *HiNAS Control CA Verification, Validation & Test Methodology* as listed in <<Normative References>> describes the processes for managing such update requests systematically, as defined by A<PERSON>us.
This document covers the following strategies:

* Version control strategy
* Software change procedure
* Hardware change procedure
* Third-party software management
* Change management for CA
* Change management for ODTC