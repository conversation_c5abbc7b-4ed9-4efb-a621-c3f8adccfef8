// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Concept of Autopilot Control

As described in <<Concept of Control Mode>>, autopilot control is only applicable in HS mode or H mode.
The primary purpose of autopilot control is to manage the ship’s course and heading.
Autopilot control is permitted only when the starting conditions of Route Tracking are satisfied and must follow the control concept and sequence described below.

Communication with the autopilot is conducted via RS422 serial communication.
Control commands and responses are exchanged using the `$HTC` and `$HTD` sentences, as defined in the IEC 61162-1 standard listed in <<Normative References>>.
HiNAS employs external heading control to maintain the planned route (track control using external heading control).

For safety, sentences such as `$PAVK`, `$PYDK`, or `$PTKI` (depending on the autopilot manufacturer) are used to indicate abnormal conditions of HiNAS Control or the autopilot itself.

According to IEC 61162-1, autopilot has five operating modes, as shown below.
HiNAS uses the *Heading Control (H)* mode.

[[autopilot-operation-mode]]
.Autopilot operation modes
[cols="2,4", options="header"]
|===
| Operation Mode | Description

| Manual Steering (*M*)
| Manual rudder control by the user.

| Stand-alone (*S*)
| Autopilot functions as an independent heading controller. It does not accept a commanded heading input.

| Track Control (*T*)
| Autopilot functions as a track controller, using a commanded track input.

| Heading Control (*H*)
| Autopilot receives the commanded heading from an external device and operates as a remotely controlled heading controller.

| Rudder Control (*R*)
| Rudder control by an external device.
|===

To perform autopilot control, HiNAS and the autopilot exchange the following signals, as shown in <<required-signal-for-autopilot-control>>.

[[required-signal-for-autopilot-control]]
.Required signals for Autopilot control
[cols="1,1,4", options="header"]
|===
| Device
| Signal
| Description

| Autopilot
| $--HTD sentence
a| 
* Echo signal in response to the `$HTC` command from HiNAS Control.  
* Modes:  
** S — Stand-alone: Heading control by user-defined heading  
** T — Track control: Track control by external device (HiNAS)  
** H — Heading control: Heading control by external device (HiNAS)

| 
| $PYDK or $PTKI sentence
a|
* Health status indicator of the autopilot.  
* `$--APL,A,,,hh<CR><LF>`  
** A: Normal  
** V: Abnormal

| HiNAS
| $--HTC sentence
a|
* Command signal from HiNAS Control.  
* Modes:  
** S — Stand-alone  
** T — Track control  
** H — Heading control

| 
| $PAVK sentence
a|
* Health status indicator of HiNAS.  
* `$--PAVK,A,,,hh<CR><LF>`  
** A: Normal  
** V: Abnormal
|===

By monitoring the `$HTC`, `$HTD`, `$PAVK`, `$PYDK`, or `$PTKI` sentences, the system verifies readiness, control status, and health status of both systems.

The concept of control build-up and abort is illustrated in the figure below.

[[concept-of-autopilot-control-build-up]]
.Concept of Autopilot Control Build-up
image::CONCEPT_OF_OPERATION/concept-of-autopilot-control-build-up-v0.png[align="center",600,300]

Four abort conditions are defined as follows:

[[four-abort-conditions]]
.Four Abort Conditions
[cols="2,4", options="header"]
|===
| Abort Condition
| Description

| Control Abort by Autopilot
| Occurs when the user switches to manual control.

| Control Abort by HiNAS
| Triggered when the user presses the Route Tracking toggle button to exit Route Tracking, or due to abnormal conditions or failure of HiNAS.

| Control Abort by User Override
| Occurs when the user manually overrides HC (e.g., switching to stand-alone or manual steering).

| Control Abort by abnormal autopilot condition
| Triggered by an abnormal condition signal from the autopilot.
|===

[Note]
====  
* Control abort due to HiNAS Control abnormal conditions is managed by transitioning the control mode, and thus not specifically defined here.  
* Override policies for user intervention or abnormal conditions in autonomous mode are described below.
====

[[control-override-policy]]
.Control Override Policy
[cols="1,5", options="header"]
|===
| No. | Policy

| 1
| To prevent human error, rudder commands should not change due to manual operation of the autopilot handle while in autonomous mode.

| 2
| If the user changes the autopilot mode from autonomous to stand-alone or manual, the heading set by HiNAS should be maintained until the user manually adjusts it.

| 3
| If the user changes the HiNAS Control mode from autonomous to monitoring, the heading set by HiNAS should be maintained until the user manually adjusts it.

| 4
| If autonomous mode is aborted due to abnormal conditions (e.g., failure), the autopilot should switch to stand-alone mode, and the heading set by HiNAS should be maintained until the user manually adjusts it.
|===

The following figures illustrate the four abort conditions graphically.
HiNAS 2.0 or NAS2.0 and Auto-mode in the following figures are HiNAS Control and Route Tracking, respectively.

[[fig-control-abort-by-autopilot-mode-change]]
.Control abort by Autopilot mode change
image::CONCEPT_OF_OPERATION/control-abort-by-autopilot-mode-change-v0.png[align="center",600,300]

[[fig-control-abort-by-hinas-control-mode-change]]
.Control abort by HiNAS mode change
image::CONCEPT_OF_OPERATION/control-abort-by-hinas-control-mode-change-v0.png[align="center",600,300]

[[fig-control-abort-by-user-override]]
.Control abort by user override
image::CONCEPT_OF_OPERATION/control-abort-by-user-override-v0.png[align="center",600,300]

[[fig-control-abort-by-abnormal-condition-autopilot]]
.Control abort by abnormal condition of autopilot
image::CONCEPT_OF_OPERATION/control-abort-by-abnormal-condition-autopilot-v0.png[align="center",600,300]