// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== Role and Responsibility of Operators

Operators of HiNAS Control must be certified officers or the captain.
The roles and responsibilities of each operator are described in <<table_role_and_responsibility_of_operators>>.
For more details regarding user interactions required to operate HiNAS Control, please refer to the Operator's Manual.

[[table_role_and_responsibility_of_operators]]
.Roles and responsibilities of operators
[cols="2,4", options="header"]
|===
| Operator Position | Roles and Responsibilities

| Captain
a| 
* holds overall responsibility for the ship.
* acts as the final decision maker.
* decides whether to activate or deactivate HiNAS Control.
* supervises system operations.

| Chief Officer
a|
* decides whether to activate or deactivate the HiNAS.
* supervises system operations.
* executes the Captain’s decisions regarding system activation.

| 2nd Officer
a|
* monitors system decisions.
* executes the Captain’s decisions regarding system activation.
* holds the authority to deactivate the autonomous control of HiNAS at their discretion.
* conducts route planning using ECDIS and identifies congested areas.

| 3rd Officer
a|
* monitors system decisions.
* executes the Captain’s decisions regarding system activation.
* holds the authority to deactivate the autonomous control of HiNAS at their discretion.
* conducts periodic system maintenance.
* reports any issues related to HiNAS.

| Duty Officer (Officer on Watch during Voyage)
a|
* monitors:
** system and voyage decisions.
** ODD related parameters.
** takes over control from the system if alarms occur or the vessel operates outside the defined envelope.
* assumes control from the system if the system actions are deemed inappropriate.
* monitors radar displays and assigns targets to be tracked as necessary.
* acknowledges alarms and takes appropriate actions.
* adjusts system parameters based on voyage conditions:
** changes/adjusts Route Tracking/CAC related parameters on the settings page.
|===
