// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

=== System Operation Mode
HiNAS Control system has four operation mode 1) N, 2) Route Tracking, 3) CAA, and 4) CAC as shown in <<fig-mode-operation-diagram>>.

In N mode, the system reads interfaced sensor data, monitors voyage status, streams camera images, displays geo-information, and assesses collision risks.
Monitoring Mode is activated at system startup or when control is returned to the user.

CAA mode is activated via the CA Assistance toggle, this mode provides the operator with suggested collision-free safe paths for decision support. These suggestions are not applied automatically.

In Route Tracking mode, HiNAS takes control of heading and speed via the autopilot and BMS to follow the planned route and speed.
Activation of Route Tracking requires certain starting conditions to be met.

CAC enables collision avoidance control on top of Route Tracking.
When deactivated under normal conditions, the system returns to Route Tracking mode.
CAC is automatically deactivated in case of system failure to ensure vessel safety and stability.
When this occurs, a TRK Control Stop warning is issued to inform the operator of the deactivation, while the last issued control commands to the autopilot and BMS are maintained.
If Off PDL conditions occur, a Low Performance warning is issued.

When failure occurred, HiNAS is returned to the monitoring mode with relevant alert and the control command to autopilot and B<PERSON> is maintained as the last command.
<<table-description-of-operation-mode>> describes the detailed function lists and its relevant operation mode.


[NOTE]
====
* In the case of Route Tracking and CAA, safe path is generated by recommend mode but does not follow alternative heading.
* Autopilot and BMS control complies to IEC 62065 (Ed. 2.0).
====


[[table-description-of-operation-mode]]
.Description of operation mode
[cols="4,2,2,2,2", options="header"]
|===
|                                     | N | CAA | Route Tracking | CAC
| Monitoring                          | O | O   | O  | O  
| Planning                            | O | O   | O  | O   
| Recognition (Object Detection)      | O | O   | O  | O  
| Decision (Collision Risk Evaluation)| O | O   | O  | O   
| Route Tracking (Route/Speed Tracking)            | X | X   | O  | O
| CAC (Collision Avoidance)           | X | X   | X  | O

| Remark 
| Default Mode (without control authority)
| Support decision making
| Conduct autonomous voyage, has control authority without CA, only valid no alarm, Starting conditions
| Conduct autonomous voyage, has control authority with collision avoidance, only valid no alarm, ODD/starting conditions
|===


NOTE: Users can set the control unit for Route Tracking and CAC mode activation such as autopilot only (H mode) and speed and heading control mode (HS mode)