// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images

=== Intro

With the rapid advancement of IT technology, there is growing interest in ship automation and autonomous navigation systems.
One major focus is the development of IoT-based smart ship technologies that utilize data generated by onboard equipment and machinery. These smart ship solutions analyze navigational and operational data to provide optimized routes and decision support for efficient ship operation.

Another area of focus is the development of autonomous navigation systems for ships. Traditionally, ships have relied on ENC, Radar, and AIS to detect surrounding vessels and identify potential hazards.
Recently, the application of deep learning-based vision sensors—already widely used in various industries—has significantly enhanced ships' target detection capabilities, contributing to safer navigation.

Furthermore, autonomous navigation solutions, leveraging these advanced detection technologies, aim to replicate human navigation processes, from planning and perception to decision-making and control, providing safer and more comfortable voyages.
Driven by these technological advancements, ship owners are increasingly interested in adopting such solutions to prevent marine accidents, reduce crew workload, and minimize human error.

In response to these developments and market demands, HiNAS Control was introduced as an autonomous navigation solution.
It enables autonomous navigation by detecting target vessels and objects using cameras, radar, and AIS, and controls the autopilot and BMS based on detected targets and the planned route.
For more details, refer to <<fig-hinas-main-ui>>.

[[fig-hinas-main-ui]]
.Main UI of HiNAS Control
image::INTRODUCTION/hinas_main_ui_v2.png[align="center",700,300]

// new page
<<<