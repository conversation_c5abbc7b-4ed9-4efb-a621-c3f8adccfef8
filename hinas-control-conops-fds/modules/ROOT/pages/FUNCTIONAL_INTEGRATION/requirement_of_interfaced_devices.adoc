// 1.1. html, pdf conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images

=== Requirement of Interfaced Devices
To ensure safe and autonomous voyage, the interfaced devices should be accurate enough and reliable.
Below <<table_requirement_of_interfaced_devices>> is the requirement of interfaced devices of HiNAS  Control.

[[table_requirement_of_interfaced_devices]]
.Requirement of interfaced devices
[cols="1,2,1", options="header"]
|===
|Equipment |Requirement |Remark

|Loading Computer (AMS or Draft Gauge)
a|
* Pressure based sensor system  
* Interface with AMS system or additional interface slot for HiNAS  
* Type approved by class society 
|

|Rudder Indicator
a|
* Interface with AMS system or additional interface slot for HiNAS  
* Type approved by class society 
|

|AIS
a|
* Equipped with additional slot for HiNAS  
* Type approved by class society 
|

|Gyro
a|
* Sensor accuracy is less than 2 deg  
* Equipped with additional slot for HiNAS  
* Type approved by class society 
|

|Echo Sounder
a|
* Sensor accuracy is less than 10m  
* Equipped with additional slot for HiNAS  
* Type approved by class society 
|

|Speed Log
a|
* Sensor accuracy is less than 0.5 kn
* Equipped with additional slot for HiNAS
* Type approved by class society 
| 

|ECDIS
a|
* Whole route information should be in output  
* Equipped with additional slot for HiNAS 
* Type approved by class society 
|

|Radar
a|
* Radar target information should be in output  
* Equipped with X-band, S-band radar  
* Equipped with additional slot for HiNAS  
* Type approved by class society 
|

|GPS
a|
* Sensor accuracy is less than 1m  
* Equipped with additional slot for HiNAS 
* Type approved by class society 
|

|Autopilot
a|
* Equipped with Route Tracking level C or above module
* Equipped with additional slot for HiNAS  
* Type approved by class society
|

|BMS
a|
* Equipped MODBUS communication module  
* Equipped with additional slot for HiNAS  
* Type approved by class society 
|

|VDR
a|
* Can record operational data of HiNAS Control (control command, actual state) for 720hours  
* Type approved by class society 
|
|===

[NOTE]
====
* ECDIS to provide "Full way point" by RTZ file format with RRT sentence
* Radar to provide tracked target information and availability by `TTM`, `TTD`, `TLB`, `OSD`, and `RSD`  
====
