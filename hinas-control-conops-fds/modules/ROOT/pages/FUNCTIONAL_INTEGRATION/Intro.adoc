// 1.1. HTML, PDF conversion support //
:imagesdir: images   
:math:
:imagesoutdir: generated_images
:stem: latexmath

// 1.2. Preview Support Mode //
// :imagesdir: ../../../images 

To support its operational concept, HiNAS Control interfaces with various onboard equipment.
<<fig_interace_diagram_of_hinas_control>> illustrates the system’s connections with external devices.

The system collects navigational data from individual sensors such as GPS, gyro, and speed log, and sends control commands to the autopilot and BMS.
Depending on the ship’s configuration, sensor interfaces can be replaced with a bridge network interface.

Additionally, HiNAS Control collects vision data from EO/IR cameras and target detection data from systems such as AIS and radar to assess the navigational situation.
The detailed purpose and data of each interfaced equipment are described in <<table_description_of_interfaced_equipment>>.

[NOTE]
====
* Radar and ECDIS refer to X-band/S-band radar and ECDIS, which must be interfaced.
* If multiple sensors such as GPS and gyro are installed, all should be interfaced.
====

[[fig_interace_diagram_of_hinas_control]]
.Interface diagram of HiNAS
image::FUNCTIONAL_INTEGRATION/interface_diagram_of_hinas_v1.png[align="center",700,300]

[[table_description_of_interfaced_equipment]]
.Description of interfaced equipment
[cols="2,4,3", options="header"]
|===
| Equipment | Purpose | Collected Data

| Loading Computer (AMS or Draft Gauge)
| detects ship dynamic model (Draft gauge uses AMS's interface)
| Forward, aft, midship draft

| Rudder Angle Indicator
| monitors ship rudder status
| Set rudder angle, actual rudder angle

| AIS
| detects AIS-equipped target ships
| Target ship position, heading, speed

| Gyro
| identifies ship heading
| Own ship gyro heading

| Echo Sounder
| supports ship dynamics modeling in shallow waters
| Water depth at current position

| Speed Log
| measures ship speed
| Speed through water

| ECDIS
| provides route information
| Route information

| Radar
| detects radar targets
| Target ship bearing, distance

| DGPS
| provides ship position and GEO information during voyage
| Own ship position, speed over ground, course over ground

| Main Engine
| monitors propulsion system status
| Engine RPM, power, torque

| Anemometer
| monitors external environmental conditions
| Wind speed, direction

| EO/IR Camera
| detects visually identified targets
| Vision image

| Autopilot
| receives heading control commands for path tracking and collision avoidance
| Set heading, actual heading

| BMS
| receives RPM (speed) control commands for path tracking and collision avoidance
| Set RPM, actual RPM

| Speed (RPM) Optimizer
| optimizes speed planning based on weather forecasts
| Optimized speed

| VDR
| records HiNAS Control operational data (control commands, actual state) for 720 hours
| Operational data recording (control commands, actual state)

| BAMS
| bridge alert management system
| Alert information
|===