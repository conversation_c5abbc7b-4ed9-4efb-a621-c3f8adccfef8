site:
  title: HiNAS Control Operator's Manual
  # the 404 page and sitemap files only get generated when the url property is set
  url: /
  start_page: hinas-control-user-manual::home.adoc
content:
  sources:
  - url: ./
    branches: HEAD
    # setting edit_url to false disables the Edit this Page link for any page that originates from this repository
    # the same thing can be achieved by adding empty credentials (i.e., @) in front of the domain in the URL
    edit_url: false
    # start_path: main
    # version: true
  # - url: ./demo-component-b
    # branches: [main, v2.0, v1.0]
    start_path: hinas-control-user-manual
asciidoc:
  attributes:
    experimental: ''
    idprefix: ''
    idseparator: '-'
    page-pagination: ''
ui:
  bundle:
    url: ./ui-bundle.zip
    snapshot: true
output:
  clean: true
  dir: ./build/hinas-control-user-manual