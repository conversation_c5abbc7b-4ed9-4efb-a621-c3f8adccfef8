:doctype: book
:sectnums:
:sectnumlevels: 6
:toc: left
:icons: font
:front-cover-image: ./images/book-cover.png
:page-background-image: ./images/page-background.png
:pdf-theme: book-theme.yml
:math:
:imagesoutdir: generated_images
:imagesdir: images
:table-caption: table
:stem: latexmath
:xrefstyle: short



== 문서 개요 (Document Overview)

include::pages/DOCUMENT_OVERVIEW/intro.adoc[]

<<<

== 시스템 개요 (System Overview)

include::pages/SYSTEM_OVERVIEW/system_overview.adoc[]

<<<

include::pages/SYSTEM_OVERVIEW/technical_overview.adoc[]

<<<

include::pages/SYSTEM_OVERVIEW/terminology.adoc[]

<<<

include::pages/SYSTEM_OVERVIEW/functional_description.adoc[]

<<<

include::pages/SYSTEM_OVERVIEW/architecture.adoc[]

<<<

include::pages/SYSTEM_OVERVIEW/background_legal_rationale.adoc[]

<<<

include::pages/SYSTEM_OVERVIEW/configuration_management.adoc[]

<<<

== 시스템 기능 및 비기능 요구사항 (Functional & Non Functional Requirements)

include::pages/FUNCTION_REQUIREMENTS/camera_view/overview.adoc[]

<<<

include::pages/FUNCTION_REQUIREMENTS/vessel_status_indicator/overview.adoc[]

<<<

include::pages/FUNCTION_REQUIREMENTS/target_information_display/overview.adoc[]

<<<

include::pages/FUNCTION_REQUIREMENTS/inference/overview.adoc[]

<<<

include::pages/FUNCTION_REQUIREMENTS/target_ar/overview.adoc[]

<<<

include::pages/FUNCTION_REQUIREMENTS/cctv/overview.adoc[]

<<<

include::pages/FUNCTION_REQUIREMENTS/setting/overview.adoc[]


<<<
include::pages/FUNCTION_REQUIREMENTS/other/overview.adoc[]

<<<

== 인터페이스 요구사항 (INTERFACE REQUIREMENTS)

include::pages/EXTERNAL_INTERFACE_REQUIREMENTS/user_interface.adoc[]


<<<
include::pages/EXTERNAL_INTERFACE_REQUIREMENTS/hardware_interface.adoc[]

<<<

include::pages/EXTERNAL_INTERFACE_REQUIREMENTS/software_interface.adoc[]


<<<

include::pages/EXTERNAL_INTERFACE_REQUIREMENTS/communication_interface.adoc[]

<<<
== 기타 요구사항 (Other Requirements)

include::pages/OTHER_REQUIREMENTS/installation_requirements.adoc[]

<<<
== 부록 (Appendix)

include::pages/APPENDIX/references.adoc[]

<<<
include::pages/revision_history.adoc[]