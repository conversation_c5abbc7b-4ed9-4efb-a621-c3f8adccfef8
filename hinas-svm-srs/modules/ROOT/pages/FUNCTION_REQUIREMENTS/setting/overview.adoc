=== SVM Setting +



==== Overview
[cols="1,1,4", options="header"]
|===
| No.
| Function
| Description

| 1
| Ship Spec
| 선박 제원 및 센서 좌표를 입력·검증하고, 제출 시 기존 카메라 데이터를 초기화할 수 있는 설정 단계입니다.

| 2
| Camera Configuration
| 개별 카메라 X, Y, Z의 좌표를 입력하고 Ping을 통해 카메라 상태를 확인하는 API 입니다.

| 3
| Side View Calibration
| Side View 영상의 Scale, Pitch, Roll, Yaw 값을 조정하여 시야 각도를 보정하는 기능입니다. 실시간으로 반영되며, 사용자 요구에 맞는 Side View 화면을 구현할 수 있습니다.

| 4
| Surround View Calibration
| Surround View 설정을 위한 보정 기능으로, Visible Distance, 드래프트 값 입력, 마스킹 설정 등을 통해 Surround View의 품질을 최적화합니다.

| 5
| Visible Distance
| Visible Distance는 Surround View 화면에서 등거리선 기반으로 시야 범위를 설정하는 값으로, 선박 제원 및 카메라 높이에 따라 계산되며, 화면 내 거리 정보와 시각적 왜곡 최소화를 동시에 고려하여 설정됩니다.

| 6
| Inference Settings
| Setting Page의 설정값을 Inference Module에 전달할 수 있게 Storage 모듈에 값을 저장합니다.

| 7
| CCTV Settings
| CCTV와 PTZ 카메라의 영상을 등록할 수 있습니다.


|===

[IMPORTANT]
====
Side View 및 Surround View 보정 기능은 설치 환경과 카메라 조건에 따라 담당자의 시각적 판단이 개입될 수 있습니다.
따라서 정량적 기준뿐만 아니라 정성적 평가 기준도 함께 고려되어야 하며, 관련 검증 항목은 각 기능별 섹션에 상세히 기술되어 있습니다.

반면, Ship Spec, Camera Configuration, Visible Distance 항목은 정의된 수치 기반 입력과 계산 로직에 따라 동작하므로 주관적 판단이 개입되지 않습니다.
====

<<<
include::ship_spec.adoc[]

<<<
include::camera_config.adoc[]

<<<
include::side_view_calibration.adoc[]

<<<
include::surround_view_calibration.adoc[]

<<<
include::visible_distance.adoc[]

<<<
include::inference_setting.adoc[]

<<<
include::cctv_setting.adoc[]