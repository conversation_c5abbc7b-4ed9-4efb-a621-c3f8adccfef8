==== Camera Config

===== 설명

Camera Config는 선박에 카메라를 등록하거나, 설치된 카메라의 정보를 조회하고 수정하는 기능입니다.




[cols="1,2,6", options="header"]
|===
| No.
| Function
| Description

| 1
| Camera Count 및 Model 조회
| - 카메라 개수를 조회하는 기능으로, 현재 설치된 카메라의 개수를 반환합니다.

- 설치된 카메라의 모델을 조회하는 기능으로, 현재 사용 중인 카메라의 모델명을 반환합니다.
| 2
| Default Camera
| 개별 카메라 X, Y, Z의 좌표를 입력하고 Ping을 통해 카메라 상태를 확인하는 API 입니다.
| 3
| Camera Setting
| 카메라 세팅을 조회하고, 수정하는 기능.
| 4
| Camera Network
| 카메라에 Ping을 통해서, 카메라의 상태를 확인하는 기능입니다.

|===


====== 카메라 개수 및 모델

.Camera Config Count and Model
image::FUNCTION_REQUIREMENTS/setting/camera_config/camera_config_count_and_model.png[align="center",600]


*카메라 개수 모델 조회 API*

1) 설치 카메라 댓수 조회

- 기능 설명 : 현재 설치된 카메라의 개수를 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/svm-config/camera-count
- Request : 없음
- Response :
+
[source,json]
-----
{
  "code": 200000,
  "message": "Get svm_config successful.",
  "data": {
    "camera_count": 8
  }
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get svm_config successful)
+
data : 카메라 설치 개수
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| camera_count | int | 설치된 카메라 개수
|===


2) 설치 카메라 모델 조회

- 기능 설명 : 현재 설치된 카메라의 모델을 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/svm-config/camera-model
- Request : 없음
- Response :
+
[source,json]
-----
{
  "code": 200000,
  "message": "Get svm_config successful.",
  "data": {
    "model": "arges"
  }
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get svm_config successful)
+
data : 설치된 카메라 모델
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| model | string | 설치된 카메라 모델
|===
+



====== Default Camera 정보

**기본 카메라 설정 조회 API**

1) 초기 카메라 설정 조회

- 기능 설명 : 현재 설치된 카메라의 모델에 적용되는 초기 카메라 설정정보를 조회한다.
초기데이터는 최초 시스템을 구동시키기 위한 값으로만 사용 할 수 있으며, 각 선박의 상황에 따른 변경이 필요하다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/default-camera
- Request : 없음
- Response :
+
[source,json]
-----
{
    "code": 200000,
    "message": "Success",
    "data": [
        {
            "camera_count": 4,
            "data": [
                {
                    "index": 0,
                    "name": "bow",
                    "position": {
                        "x": -0.5,
                        "y": 0.0,
                        "z": -100.0
                    },
                    "rotation": {
                        "pitch": 45.0,
                        "roll": 0.0,
                        "yaw": 0.0
                    },
                    "sideview_scale": 0.1,
                    "ip": "************",
                    "intrinsic": {
                        "fx": 385.215,
                        "fy": 385.351,
                        "cx": 643.299,
                        "cy": 480.242
                    },
                    "distortion": {
                        "k1": -0.00260598516653871,
                        "k2": -0.00130293961556752,
                        "k3": -0.000970285298225214,
                        "k4": -0.000303661458364969
                    }
                }
            ]
        }
    ]
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Success)
+
camera_count : 카메라 개수
+
data : 조회된 카메라 모델, 카메라 개수에 해당하는 기본 카메라 데이터. 양이 많아 초기 1개의 값만 표기함.
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| index | int | 카메라 인덱스 (0부터 시작)
| name | string | 카메라 이름 (예: bow, stern 등)
| position(x, y, z) | float | 카메라 상대 위치 좌표 (x, y, z) - 선체 기준 (x: 좌우, y: 전후, z: 상하)
| rotation(pitch, roll, yaw) | float | 카메라 회전 각도 (pitch: 상하, roll: 좌우, yaw: 좌우 회전)
| sideview_scale | float | 개별 카메라뷰의 스케일
| ip | string | 카메라 IP 주소
| intrinsic(fx, fy, cx, cy) | float | 카메라 내부 파라미터 (fx: x 방향 초점거리, fy: y 방향 초점거리, cx: 주점 x 좌표, cy: 주점 y 좌표)
| distortion(k1, k2, k3, k4) | float | 카메라 왜곡 계수 (k1, k2, k3, k4)
|===

====== Camera 정보

.Camera Config API
image::FUNCTION_REQUIREMENTS/setting/camera_config/camera_config_api.png[align="center",600]

*Camera Config API*


1) 카메라 설정 조회
- 기능 설명 : 현재 설치된 카메라의 설정정보를 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/svm-camera
- Request : 없음
- Response :
+
[source,json]
-----
{
    "code": 200000,
    "message": "Success",
    "data": [
        {
            "index": 0,
            "name": "bow",
            "position": {
                "x": -100.0,
                "y": 0.0,
                "z": -2.0
            },
            "rotation": {
                "pitch": 45.0,
                "roll": 0.0,
                "yaw": 0.0
            },
            "sideview_scale": 0.1,
            "ip": "************",
            "intrinsic": {
                "fx": 385.215,
                "fy": 385.351,
                "cx": 643.299,
                "cy": 480.242
            },
            "distortion": {
                "k1": -0.00260598516653871,
                "k2": -0.00130293961556752,
                "k3": -0.000970285298225214,
                "k4": -0.000303661458364969
            }
        }
    ]
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Success)
+
data : 현재 저장된 카메라 데이터. 양이 많아 초기 1개의 값만 표기함.
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| index | int | 카메라 인덱스 (0부터 시작)
| name | string | 카메라 이름 (예: bow, stern 등)
| position(x, y, z) | float | 카메라 상대 위치 좌표 (x, y, z) - 선체 기준 (x: 좌우, y: 전후, z: 상하)
| rotation(pitch, roll, yaw) | float | 카메라 회전 각도 (pitch: 상하, roll: 좌우, yaw: 좌우 회전)
| sideview_scale | float | 개별 카메라뷰의 스케일
| ip | string | 카메라 IP 주소
| intrinsic(fx, fy, cx, cy) | float | 카메라 내부 파라미터 (fx: x 방향 초점거리, fy: y 방향 초점거리, cx: 주점 x 좌표, cy: 주점 y 좌표)
| distortion(k1, k2, k3, k4) | float | 카메라 왜곡 계수 (k1, k2, k3, k4)
|===
+
2) 카메라 설정 저장/수정
- 기능 설명 : 현재 설치된 카메라의 설정정보를 저장/수정한다.
- HTTP Method : POST
- Endpoint : /svm-api/v1/svm-camera
- Request :
+
[source,json]
-----
[
  {
    "index": 0,
    "name": "bow",
    "position": {
      "x": 40,
      "y": 60,
      "z": 80
    },
    "rotation": {
      "pitch": 30,
      "roll": 20,
      "yaw": 40
    },
    "sideview_scale": 0.1,
    "ip": "************",
    "intrinsic": {
        "fx": 385.215,
        "fy": 385.351,
        "cx": 643.299,
        "cy": 480.242
    },
    "distortion": {
        "k1": -0.00260598516653871,
        "k2": -0.00130293961556752,
        "k3": -0.000970285298225214,
        "k4": -0.000303661458364969
    }
  }
]
-----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| index | int | 카메라 인덱스 (0부터 시작) | required, 0 <= index < camera_count
| name | string | 카메라 이름 (예: bow, stern 등) | required, 최대 50자, 고유해야 함
| position(x, y, z) | float | 카메라 상대 위치 좌표 (x, y, z) - 선체 기준 (x: 좌우, y: 전후, z: 상하) | required, x: -ship_length =< x =< ship_length, y: -ship_beam/2 =< y =< ship_beam/2, z: -100 < z < 100
| rotation(pitch, roll, yaw) | float | 카메라 회전 각도 (pitch: 상하, roll: 좌우, yaw: 좌우 회전) | required, pitch: 0 <= pitch <= 90, roll: -45 <= roll <= 45, yaw: -45 <= yaw <= 45
| sideview_scale | float | 개별 카메라뷰의 스케일 | required, 0.01 <= sideview_scale <= 1.0
| ip | string | 카메라 IP 주소 | required, 유효한 IP 형식 (예:************)
| intrinsic(fx, fy, cx, cy) | float | 카메라 내부 파라미터 (fx: x 방향 초점거리, fy: y 방향 초점거리, cx: 주점 x 좌표, cy: 주점 y 좌표) | required
| distortion(k1, k2, k3, k4) | float | 카메라 왜곡 계수 (k1, k2, k3, k4) | required
|===
- Response :
+
[source,json]
-----
{
    "code": 201000,
    "message": "Create camera Successful.",
    "data": [
        {
            "index": 0,
            "name": "bow",
            "position": {
                "x": -100.0,
                "y": 0.0,
                "z": -5.0
            },
            "rotation": {
                "pitch": 45.0,
                "roll": 0.0,
                "yaw": 0.0
            },
            "sideview_scale": 0.1,
            "ip": "************",
            "intrinsic": {
                "fx": 385.215,
                "fy": 385.351,
                "cx": 643.299,
                "cy": 480.242
            },
            "distortion": {
                "k1": -0.00260598516653871,
                "k2": -0.00130293961556752,
                "k3": -0.000970285298225214,
                "k4": -0.000303661458364969
            }
        }
    ]
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (201000)
+
message : 응답 메시지 (Create camera Successful)
+
data : 저장된 카메라 데이터. 양이 많아 초기 1개의 값만 표기함.
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| index | int | 카메라 인덱스 (0부터 시작)
| name | string | 카메라 이름 (예: bow, stern 등)
| position(x, y, z) | float | 카메라 상대 위치 좌표 (x, y, z) - 선체 기준 (x: 좌우, y: 전후, z: 상하)
| rotation(pitch, roll, yaw) | float | 카메라 회전 각도 (pitch: 상하, roll: 좌우, yaw: 좌우 회전)
| sideview_scale | float | 개별 카메라뷰의 스케일
| ip | string | 카메라 IP 주소
| intrinsic(fx, fy, cx, cy) | float | 카메라 내부 파라미터 (fx: x 방향 초점거리, fy: y 방향 초점거리, cx: 주점 x 좌표, cy: 주점 y 좌표)
| distortion(k1, k2, k3, k4) | float | 카메라 왜곡 계수 (k1, k2, k3, k4)
|===



====== 카메라 네트워크 상태체크


.Camera Network
image::FUNCTION_REQUIREMENTS/setting/camera_config/camera_config_camera_network.png[align="center",600]

*카메라 Network Check API*

1) 카메라 네트워크 상태 체크

- 기능 설명 : 선박의 카메라와 서버와의 네트워크 연결 상태를 확인한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/network-status/camera_all
- Request : count (카메라 개수), /svm-api/v1/network-status/camera_all?count=8
- Response :
+
[source,json]
-----
{
    "code": 200000,
    "message": "Get NetworkStatus List successful.",
    "data": {
        "result": false,
        "network_list": [
            {
                "ip": "************",
                "status": false
            },
            {
                "ip": "************",
                "status": false
            },
            {
                "ip": "************",
                "status": false
            },
            {
                "ip": "************",
                "status": true
            },
            {
                "ip": "************",
                "status": false
            },
            {
                "ip": "************",
                "status": true
            },
            {
                "ip": "************",
                "status": true
            },
            {
                "ip": "************",
                "status": true
            }
        ]
    }
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get NetworkStatus List successful)
+
data : 카메라 네트워크 상태 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| ip | string | 카메라 IP 주소
| status | boolean | 카메라와 서버 간의 네트워크 연결 상태 (true: 연결됨, false: 연결 안됨)
|===



====== Arges 카메라 개별 캘리브레이션

.Camera Config Arges Calibration
image::FUNCTION_REQUIREMENTS/setting/camera_config/camera_config_arges_camera_calibartion_api.png[align="center",600]


1) 카메라 개별캘리브레이션 정보 조회

- 기능 설명 : 각 카메라의 개별 캘리브레이션 정보를 조회하여 camera 정보에 업데이트 한다.
- HTTP Method : GET
- Endpoint :/svm-api/v1/arges-camera/calibration
- Request : camera_ip (카메라 IP 주소), ex) /svm-api/v1/arges-camera/calibration?camera_ip=************
- Response :
+
[source,json]
-----
{
    "intrinsic": {
        "fx": 385.215,
        "fy": 385.351,
        "cx": 643.299,
        "cy": 480.242
    },
    "distortion": {
        "k1": -0.00260598516653871,
        "k2": -0.00130293961556752,
        "k3": -0.000970285298225214,
        "k4": -0.000303661458364969
    },
    "homography": {
        "h00": -0.9999862922474267,
        "h01": -0.08594936875152151,
        "h02": -67.66656957734234,
        "h10": 0.005247029381793636,
        "h11": 0.9513058804261553,
        "h12": 96.46860659758295,
        "h20": 0.0,
        "h21": -5.6967395333e-05,
        "h22": 1.044950034232727
    },
    "fov": 180.0
}
-----
- 응답 필드 설명:
+
intrinsic : 카메라 내부 파라미터
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| fx | float | x 방향 초점거리
| fy | float | y 방향 초점거리
| cx | float | 주점 x 좌표
| cy | float | 주점 y 좌표
|===
distortion : 카메라 왜곡 계수
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| k1 | float | 왜곡 계수 k1
| k2 | float | 왜곡 계수 k2
| k3 | float | 왜곡 계수 k3
| k4 | float | 왜곡 계수 k4
|===
homography : 카메라 Homography 행렬, inference_input에 저장되는 월드 좌표계에서 사이드뷰로 변환하는 Homography 행렬
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| h00 | float | Homography 행렬 요소 h00
| h01 | float | Homography 행렬 요소 h01
| h02 | float | Homography 행렬 요소 h02
| h10 | float | Homography 행렬 요소 h10
| h11 | float | Homography 행렬 요소 h11
| h12 | float | Homography 행렬 요소 h12
| h20 | float | Homography 행렬 요소 h20
| h21 | float | Homography 행렬 요소 h21
| h22 | float | Homography 행렬 요소 h22
|===
+
fov : 카메라 시야각 (Field of View)
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| fov | float | 카메라 수평 시야각 (Field of View)
|===


===== 실행 조건

- 시스템은 정상적으로 기동된 상태여야 하며, SVM Backend API가 활성화되어 있어야 함

- Redis, Camera 장치(IP 기반 네트워크 연결 장비), Arges 카메라 HTTP API 연동 가능 상태일 것

- API 호출 시 필요한 카메라 개수(camera_count)는 HiNAS 365에서 등록되어야 함.

- 설정 변경 시, 입력 값은 각 필드의 제약 조건을 만족해야 하며, 형식 오류 발생 시 400 Bad Request 반환

- Arges 카메라인 경우, Arges 카메라가 개별 Calibration이 가능한 상태여야 하며, 해당 API를 통해 카메라의 내부 파라미터와 왜곡 계수를 조회할 수 있어야 함

===== 검증 기준

- 카메라 개수 조회 API 호출 시, HiNAS 365에 등록된 카메라 수와 정확히 일치해야 함

- 카메라 모델 조회 API 결과는 HiNAS 365에 등록된 모델명과 정확히 일치해야 함

- /svm-camera GET 호출 시, 저장된 카메라의 좌표/회전각/내부 파라미터가 모두 포함되어야 하며, 누락된 필드가 없어야 함

- POST로 전달된 설정값 저장 후 재조회 시, 값이 정확히 반영되어야 함

- /network-status/camera_all 호출 시, 실제 네트워크 연결 여부와 응답 결과(status)가 일치해야 함

- Arges 캘리브레이션 API 호출 시, 반환된 intrinsic, distortion, homography, fov 값은 float 타입으로 반환되어야 하며 0 또는 null 값이 없어야 함




*오차 허용 범위*

[IMPORTANT]
====
카메라 설치는 설계 도면상 위치와 실제 설치 위치 간에 불일치가 발생할 수 있습니다. 이는 선박 구조, 시공 오차, 설치 환경 등의 다양한 요인에 의해 발생하며, 현장 상황에서는 이러한 불일치를 완전히 제거하기 어렵습니다.

따라서 시스템은 *설계 위치와 실제 설치 위치 간의 오차가 존재하더라도 정상적으로 동작해야 하며*, 이러한 조건 하에서도 모든 기능이 기대한 수준의 정확도와 안정성을 유지하는지를 *반드시 검증해야 합니다*.

이러한 점을 고려하여 보정값의 정합성은 단순 수치 비교가 아닌 *영상 일관성*, *구조물 연결 상태*, *시야 왜곡 등 시각적 품질 기준을 포함한 종합적 관점에서 검증*해야 합니다.

해당 항목은 *중요 검증 항목(Important Validation Item)*으로 간주되며, *필수 테스트 항목에 포함되어야 합니다*.
====

시스템의 안정성과 사용성 확보를 위해 아래와 같은 오차 범위를 기준으로 설정합니다.

[cols="1,2,2", options="header"]
|===
| 항목 | 허용 오차 | 예시 (선체 Length 100m, Beam 50m, Scantling Draught 5m 기준)

| `x` (좌우 방향) | 기준 Length ±2.0% 이하 | ±2.0m (100m × 0.02)
| `y` (전후 방향) | 기준 Beam ±2.0% 이하 | ±1.0m (50m × 0.02)
| `z` (상하 방향) | 기준 Scantling Draught의 ±2.0% 이하 | ±0.1m (5m × 0.02)
|===

[IMPORTANT]
====
Measured Draught는 자선의 실시간 흘수 정보를 나타내며, 카메라의 `z` 위치 보정 시 반드시 함께 고려되어야 합니다. Scantling Draught 기준만으로 보정할 경우 실제 수면과의 상대 위치 오차가 발생할 수 있으므로, 영상 정합 및 수직 정렬 정확도를 확보하기 위해 *Measured Draught 값과 연계한 보정 및 검증이 필수*입니다.
====

해당 오차범위내에서 Surrond View 및 Side View 영상이 정상적으로 현시되어야 합니다.


===== 보정 오차 발생 시 기능 정상 동작 검증 항목 (Functional Integrity Under Calibration Deviation)

|===
| 검증 항목 | 설명 | 검증 방법 | 기준

| Surround View 생성
| Side View 영상이 틀어져 있어도 Surround View 영상 생성 및 표시가 정상인지 확인
| Surround View UI 확인 및 왜곡·깨짐 여부 점검

- Surround View에 보이는 타선이 이동 시, Surround View의 카메라 겹침(Overlapping) 구간에서도 영상 정합성 유지 여부 확인 +

- 타선의 외곽선(윤곽선)이 왜곡되거나 연결되지 않는 경우 없는지 확인

| 영상 생성 및 시각 정렬 문제 없음

- 겹쳐지는 영역에서도 영상 왜곡 또는 불연속 현상 없어야 함

| Target 정보 표시 (AIS/ARPA)
| 틀어진 영상에서도 AIS/ARPA 대상의 위치 및 정보가 정확하게 표시되는지
| 대상 아이콘 위치, CPA/TCPA 정보 비교
| 위치 정확도 ±0.5m, 데이터 일치

| UI 조작 응답성
| 영상이 틀어진 상태에서 UI 조작(슬라이더, 확대/축소 등)이 정상 반응하는지
| 슬라이더, 클릭 이벤트 반응 확인
| 응답 지연 없이 반영 (500ms 이내)

| 영상 기록 기능
| 영상 왜곡 상태에서도 기록된 영상이 재생되고 시간 일치하는지
| 영상 재생 시간 및 프레임 연속성 확인
| 시간 일치, 재생 오류 없음

| 위험 구역 경고 표시
| Side View에 설정된 위험 구역(예: 붉은 영역)이 영상 오차에도 정확히 표시되는지
| 경고 오버레이 위치 확인
| 오차 ±0.5m 이내 유지

| 시스템 안정성
| 보정값이 극단값(Pitch 90도 등)일 경우에도 시스템이 Crash 없이 동작하는지
| 설정 극단값 입력 후 시스템 반응 확인
| 오류 없이 정상 동작 유지

| 데이터 저장 및 복원
| 오차 있는 설정 상태 저장 후 재부팅해도 값이 복원되는지
| 설정 저장 → 재부팅 → 값 비교
| 값 일치 및 영상 반영 확인
|===



===== 성능 요구사항

*API 응답 시간 성능*
- 모든 GET API 응답 시간: 500ms 이내


*내부 파라미터 조회 실패 시 동작 안정성*

- Arges 카메라로부터 캘리브레이션 데이터를 조회하는 `/svm-api/v1/arges-camera/calibration` API 호출 시, 일시적인 네트워크 오류나 카메라 응답 지연으로 인해 intrinsic 또는 distortion 정보가 수신되지 않더라도, 전체 시스템은 중단 없이 **정상 동작을 유지**해야 합니다.

- 내부 파라미터(intrinsic, distortion)가 조회되지 않은 카메라에 대해서는, 기본 설정 또는 이전에 저장된 값으로 자동 대체하여 영상 출력 및 UI 렌더링에 문제가 발생하지 않아야 합니다.

- 파라미터 조회 실패 상태에서도 Surround View 및 Side View UI가 로딩 실패, 렌더링 오류, 시스템 크래시 없이 정상적으로 구동되어야 하며, 사용자에게는 해당 카메라의 보정 정보 수신 실패에 대한 명확한 경고 메시지가 UI 또는 로그로 출력되어야 합니다.

- 이와 같은 실패 상황에서도 전체 API(`/svm-camera`, `/network-status/camera_all`, `/svm-config/camera-count`) 호출에 영향이 없어야 하며, 다른 카메라 설정 및 영상 처리에는 지장이 없어야 합니다.



*카메라 네트워크 상태 반복 요청 처리*

- `/network-status/camera_all` API는 사용자가 UI에서 1초 간격으로 10회 이상 연속적으로 요청(Ping)을 수행하더라도, 모든 요청에 대해 정확하고 안정적인 응답을 반환해야 합니다.

- 반복 요청 중 각 응답의 최대 지연 시간은 1초 이내여야 하며, 전체 요청 중 95% 이상의 응답은 700ms 이내에 완료되어야 합니다.

- 연속적인 Ping 요청 처리 중에도 SVM Backend의 CPU 사용률은 50% 이하, 메모리 사용량은 200MB 이하로 유지되어야 하며, 시스템의 안정성이나 타 서비스 성능에 영향을 주지 않아야 합니다.

- 사용자의 빠른 연타 클릭이나 자동 모니터링 기능이 동시에 동작하는 경우에도, 네트워크 상태 응답 결과가 누락되거나 잘못 반환되어서는 안 됩니다.

- 시스템은 병렬 처리를 통해 모든 카메라의 네트워크 상태를 신속하게 검사하고 응답할 수 있어야 하며, 카메라 수가 증가(최대 : 10대) 하더라도 선형 성능 저하 없이 동작해야 합니다.


===== 인터페이스 요구사항

1. API 형식 및 통신 프로토콜
- 모든 API는 RESTful 방식으로 설계되어야 하며, HTTP/2 프로토콜을 지원해야 한다.
- 데이터 전송은 JSON 포맷을 기본으로 하며, 요청(Request) 및 응답(Response)의 Content-Type은 `application/json`으로 명시해야 한다.
- 요청 본문은 UTF-8 인코딩을 사용해야 한다.

2. HTTP Method 규칙
- 데이터 조회는 GET, 신규 등록은 POST, 설정 수정은 POST 또는 PUT을 사용해야 한다.
- 각 Method는 의도한 목적 외로 사용하지 않아야 하며, RESTful 원칙을 준수해야 한다.

3. 엔드포인트 URL 규칙
- 모든 API는 /svm-api/v1/ 접두 경로를 사용하며, 기능에 따라 명확하게 구분된 경로를 사용해야 한다.
예:
+
[source]
----
/svm-api/v1/ship-specs
/svm-api/v1/svm-camera
/svm-api/v1/calibration/surround-view
/svm-api/v1/network-status/camera_all
----

4. 응답 포맷 및 응답 코드
- 응답은 공통 구조를 따라야 하며, 다음 필드를 포함해야 한다.
+
[source,json]
----
{
  "code": 200000,
  "message": "Success",
  "data": { ... }
}
----
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| code | int | 응답 코드 (200000: 조회 성공, 201000: 등록/수정 성공, 400000: 유효성 오류, 500000: 시스템 오류 등)
| message | string | 요청 처리 결과 메시지
| data | object or array or null | 요청 결과 데이터
|===

5. 인증 및 보안
- API는 내부망에서 접근하도록 설계되어 있으며, 다음 방식으로 인증이 적용될 수 있다:
  - OAuth2 Bearer Token 방식
- HTTPS를 통한 암호화 통신을 지원하며, TLS 1.2 이상 사용을 권장한다.

6. 에러 처리 규칙
- 요청 실패 시 다음 형식의 에러 응답을 제공해야 한다.
+
[source,json]
----
{
  "code": 400000,
  "message": "Invalid parameter: 'beam' must be > 0 and < 100.",
  "data": null
}
----
- 잘못된 요청, 누락된 필드, 유효하지 않은 포맷 등은 code: 404000으로 응답하며, 오류 메시지는 사용자 또는 개발자가 쉽게 원인을 파악할 수 있도록 구체적이어야 한다.


7. 호환성
- 본 API는 SVM 시스템 v1.0과 호환되며, 향후 버전 업그레이드 시 하위 호환성을 유지해야 한다.

===== 기타 요구사항

1. 시스템 초기화 및 설정 유지
- SVM 시스템은 설정값을 비휘발성 저장소에 유지하여 시스템 재시작 후에도 기존 설정이 유지되어야 한다.
- Default Camera 설정은 SVM Backend에 저장된 초기값이며, 실운용 환경에서는 반드시 사용자에 의해 조정되어야 함
2. 설정값 유효성 검증
- 모든 설정값은 API 레벨에서 유효성 검사가 선행되어야 하며, UI 상에서도 기본값과 허용 범위를 명시해야 한다.
- 숫자 범위 제한, 필수 항목 누락, 중복 IP/이름 등은 저장 전에 차단되어야 한다.
3. 운용 환경 제약
- 설정 페이지는 내부망에서만 접근 가능해야 하며, 외부 인터넷망을 통한 접근은 허용되지 않는다.

