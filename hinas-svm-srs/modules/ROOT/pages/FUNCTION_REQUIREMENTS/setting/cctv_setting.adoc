==== CCTV 설정

===== 설명

.CCTV And PTZ Setting
image::FUNCTION_REQUIREMENTS/setting/cctv_and_ptz/cctv_and_ptz_setting_1.png[align="center",600]

.CCTV And PTZ Setting Input
image::FUNCTION_REQUIREMENTS/setting/cctv_and_ptz/cctv_and_ptz_setting_2.png[align="center",600]


*CCTV 주요 기능 및 API 상세.*

1) CCTV 정보 조회

- 기능 설명 : 현재 저장된 CCTV 설정 정보를 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/cctv
- Request : 없음
- Response :
+
[source,json]
-----
{
    "code": 200000,
    "message": "Get cctv setting successful.",
    "data": [
        {
            "name": "cctv1",
            "ip": "************",
            "port": 554,
            "id": "avikus",
            "pw": "avi1357!!",
            "server_url": "/media/1/Profile1",
            "type": "cctv"
        }
    ]
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get cctv setting successful)
+
data : CCTV 설정 정보 리스트
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| name | string | CCTV 이름 (예: cctv1)
| ip | string | CCTV IP 주소 (예:************)
| port | int | CCTV 포트 번호 (예: 554)
| id | string | CCTV 로그인 ID (예: avikus)
| pw | string | CCTV 로그인 비밀번호 (예: avi1357!!)
| server_url | string | CCTV 서버 URL (예: /media/1/Profile1)
| type | string | CCTV 타입 (예: cctv)
|===


2) CCTV 정보 등록/수정

- 기능 설명 : CCTV 설정 정보를 등록/수정한다.
- HTTP Method : POST
- Endpoint : /svm-api/v1/cctv
- Request :
+
[source,json]
-----
{
  "data": [
    {
      "name": "cctv1",
      "ip": "************",
      "port": 554,
      "id": "avikus",
      "pw": "avi1357!!",
      "server_url": "/media/1/Profile1",
      "type": "cctv"
    }
  ]
}
-----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| data | List[object] | CCTV 설정 정보 리스트 | required, 최소 1개 이상의 CCTV 정보 필요
| name | string | CCTV 이름 (예: cctv1) | required, 최대 50자
| ip | string | CCTV IP 주소 (예:************) | required, 유효한 IP 형식
| port | int | CCTV 포트 번호 (예: 554) | required, 1 <= port <= 65535
| id | string | CCTV 로그인 ID (예: avikus) | required
| pw | string | CCTV 로그인 비밀번호 (예: avi1357!!) | required
| server_url | string | CCTV 서버 URL (예: /media/1/Profile1) | required
| type | string | CCTV 타입 (예: cctv) | required, 고정값 "cctv"
|===
- Response :
+
[source,json]
-----
{
    "code": 201000,
    "message": "Add cctv setting successful.",
    "data": [
        {
            "name": "cctv1",
            "ip": "************",
            "port": 554,
            "id": "avikus",
            "pw": "avi1357!!",
            "server_url": "/media/1/Profile1",
            "type": "cctv"
        }
    ]
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (201000)
+
message : 응답 메시지 (Add cctv setting successful)
+
data : 저장된 CCTV 설정 정보 리스트

===== 실행 조건

* Backend가 정상적으로 구동 중이어야 하며, Storage 모듈이 접근 가능해야 하며, Mediamtx 설정 경로가 접근 가능해야 함
* 등록/수정 요청 시 최소 1개 이상의 유효한 CCTV 정보가 포함되어야 함
* 조회 및 등록 요청은 인증된 Admin 이상 권한을 가진 사용자만 수행 가능함

===== 검증 기준

* 설정 조회 시 저장된 모든 CCTV 정보가 정확히 반환되어야 함
* 등록/수정 요청 시 유효성 검증에 통과한 정보만 반영되어야 함
* Redis Key(`svm:cctv`)에 저장된 값과 API 응답 값이 일치해야 함
* Mediamtx 설정 파일(`/data/hdd/cctv-config/mediamtx.yml`)의 `paths` 항목에 CCTV 경로가 생성되어야 함
* 서버 재기동 후에도 설정값이 유지되어야 함

===== 성능 요구사항

* CCTV 설정 조회 응답 시간은 300ms 이내이어야 함
* CCTV 설정 등록/수정 후 Mediamtx 설정 반영까지 최대 1초 이내에 완료되어야 함
* 최대 10대의 CCTV가 등록되어 있어도 성능 저하 없이 처리되어야 함
* 설정 요청 연속 10회 처리 시에도 99.9% 이상 성공률을 보장해야 함


===== 인터페이스 요구사항

* API 응답은 공통 응답 포맷(code, message, data)을 따라야 함
* 요청은 `Content-Type: application/json` 형식이어야 하며, 유효하지 않은 요청은 400 오류 반환
* 모든 필드는 명시된 타입과 제약 조건을 따라야 하며, 누락되거나 유효하지 않은 경우 예외 발생
* IP는 RFC 791 포맷의 유효한 IPv4 형식을 따라야 하며, 포트는 1~65535 범위의 정수만 허용됨
* `type` 필드는 고정값 `"cctv"`로 제한되어야 함


===== 기타 요구사항

* 동일한 name, ip 조합이 이미 존재할 경우 에러를 반환해야 하며, 중복 등록은 허용되지 않음
* Mediamtx 설정 파일 저장 시 YAML 포맷 에러 발생 시 예외 처리 및 사용자에게 알림해야 함.
