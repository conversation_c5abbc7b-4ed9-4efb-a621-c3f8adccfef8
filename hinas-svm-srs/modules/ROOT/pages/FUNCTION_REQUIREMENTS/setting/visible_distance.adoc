==== Visible Distance

===== 설명



*Visible Distance*

.Variables Used in Visible Distance Calculation
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_variables_used_in_calculation.png[align="center",600]


- 정의 : 선박의 Port(좌현) 끝 지점에서 Surround View 화면 좌측 끝까지의 거리

- 선박의 좌측 끝에서 **Surround View 화면의 가장자리에 해당하는 최대 시야 거리**로, 등거리선의 범위와 비례하여 화면상에 표시됩니다.

- 화면상에서 Port에서의 Surround View 화면 좌측 거리와 Starboard에서의 Surround View 화면 우측 거리는 동일하므로, Visible Distance는 선박의 Starboard(우현) 끝에서의 Surround View 화면 우측까지의 거리로 정의될 수 있음.

- 이는 **등거리선** 개념으로 표현되며, **Surround View 화면에서 Port 방향으로 시각화할 수 있는 최대 거리**를 나타냅니다. (단위: m)

- 즉, **등거리선**을 그릴 수 있는 **최대 범위**를 의미합니다.



*Visible Distance와 등거리선*

.등거리선 (3개)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_above_recommend_view.png[align="center",600]


.등거리선 간격 조정 (4단계)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_recommended_equip_distance.png[align="center",600]



[source,math]
----
등거리선 간격 = 최소 간격 × N  (N: 1 이상 4 이하의 정수)
Visible Distance = 선택된 등거리선 간격 × 3
----

- 등거리선은 항상 3개이며, 각 선의 간격은 동일합니다.
- 간격은 사용자가 선택할 수 있는 **4단계 설정값(1~4단계)** 으로 구성됩니다.
- 따라서 **Visible Distance**는 등거리선 간격에 따라 자동으로 결정되며, **등거리선 최소 간격**은 다음과 같이 정의됩니다:

[source,math]
----
min_equip_distance_gap = Visible Distance ÷ 12
----

예시) Visible Distance가 60m인 경우:

- 등거리선 3개가 60m 내에 위치하며, 설정에 따라 다음과 같이 배치됩니다:

[cols="1,1,2", options="header"]
|===
|설정 단계 |간격 (m) |등거리선 위치 (거리 기준)
|1단계     |5        |5m, 10m, 15m
|2단계     |10       |10m, 20m, 30m
|3단계     |15       |15m, 30m, 45m
|4단계     |20       |20m, 40m, 60m
|===



*Visible Distance 계산 공식*

.Variables Used in Visible Distance Calculation
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_variables_used_in_calculation.png[align="center",600]

*Visible Distance Formula Input*

*선박 제원 정보*

[cols="1,2,1,1", options="header"]
|===
| 변수명       | 설명                      | 단위 | 입력 범위

| Length       | 선박의 길이                | m    | 0 ~ 600
| Beam         | 선박의 폭                  | m    | 0 ~ 150
| h_port       | Port(좌현) 카메라 높이     | m    | ?
| h_starboard  | Starboard(우현) 카메라 높이| m    | ?
| h_bow        | Bow(선수) 카메라 높이      | m    | ?
| h_stern      | Stern(선미) 카메라 높이    | m    | ?
|===

*화면 정보*

[cols="1,2,1,1", options="header"]
|===
| 변수명 | 설명                   | 단위   | 고정값

| Width  | Surround View 화면 가로 해상도        | pixel | 588
| Height | Surround View 화면 세로 해상도        | pixel | 982
|===



*Visible Distance Formula Output*

- Visible Distance: 선박의 Port 끝에서 Surround View 화면 좌측 끝까지의 거리 (m)
- min_equip_distance_gap : 등거리선 최소 간격

*Visible Distance Formula*

.Visible Distance Formula
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_formula.png[align="center",600]


*Visible Distance 선박 크기 지원*

- Visible Distance 기반으로 다양한 선박 비율에 따른 화면 표시를 지원합니다.

- Length 대비 Beam 비율이 최소 1:1부터 최대 25:1까지인 선박 이미지의 화면 표시를 지원합니다.

- 25:1 비율의 선박 이미지에서도, 권장 Visible Distance를 적용하면 등거리선은 항상 Surround View 화면 내에 표시됩니다.

.1:1 Aspect Ratio Ship
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_ship_image_1_1.png[align="center",500]

.25:1 Aspect Ratio Ship
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_ship_image_25_1.png[align="center",500]


*Visible Distance 별 Surround View 선박 사이즈 비교*


**추천 Visible Distance 사용 시 - 선박 크기 동적 계산 (예: 추천 67, 현재 67)**

- **추천 Visible Distance** 값에 따라 선박의 크기는 Ship Spec (선박 사양)에 맞게 **동적으로 계산**되어 표시됩니다.
- 이 값은 선박의 크기, 형태 및 카메라 설치 위치를 기준으로 최적화된 크기 비율로 조정됩니다.

.Visible Distance Setting (Recommended)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_recommended_setting.png[align="center",600]

.Visible Distance Equip Distance (Recommended)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_recommended_equip_distance.png[align="center",600]


**추천 값보다 작은 Visible Distance 사용 시 (예: 추천 67, 현재 40) - 선박 크기 커짐**

- **Visible Distance** 값이 추천 값보다 **작아지면**, 화면상에 **선박의 크기가 커지게** 됩니다.
- 이는 화면에 더 가까운 범위만을 표시하게 되며, 상대적으로 선박이 크게 보이게 됩니다.
- 등거리선의 **범위가 좁아져 가까운 범위까지 표현**됩니다.
- 추천값보다 작게 Visible Distance를 설정한 경우 등거리선이 Surround View 화면 밖으로 나올 수 있습니다.


.Visible Distance Setting (Below Recommended)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_recommended_setting.png[align="center",600]

.Visible Distance Equip Distance (Below Recommended)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_recommended_equip_distance.png[align="center",600]

- 등거리선이 Surround View 화면을 벗어나는 경우, Calibration Viewer 단계에서 경고 메시지가 표시됩니다.

- `Waring: The bow & stern equipment distance exceeds the displayable range of the Surround View.`

.Visible Distance Setting Warning Message (Below Recommended)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_below_recommend_setting_warning_message.png[align="center",600]


**추천 값보다 큰 Visible Distance 사용 시  (예: 추천 67, 현재 90) - 선박 크기 작아짐**

- **Visible Distance** 값이 추천 값보다 **크면, 선박의 크기가 작게 표시**됩니다.
- 이는 화면에 더 넓은 범위가 표시되어, 선박이 상대적으로 작게 나타나게 됩니다:
- 등거리선의 **범위가 넓어져 더 먼 거리까지 표현**됩니다.
- **Visible Distance가 추천 값 이상**일 경우, 등거리선은 항상 **Surround View 화면 내에 유지**됩니다.

.Visible Distance Setting (Above Recommended)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_above_recommend_setting.png[align="center",600]

.Visible Distance Equip Distance (Above Recommended)
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_above_recommend_equip_distance.png[align="center",600]

*Visible Distance에 따른 등거리선 계산*




*Visible Distance API*

- 사용자는 설정화면에서 Visible Distance를 설정할 수 있습니다.
- 사용자가 설정화면에서 Visible Distance를 설정하거나, 카메라 위치 정보를 입력할 경우, 시스템은 Visible Distance를 계산하여 저장합니다.

===== 실행 조건

*선박 제원 정보*

- Length, Beam, h_port, h_stbd, h_bow, h_stern 입력 범위내에 위치해야 함.


===== 검증 기준


*1) 수식 기반 산출값 검증*

*검증 목적*

- Recommended Visible Distance 값이 사양서에 정의된 공식에 따라 정확히 계산되었는지를 정량적으로 확인한다.

*검증 방법*

- 시스템이 산출한 visible_distance 값을 저장

- 입력 변수(h_port, h_stbd, h_bow, h_stern 등)를 수동으로 입력한 후 공식에 대입하여 수작업으로 계산

- 두 값(자동 산출값 vs. 수식 계산값)을 비교

*Visible Distance 수식*

.Visible Distance Formula
image::FUNCTION_REQUIREMENTS/setting/visible_distance/visible_distance_formula.png[align="center",600]


*판정 기준*

- 시스템에서 계산한 값과, 수식에 따라 수동으로 계산한 값이 소수점 이하 1자리까지 일치해야 함

- 입력 값이 변경되었을 경우, visible_distance가 수식에 따라 즉각 반영될 것



*1) 형상 왜곡 허용 범위 검증*

*검증 목적*

- 자동 산출된 Recommended Visible Distance 값이 시야 확보를 보장하면서도, 시각적 왜곡이 허용 가능한 범위 내에 있는지를 확인한다.

*검증 방법*

- Visible Distance 범위 내에 기준 물체를 실제 배치하고, Surround View 화면 상에서 해당 물체의 시각적 왜곡 여부를 확인한다.

*판정 기준*

- 화면 상에 표시되는 기준 물체의 종횡비(Aspect Ratio) 왜곡률이 10% 이하일 것

- 물체가 시각적으로 명확히 식별 가능하며, 과도한 형상 찌그러짐이 발생하지 않을 것


*2) 시야 확보 검증 (Tug 접근 상황 포함)*

*검증 목적*

- 선박 주변 상황을 충분히 감지할 수 있도록, 실질적인 시야 확보가 보장되는지를 확인한다.

*검증 시나리오*

- 선박 양측(Port/Stbd)에 Tug(예선)가 근접하거나 부착되는 상황을 가정

- 해당 상황에서 Surround View 화면 내에 Tug의 전체 또는 대부분이 포함되어야 함

*판정 기준*

- Tug의 선체가 Surround View 화면 내에 명확히 표시되어야 함

- Tug와 본선 간의 간격, 움직임 여부 등 주변 상황이 시각적으로 인지 가능한 수준으로 확보되어야 함

- 사각지대(Blind Spot)가 존재하지 않아야 하며, 사용자 조작 없이 기본 Visible Distance 설정만으로도 시야 확보가 이루어져야 함

*3) 선박 형상별 대응 검증*

*검증 목적*

- 다양한 선박 형상(Aspect Ratio)에 대해 Recommended Visible Distance가 일관되게 적용되는지를 확인한다.

*검증 대상*

- 1:1 비율 선박: Surround View 화면이 세로 방향으로 과도하게 압축되거나, 가로로 늘어져 보이지 않아야 함

- 25:1 비율 선박: Surround View 화면이 좌우로 지나치게 확장되거나, 선박이 과도하게 작게 표현되지 않아야 함

*판정 기준*

- 각 형상에 대해 Surround View 화면이 균형 있게 렌더링되며, 왜곡 없이 선박 전체 형상이 유지되어야 한다.


*3) 등거리선 계산 검증*


===== 성능 요구사항

- 사용자가 Visible Distance를 변경할 경우, 최대 2초 이내에 Surround View 화면에 반영되어야 합니다.

===== 인터페이스 요구사항

===== 기타 요구사항
