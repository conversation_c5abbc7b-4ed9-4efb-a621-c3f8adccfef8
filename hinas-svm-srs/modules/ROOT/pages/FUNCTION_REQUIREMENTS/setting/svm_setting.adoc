==== SVM Setting

===== 설명

이 페이지는 SVM 시스템의 설정을 관리하는 기능을 제공한다.
이 페이지는 선박 정보 등록, 시스템 기본 설정, 카메라 및 CCTV 설정 등을 포함하며,
커미셔너가 SVM 시스템을 초기 설정하는 데 사용됩니다.
본 문서는 설정에 사용되는 API 사양, 실행 조건, 검증 기준, 성능 및 인터페이스 요구사항을 상세히 기술한다.


+
3.4 카메라 설정 저장/수정
- 기능 설명 : 현재 설치된 카메라의 설정정보를 저장/수정한다.
- HTTP Method : POST
- Endpoint : /svm-api/v1/svm-camera
- Request :
+
[source,json]
-----
[
  {
    "index": 0,
    "name": "bow",
    "position": {
      "x": 40,
      "y": 60,
      "z": 80
    },
    "rotation": {
      "pitch": 30,
      "roll": 20,
      "yaw": 40
    },
    "sideview_scale": 0.1,
    "ip": "************",
    "intrinsic": {
        "fx": 385.215,
        "fy": 385.351,
        "cx": 643.299,
        "cy": 480.242
    },
    "distortion": {
        "k1": -0.00260598516653871,
        "k2": -0.00130293961556752,
        "k3": -0.000970285298225214,
        "k4": -0.000303661458364969
    }
  }
]
-----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| index | int | 카메라 인덱스 (0부터 시작) | required, 0 <= index < camera_count
| name | string | 카메라 이름 (예: bow, stern 등) | required, 최대 50자, 고유해야 함
| position(x, y, z) | float | 카메라 상대 위치 좌표 (x, y, z) - 선체 기준 (x: 좌우, y: 전후, z: 상하) | required, x: -ship_length =< x =< ship_length, y: -ship_beam/2 =< y =< ship_beam/2, z: -100 < z < 100
| rotation(pitch, roll, yaw) | float | 카메라 회전 각도 (pitch: 상하, roll: 좌우, yaw: 좌우 회전) | required, pitch: 0 <= pitch <= 90, roll: -45 <= roll <= 45, yaw: -45 <= yaw <= 45
| sideview_scale | float | 개별 카메라뷰의 스케일 | required, 0.01 <= sideview_scale <= 1.0
| ip | string | 카메라 IP 주소 | required, 유효한 IP 형식 (예:************)
| intrinsic(fx, fy, cx, cy) | float | 카메라 내부 파라미터 (fx: x 방향 초점거리, fy: y 방향 초점거리, cx: 주점 x 좌표, cy: 주점 y 좌표) | required
| distortion(k1, k2, k3, k4) | float | 카메라 왜곡 계수 (k1, k2, k3, k4) | required
|===
- Response :
+
[source,json]
-----
{
    "code": 201000,
    "message": "Create camera Successful.",
    "data": [
        {
            "index": 0,
            "name": "bow",
            "position": {
                "x": -100.0,
                "y": 0.0,
                "z": -5.0
            },
            "rotation": {
                "pitch": 45.0,
                "roll": 0.0,
                "yaw": 0.0
            },
            "sideview_scale": 0.1,
            "ip": "************",
            "intrinsic": {
                "fx": 385.215,
                "fy": 385.351,
                "cx": 643.299,
                "cy": 480.242
            },
            "distortion": {
                "k1": -0.00260598516653871,
                "k2": -0.00130293961556752,
                "k3": -0.000970285298225214,
                "k4": -0.000303661458364969
            }
        }
    ]
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (201000)
+
message : 응답 메시지 (Create camera Successful)
+
data : 저장된 카메라 데이터. 양이 많아 초기 1개의 값만 표기함.
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| index | int | 카메라 인덱스 (0부터 시작)
| name | string | 카메라 이름 (예: bow, stern 등)
| position(x, y, z) | float | 카메라 상대 위치 좌표 (x, y, z) - 선체 기준 (x: 좌우, y: 전후, z: 상하)
| rotation(pitch, roll, yaw) | float | 카메라 회전 각도 (pitch: 상하, roll: 좌우, yaw: 좌우 회전)
| sideview_scale | float | 개별 카메라뷰의 스케일
| ip | string | 카메라 IP 주소
| intrinsic(fx, fy, cx, cy) | float | 카메라 내부 파라미터 (fx: x 방향 초점거리, fy: y 방향 초점거리, cx: 주점 x 좌표, cy: 주점 y 좌표)
| distortion(k1, k2, k3, k4) | float | 카메라 왜곡 계수 (k1, k2, k3, k4)
|===

+
3.6 카메라 개별캘리브레이션 정보 조회
- 기능 설명 : 각 카메라의 개별 캘리브레이션 정보를 조회하여 camera 정보에 업데이트 한다.
- HTTP Method : GET
- Endpoint :/svm-api/v1/arges-camera/calibration
- Request : camera_ip (카메라 IP 주소), ex) /svm-api/v1/arges-camera/calibration?camera_ip=************
- Response :
+
[source,json]
-----
{
    "intrinsic": {
        "fx": 385.215,
        "fy": 385.351,
        "cx": 643.299,
        "cy": 480.242
    },
    "distortion": {
        "k1": -0.00260598516653871,
        "k2": -0.00130293961556752,
        "k3": -0.000970285298225214,
        "k4": -0.000303661458364969
    },
    "homography": {
        "h00": -0.9999862922474267,
        "h01": -0.08594936875152151,
        "h02": -67.66656957734234,
        "h10": 0.005247029381793636,
        "h11": 0.9513058804261553,
        "h12": 96.46860659758295,
        "h20": 0.0,
        "h21": -5.6967395333e-05,
        "h22": 1.044950034232727
    },
    "fov": 180.0
}
-----
- 응답 필드 설명:
+
intrinsic : 카메라 내부 파라미터
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| fx | float | x 방향 초점거리
| fy | float | y 방향 초점거리
| cx | float | 주점 x 좌표
| cy | float | 주점 y 좌표
|===
distortion : 카메라 왜곡 계수
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| k1 | float | 왜곡 계수 k1
| k2 | float | 왜곡 계수 k2
| k3 | float | 왜곡 계수 k3
| k4 | float | 왜곡 계수 k4
|===
homography : 카메라 Homography 행렬, inference_input에 저장되는 월드 좌표계에서 사이드뷰로 변환하는 Homography 행렬
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| h00 | float | Homography 행렬 요소 h00
| h01 | float | Homography 행렬 요소 h01
| h02 | float | Homography 행렬 요소 h02
| h10 | float | Homography 행렬 요소 h10
| h11 | float | Homography 행렬 요소 h11
| h12 | float | Homography 행렬 요소 h12
| h20 | float | Homography 행렬 요소 h20
| h21 | float | Homography 행렬 요소 h21
| h22 | float | Homography 행렬 요소 h22
|===
+
fov : 카메라 시야각 (Field of View)
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| fov | float | 카메라 수평 시야각 (Field of View)
|===

+

+
4.5 Side View 세팅 목록 설정
- 기능 설명 : 카메라 뷰의 세팅 목록에서 화면 전환시 처음 선택할 카메라를 설정한다.
- HTTP Method : PUT
- Endpoint : /svm-api/v1/preferencs/setup-sideview
- Request :
+
[source,json]
-----
{
  "selected_sideview": [
    0
  ]
}
-----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| selected_sideview | List[int] | 선택된 카메라 인덱스 정보 | required, 1개만 선택가능
|===
- Response :
+
[source,json]
-----
{
    "code": 200000,
    "message": "update preference successful.",
    "data": {
        "selected_sideview": [
            0
        ]
    }
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (update preference successful)
+
data : 선택된 카메라 인덱스 정보


===== 실행 조건
1. 네트워크 연결
- SVM 서버는 선박 내 로컬 네트워크에 연결되어 있어야 하며, 카메라(CCTV 포함), GPS, Radar, VDR 등 각 센서 장비들과의 통신이 가능해야 한다.
- 각 카메라는 고정 IP를 가지고 있어야 하며, 설정된 포트를 통해 접근 가능해야 한다 (기본 RTSP 포트: 554).

2. 장비 구성 및 사전 설치
- 설치 대상 선박에 최소 1개 이상의 카메라가 물리적으로 장착되어 있어야 한다.
- 카메라 장비의 intrinsic 및 distortion 파라미터가 보정(calibration)된 상태여야 한다.
- 시스템 설치 전, 선박의 제원(IMO 번호, 선명, 길이, 너비, 흘수 등)이 `/svm-api/v1/ship-specs` 엔드포인트를 통해 등록될 수 있도록 도면을 보고 해당정보를 알고 있어야한다.

3. SVM 설정 초기화
* Surround View, Side View 설정은 기본 설정을 통해 초기화된 상태여야 한다.

4. 권한 및 인증
- 모든 API는 내부망에서 접근이 가능한 구조로 설계되어 있으며, 별도의 인증 토큰 또는 접근 제어 로직은 내부 정책에 따라 적용되어야 한다.

5. 버전 및 호환성
- 서버는 Python 기반으로 운영되며, 최소 Python 3.8 이상 및 FastAPI 프레임워크 기반의 환경이 요구됩니다.

6. 운영 환경
- 시스템은 Ubuntu 22.04 이상, Kubernetes 컨테이너 기반 환경에서 동작하도록 설계되어 있다.

===== 검증 기준


1. 자선 정보 등록 및 조회
- /svm-api/v1/ship-specs API를 통해 IMO 번호, 선명, 길이, 너비, 흘수 등의 항목을 정상 등록할 수 있어야 한다.
등록된 자선 정보는 GET 요청을 통해 정확히 조회되어야 하며, 입력값과 응답값이 일치해야 한다.

2. CCRP 기준점 설정
- CCRP 등록 API(/svm-api/v1/ccrp)에 유효한 좌표값을 입력했을 때, 정상 응답(code: 201000)이 반환되어야 한다.
- 등록된 기준점은 조회 API(/svm-api/v1/ccrp)를 통해 동일하게 확인 가능해야 한다.
- 잘못된 범위의 좌표를 입력할 경우, 서버는 적절한 에러 메시지와 코드(404000)를 반환해야 한다.

3. 카메라 개수 및 모델 조회
- /svm-api/v1/svm-config/camera-count, /camera-model 호출 시 실제 구성된 장비 기준 정보가 일치해야 한다.


4. 카메라 세팅 및 저장
- 각 카메라의 설정값(intrinsic, distortion 등)을 /svm-api/v1/svm-camera API로 저장 후, 다시 조회 시 동일해야 한다.
- 입력 필드 중 하나라도 누락된 경우, 서버는 명확한 validation 오류 메시지를 반환해야 한다.

5. 네트워크 상태 조회
- /network-status/camera_all API 호출 시, 각 카메라 IP에 대한 연결 상태가 정확히 반환되어야 한다.
- 카메라 전원을 끈 상태에서 status가 `false`로 나와야 한다.
- 각 카메라의 타임아웃 시간은 1초이며, 최대 응답시간은 1초 * 카메라 댓수이다.

6. 개별 캘리브레이션 정보 조회
- /arges-camera/calibration API는 요청한 카메라 IP 기준으로 intrinsic, distortion, homography, fov 항목을 포함하여 응답해야 한다.
- 응답 필드가 일부라도 누락되면 해당 요청은 실패로 간주한다.

7. Surround View 설정 저장 및 조회**
- /calibration/surround-view API로 설정값 저장 후 동일한 값을 GET 요청으로 조회할 수 있어야 한다.
- 설정값 범위가 유효하지 않을 경우, 서버는 예외를 반환해야 한다.

8. CCTV 설정 등록/조회
- /cctv API를 통해 등록된 CCTV 정보가 조회 시 일치해야 한다. 등록된 CCTV 정보가 없을 경우, 빈 배열이 반환되어야 한다.
- 최소 1개의 CCTV가 등록된 상태에서 /cctv 조회 결과가 배열 형태로 반환되어야 하며, 각 필드 값이 명확하게 들어 있어야 한다.

9. 전체 API 공통 조건
- 모든 API 응답은 다음을 포함해야 한다:
  - 응답 코드(code)가 200000 또는 201000일 경우 성공
  - 메시지(message)는 상황에 맞는 명확한 성공/실패 사유를 포함
  - 응답 본문에는 data 필드가 반드시 존재해야 함
* 응답 시간은 평균 500ms 이내여야 하며, 1초를 초과하지 않아야 한다.(네트워크 상태 조회 제외)


===== 성능 요구사항

1. API 응답 시간
- 모든 API 요청에 대해 평균 응답 시간은 500ms 이하를 유지해야 한다.
- 최대 응답 시간은 1초를 초과해서는 안 됩니다.
- 네트워크 지연을 제외한 서버 처리 시간은 300ms 이하를 유지해야 한다.

2. 데이터 정합성
- 설정 API를 통해 등록된 모든 값은 조회 API를 통해 동일하게 반환되어야 하며, 데이터 손실이나 변형이 없어야 한다.
- 데이터 저장 후 1초 이내에 해당 값이 GET 요청을 통해 반영되어야 한다.

3. 저장 성능
- 카메라 설정, CCRP 설정 등 POST API 요청 시, 파일 또는 DB 저장 시간이 200ms를 초과하지 않아야 한다.
- 설정 저장 시 데이터 손상 방지를 위해 트랜잭션 또는 원자성 보장이 필요하다.

4. 리소스 사용량 제한
- SVM 설정 기능 동작 시 CPU 점유율은 평균 2000Mi 이내, 메모리 사용량은 3000Mi 이하를 권장한다.
- 고해상도 카메라가 다수 연결된 경우에도 리소스 사용량이 시스템 전체 안정성을 저해하지 않아야 한다.

5. 시스템 초기화 성능
- 시스템 최초 부팅 시 설정 파일 로딩 및 초기화는 300초 이내에 완료되어야 하며, 이후 설정 페이지 접근이 가능해야 한다.

===== 인터페이스 요구사항


1. API 형식 및 통신 프로토콜
- 모든 API는 RESTful 방식으로 설계되어야 하며, HTTP/2 프로토콜을 지원해야 한다.
- 데이터 전송은 JSON 포맷을 기본으로 하며, 요청(Request) 및 응답(Response)의 Content-Type은 `application/json`으로 명시해야 한다.
- 요청 본문은 UTF-8 인코딩을 사용해야 한다.

2. HTTP Method 규칙
- 데이터 조회는 GET, 신규 등록은 POST, 설정 수정은 POST 또는 PUT을 사용해야 한다.
- 각 Method는 의도한 목적 외로 사용하지 않아야 하며, RESTful 원칙을 준수해야 한다.

3. 엔드포인트 URL 규칙
- 모든 API는 /svm-api/v1/ 접두 경로를 사용하며, 기능에 따라 명확하게 구분된 경로를 사용해야 한다.
예:
+
[source]
----
/svm-api/v1/ship-specs
/svm-api/v1/svm-camera
/svm-api/v1/calibration/surround-view
/svm-api/v1/network-status/camera_all
----

4. 응답 포맷 및 응답 코드
- 응답은 공통 구조를 따라야 하며, 다음 필드를 포함해야 한다.
+
[source,json]
----
{
  "code": 200000,
  "message": "Success",
  "data": { ... }
}
----
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| code | int | 응답 코드 (200000: 조회 성공, 201000: 등록/수정 성공, 400000: 유효성 오류, 500000: 시스템 오류 등)
| message | string | 요청 처리 결과 메시지
| data | object or array or null | 요청 결과 데이터
|===

5. 인증 및 보안
- API는 내부망에서 접근하도록 설계되어 있으며, 다음 방식으로 인증이 적용될 수 있다:
  - OAuth2 Bearer Token 방식
- HTTPS를 통한 암호화 통신을 지원하며, TLS 1.2 이상 사용을 권장한다.

6. 에러 처리 규칙
- 요청 실패 시 다음 형식의 에러 응답을 제공해야 한다.
+
[source,json]
----
{
  "code": 400000,
  "message": "Invalid parameter: 'beam' must be > 0 and < 100.",
  "data": null
}
----
- 잘못된 요청, 누락된 필드, 유효하지 않은 포맷 등은 code: 404000으로 응답하며, 오류 메시지는 사용자 또는 개발자가 쉽게 원인을 파악할 수 있도록 구체적이어야 한다.


7. 호환성
- 본 API는 SVM 시스템 v1.0과 호환되며, 향후 버전 업그레이드 시 하위 호환성을 유지해야 한다.

===== 기타 요구사항


1. 시스템 초기화 및 설정 유지
- SVM 시스템은 설정값을 비휘발성 저장소에 유지하여 시스템 재시작 후에도 기존 설정이 유지되어야 한다.

2. 설정값 유효성 검증
- 모든 설정값은 API 레벨에서 유효성 검사가 선행되어야 하며, UI 상에서도 기본값과 허용 범위를 명시해야 한다.
- 숫자 범위 제한, 필수 항목 누락, 중복 IP/이름 등은 저장 전에 차단되어야 한다.

3. 운용 환경 제약
- 설정 페이지는 내부망에서만 접근 가능해야 하며, 외부 인터넷망을 통한 접근은 허용되지 않는다.

