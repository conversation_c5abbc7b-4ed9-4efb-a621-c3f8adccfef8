==== Side View Calibration

===== 설명

본 기능은 개별 카메라의 Scale, Pitch, Roll, Yaw 값을 조정하여 Side View 영상의 배율과 시야 각도를 보정(Calibration)하는 기능입니다.
실시간 반영이 가능하며, 정밀한 시각 조정을 통해 사용자 요구에 맞는 Side View 화면을 구현할 수 있습니다.




*Scale*

- 영상의 배율을 조절하는 값으로, 실제 거리 대비 영상의 크기 차이를 보정합니다.
- 카메라 간 거리 오차나 렌즈 왜곡에 따른 영상 확대/축소


.Roll & Pitch & Yaw
image::FUNCTION_REQUIREMENTS/setting/side_view_calibration/side_view_calibration_roll_pitch_yaw.png[align="center",300]


*Pitch*

- 카메라의 세로축 회전 (전후 기울기) 각도입니다.

- 카메라가 위 또는 아래로 기울어진 경우, 해당 기울기를 보정합니다.

- Pitch의 범위는 0도에서 90도까지입니다.



*Roll*

- 카메라의 가로축 회전 (좌우 기울기) 각도입니다.

- 설치 시 수평이 맞지 않아 영상이 한쪽으로 기운 경우, 이를 보정합니다.

- Roll의 범위는 -45도에서 45도까지입니다.


*Yaw*

- 카메라의 수직축 회전 (좌우 회전) 각도입니다.

- 카메라가 정면이 아닌 방향으로 비틀어진 경우, 영상 시야 정렬을 보정합니다.

- Yaw의 범위는 -45도에서 45도까지입니다.



.Side View Calibration
image::FUNCTION_REQUIREMENTS/setting/side_view_calibration/side_view_calibration_setting.png[align="center",600]


화면에서, 값을 직접입력해 수정할 수 있으며, Progress Bar을 조절하여, 이동 시킬 수 있습니다. Progress Bar을 선택하고 키보드를 누르면 변화됩니다.



*Side (Camera) View 설정 API*

- 기능 설명 : 카메라 뷰의 설정 정보를 수정한다.
- HTTP Method : POST
- Endpoint : /svm-api/v1/calibration/side-view
- Request :
+
[source,json]
-----
{
    "index": 1,
    "name": "stbd-1",
    "rotation": {
        "pitch": 45,
        "roll": 0,
        "yaw": 9
    },
    "scale": 0.1,
    "ip": "************"
}
-----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| index | int | 카메라 인덱스 (0부터 시작) | required, 0 <= index < camera_count
| name | string | 카메라 이름 (예: bow, stern 등) | required, 고유값
| rotation(pitch, roll, yaw) | float | 카메라 회전 각도 (pitch: 상하, roll: 좌우, yaw: 좌우 회전) | required, pitch: -0 ≤ pitch ≤ 90, roll: -45 ≤ roll ≤ 45, yaw: -45 ≤ yaw ≤ 45
| scale | float | 개별 카메라뷰의 스케일 | required
| ip | string | 카메라 IP 주소 | required, 유효한 IP 형식 (예:************)
|===


===== 실행 조건

1. 네트워크 연결
- SVM 서버는 선박 내 로컬 네트워크에 연결되어 있어야 하며, 카메라(CCTV 포함), GPS, Radar, VDR 등 각 센서 장비들과의 통신이 가능해야 한다.
- 각 카메라는 고정 IP를 가지고 있어야 하며, 설정된 포트를 통해 접근 가능해야 한다 (기본 RTSP 포트: 554).

2. 버전 및 호환성
- 서버는 Python 기반으로 운영되며, 최소 Python 3.8 이상 및 FastAPI 프레임워크 기반의 환경이 요구됩니다.

3. 운영 환경
- 시스템은 Ubuntu 22.04.05 이상, Kubernetes 컨테이너 기반 환경에서 동작하도록 설계되어 있다.


===== 검증 기준

*값 Validation*
|===
| 항목             | 허용 범위                    | 소수점 제한

| Scale            | 0.01 ≤ scale ≤ 1             | 소수점 3자리까지
| Pitch            | 0 ≤ pitch ≤ 90               | 소수점 1자리까지
| Roll             | -45 ≤ roll ≤ 45              | 소수점 1자리까지
| Yaw              | -45 ≤ yaw ≤ 45               | 소수점 1자리까지
|===

*기능 검증 항목*
- 사용자 입력에 따라 각 카메라에 대한 Scale, Pitch, Roll, Yaw 값이 수정됩니다
- 잘못된 값 입력 시 경고 메시지가 표시되고, 값은 반영되지 않아야 함
- 수정 즉시 Side View 영상에 반영되어 실시간으로 효과를 확인할 수 있어야 합니다.
- 설정 값은 개별 카메라 단위로 저장되어야 하며, 타 카메라 설정에 영향을 주지 않아야 합니다.


*오차 허용 범위*

[IMPORTANT]
====
Side View 보정값은 설치 환경, 카메라 위치, 화각 등의 영향을 받기 때문에, 일정 수준의 오차를 허용한 상태에서도 시스템의 다른 기능들이 정상적으로 동작해야 합니다.

특히 커미셔닝(Commissioning) 과정이나 현장 보정 시에는 담당자(Calibrator)의 주관적 판단이 일부 반영될 수 있으며, 시각적 정렬 기준은 장비 설치 조건에 따라 상이할 수 있습니다.

따라서 보정값의 정합성은 수치적 기준뿐 아니라 영상 일관성, 구조물 연결 상태, 시야 왜곡 등 **시각적 품질 기준을 함께 고려하여 검증**해야 하며, 해당 항목은 **중요 검증 항목(Important Validation Item)**으로 **반드시 테스트 범위에 포함**되어야 합니다.
====


시스템의 안정성과 사용성 확보를 위해 아래와 같은 오차 범위를 기준으로 설정합니다.

[cols="1,3", options="header"]
|===
| 항목 | 허용 오차

| 카메라 시야 정렬 오차 (Yaw 기준) | ±3 도 이하
| 수평 기울기 오차 (Roll 기준) | ±3도 이하
| 수직 기울기 오차 (Pitch 기준) | ±3도 이하
|===


해당 오차범위내에서 Surrond View 및 Side View 영상이 정상적으로 현시되어야 합니다.


===== 보정 오차 발생 시 기능 정상 동작 검증 항목 (Functional Integrity Under Calibration Deviation)

|===
| 검증 항목 | 설명 | 검증 방법 | 기준

| Surround View 생성
| Side View 영상이 틀어져 있어도 Surround View 영상 생성 및 표시가 정상인지 확인
| Surround View UI 확인 및 왜곡·깨짐 여부 점검

- Surround View에 보이는 타선이 이동 시, Surround View의 카메라 겹침(Overlapping) 구간에서도 영상 정합성 유지 여부 확인 +

- 타선의 외곽선(윤곽선)이 왜곡되거나 연결되지 않는 경우 없는지 확인

| 영상 생성 및 시각 정렬 문제 없음

- 겹쳐지는 영역에서도 영상 왜곡 또는 불연속 현상 없어야 함

| Target 정보 표시 (AIS/ARPA)
| 틀어진 영상에서도 AIS/ARPA 대상의 위치 및 정보가 정확하게 표시되는지
| 대상 아이콘 위치, CPA/TCPA 정보 비교
| 위치 정확도 ±0.5m, 데이터 일치

| UI 조작 응답성
| 영상이 틀어진 상태에서 UI 조작(슬라이더, 확대/축소 등)이 정상 반응하는지
| 슬라이더, 클릭 이벤트 반응 확인
| 응답 지연 없이 반영 (500ms 이내)

| 영상 기록 기능
| 영상 왜곡 상태에서도 기록된 영상이 재생되고 시간 일치하는지
| 영상 재생 시간 및 프레임 연속성 확인
| 시간 일치, 재생 오류 없음

| 위험 구역 경고 표시
| Side View에 설정된 위험 구역(예: 붉은 영역)이 영상 오차에도 정확히 표시되는지
| 경고 오버레이 위치 확인
| 오차 ±0.5m 이내 유지

| 시스템 안정성
| 보정값이 극단값(Pitch 90도 등)일 경우에도 시스템이 Crash 없이 동작하는지
| 설정 극단값 입력 후 시스템 반응 확인
| 오류 없이 정상 동작 유지

| 데이터 저장 및 복원
| 오차 있는 설정 상태 저장 후 재부팅해도 값이 복원되는지
| 설정 저장 → 재부팅 → 값 비교
| 값 일치 및 영상 반영 확인
|===



===== 성능 요구사항

1. API 응답 시간
- 모든 API 요청에 대해 평균 응답 시간은 500ms 이하를 유지해야 한다.
- 최대 응답 시간은 1초를 초과해서는 안 됩니다.
- 네트워크 지연을 제외한 서버 처리 시간은 300ms 이하를 유지해야 한다.
- 연속된 값 변경(예: 슬라이더 입력 등)에 대해서도 500ms 이내에 반영되어야 한다.

2. 데이터 정합성
- 설정 API를 통해 등록된 모든 값은 조회 API를 통해 동일하게 반환되어야 하며, 데이터 손실이나 변형이 없어야 한다.
- 데이터 저장 후 1초 이내에 해당 값이 GET 요청을 통해 반영되어야 한다.

3. 저장 성능
- 카메라 설정, CCRP 설정 등 POST API 요청 시, 파일 또는 DB 저장 시간이 200ms를 초과하지 않아야 한다.
- 설정 저장 시 데이터 손상 방지를 위해 트랜잭션 또는 원자성 보장이 필요하다.

4. 리소스 사용량 제한
- SVM 설정 기능 동작 시 CPU 점유율은 평균 2000Mi 이내, 메모리 사용량은 3000Mi 이하를 권장한다.
- 고해상도 카메라가 다수 연결된 경우에도 리소스 사용량이 시스템 전체 안정성을 저해하지 않아야 한다.


5. 시스템 초기화 성능
- 시스템 최초 부팅 시 설정 파일 로딩 및 초기화는 300초 이내에 완료되어야 하며, 이후 설정 페이지 접근이 가능해야 한다.

6. 실시간 반영 성능
- 값 변경 후 **1초 이내**에 Side View 영상에 반영되어야 함


===== 인터페이스 요구사항

- 입력 방식은 직접 입력 또는 슬라이더로 제공
- 각 항목에 유효 범위 표기 및 실시간 오류 표시 기능 포함
- 실시간 영상 미리보기와 함께 각 값의 변경이 시각적으로 확인 가능해야 함
- 각 카메라의 설정은 드롭다운 또는 탭 등으로 선택 가능해야 함

===== 기타 요구사항

- 기본 Scale 값은 0.1로 초기화되어야 함
- 설정값은 사용자 설정으로 저장되어 재접속 시 복원 가능해야 함
- 설정값은 별도 초기화(Reset) 기능을 통해 원복할 수 있어야 함
