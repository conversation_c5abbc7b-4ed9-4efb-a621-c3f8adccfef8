==== Surround View Calibration

===== 설명

Surround View 설정은 선박 주위의 전방위 시야를 구성하기 위한 보정(Calibration) 기능입니다.
Visible Distance 설정, 드래프트 값 입력, 카메라 영역 조정, 마스킹 설정 등을 통해 사용자는 Surround View의 품질을 최적화할 수 있습니다.
Recommended 설정 또는 사용자 지정 입력을 통해 조정 가능하며, 입력값에 따라 실시간 반영됩니다.


 Surround View 설정 조회
- 기능 설명 : Surround View의 설정 정보를 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/calibration/surround-view
- Request : 없음
- Response :
+
[source,json]
-----
{
    "code": 200000,
    "message": "Success",
    "data": {
        "bow_slope": 10,
        "stern_slope": 60,
        "mask_gradient_width": 60,
        "visible_distance": 119.6945,
        "recommend_visible_distance": 119.6945,
        "scale": 0.5091649659863946,
        "visible_distance_warning": null
    }
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Success)
+
data : Surround View 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| bow_slope | float | Bow 카메라의 경사각 (도 단위)
| stern_slope | float | Stern 카메라의 경사각 (도 단위)
| mask_gradient_width | float | 마스크 그라디언트 너비 (픽셀 단위)
| visible_distance | float | 현재 설정된 가시 거리 (m)
| recommend_visible_distance | float | 추천 가시 거리 (m)
| scale | float | Surround View의 스케일
| visible_distance_warning | string or null | 가시 거리 경고 메시지 (null인 경우 경고 없음)
|===
+
4.3 Surround View 설정 저장/수정
- 기능 설명 : Surround View의 설정 정보를 저장/수정한다.
- HTTP Method : POST
- Endpoint : /svm-api/v1/calibration/surround-view
- Request :
+
[source,json]
-----
{
    "bow_slope": 10,
    "stern_slope": 60,
    "mask_gradient_width": 60,
    "visible_distance": 119.6945,
    "recommend_visible_distance": 119.6945,
    "scale": 0.5091649659863946
}
-----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| bow_slope | float | Bow 카메라의 경사각 (도 단위) | required, 0 <= bow_slope <= 90
| stern_slope | float | Stern 카메라의 경사각 (도 단위) | required, 0 <= stern_slope <= 90
| mask_gradient_width | float | 마스크 그라디언트 너비 (픽셀 단위) | required, 0 <= mask_gradient_width <= 100
| visible_distance | float | 현재 설정된 가시 거리 (m) | required
| recommend_visible_distance | float | 추천 가시 거리 (m) | required
| scale | float | Surround View의 스케일 | required, default 0.1
|===
- Response :
+
[source,json]
-----
{
    "code": 200000,
    "message": "Success",
    "data": {
        "bow_slope": 10,
        "stern_slope": 60,
        "mask_gradient_width": 60,
        "visible_distance": 119.6945,
        "recommend_visible_distance": 119.6945,
        "scale": 0.5091649659863946,
        "visible_distance_warning": null
    }
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Success)
+
data : Surround View 설정 정보


===== 실행 조건

- Stage 1에서 카메라 위치가 변경된 경우, Recommended Visible Distance 및 Scale 값이 자동으로 설정됩니다.
- 사용자는 Recommended 또는 직접 입력 방식으로 Visible Distance 값을 설정할 수 있습니다.
- Measured Draught, Bow/Stern Slope, Mask Gradient Width는 별도로 설정 가능하며, 각 항목은 유효 범위를 만족해야 반영됩니다.

===== 검증 기준

.입력값 유효성 검증

|===
| 항목                      | 허용 범위                                      | 소수점 제한

| Visible Distance (Etc)    | 12 ≤ 값 ≤ 1000                                | 소수점 3~4자리까지 허용
| Measured Draught          | 0 < 값 ≤ Scantling Draught                    | 소수점 1자리
| Bow Camera Slope          | 0 ≤ 값 ≤ 100000                               | 정수
| Stern Camera Slope        | 0 ≤ 값 ≤ 100000                               | 정수
| Mask Gradient Width       | 0 ≤ 값 ≤ 100000                               | 정수
|===

.기능 검증 항목

- Recommended 설정 시 Stage 1 기준 최적 Visible Distance와 Scale이 자동 적용되는지 확인
- Measured Draught가 Scantling Draught를 초과하지 않는지 검증
- 픽셀 기반 설정값(Bow/Stern slope, Mask width)이 Surround View 영상에 반영되는지 확인

===== 성능 요구사항

- Visible Distance, Slope, Gradient 등 입력값 변경 시 1초 이내에 화면에 반영되어야 함
- Recommended 설정 클릭 시 내부 계산을 포함하여 2초 이내에 적용 완료되어야 함

===== 인터페이스 요구사항

- Visible Distance는 Recommended/Etc(사용자 입력) 선택 방식으로 구성
- Measured Draught, Slope, Gradient 값은 입력 필드 또는 슬라이더 형태로 제공
- Recommended 적용 시 사용자에게 적용 완료 상태를 시각적으로 표시해야 함
- 실시간 미리보기 또는 적용 확인이 가능해야 함

===== 기타 요구사항

- Visible Distance 및 Scale 값은 Surround View 구성의 핵심 파라미터로, 내부 계산 결과가 저장되어야 함
- 모든 설정 값은 사용자 설정으로 저장되어, 시스템 재시작 시에도 복원 가능해야 함
- 마스킹 처리 영역(Gradient Width)은 선형 보간 또는 페이드 처리를 위해 사용되며, 영상의 이질감 최소화를 목적으로 함
