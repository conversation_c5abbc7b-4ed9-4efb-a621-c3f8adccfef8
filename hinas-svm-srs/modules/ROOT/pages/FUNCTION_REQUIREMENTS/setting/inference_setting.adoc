==== Inference Setting

===== 설명

*Inference 설정 저장 API*

- 기능 설명 : Inference 설정 정보를 저장한다.
- HTTP Method : POST
- Endpoint : /svm-api/v1/calibration/inference
- Request : 없음.(내부변수들의 목록으로 구성하여 저장)
- Response :
+
[source,json]
-----
{
    "code": 201000,
    "message": "put inference Successful.",
    "data": null
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (201000)
+
message : 응답 메시지 (put inference Successful)

===== 검증 기준

* Inference 설정 저장 요청 후 Redis 또는 내부 스토리지에 정상 반영되었는지 확인
* 잘못된 파라미터(예: 타입 불일치, 누락 등) 입력 시 400 Bad Request 반환
* 설정값 저장 후 동일 파라미터로 조회 시 저장된 값이 정확히 반환되어야 함
* 중복 요청(동일한 설정 재요청) 시에도 무조건 성공 응답 및 설정 덮어쓰기 처리


* UI 검증 기준:
  - 설정 저장 전에는 UI 상에서 `"Inference 설정 미저장"` 상태로 표시되어야 함
  - 설정 저장 직후에는 `"Inference 설정 저장됨"` 상태로 즉시 반영되어야 함 (500ms 이내)
  - 다른 설정 항목이 변경되면 `"Inference 설정 무효화됨"` 또는 `"재설정 필요"`로 UI 상태가 변경되어야 함
  - 설정 상태는 페이지 이동, 새로고침 후에도 정확히 유지되어야 함 (localStorage 또는 backend fetch 기준)


===== 성능 요구사항

* 설정 저장 요청 처리 시간은 500ms 이내여야 함
* 1초 이내에 다수의 설정 요청이 연속 발생해도 시스템은 정상적으로 처리해야 함.

===== 인터페이스 요구사항

* 요청은 반드시 `application/json` MIME 타입으로 전송되어야 함
* 설정 파라미터는 미리 정의된 명세에 따라야 하며, 정의되지 않은 파라미터 포함 시 예외 반환
* 응답은 공통 응답 스펙(code, message, data)을 따라야 함

===== 기타 요구사항

- 해당사항 없음.