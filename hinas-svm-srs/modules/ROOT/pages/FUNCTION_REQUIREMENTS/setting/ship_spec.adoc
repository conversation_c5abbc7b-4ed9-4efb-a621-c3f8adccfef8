==== Ship Specification 및 CCRP 설정 단계

===== 설명

이 페이지는 SVM 시스템의 설정을 관리하는 기능을 제공합니다.
이 페이지는 선박 정보 등록, 시스템 기본 설정, 카메라 및 CCTV 설정 등을 포함하며, 커미셔너가 SVM 시스템을 초기 설정하는 데 사용됩니다.


====== Ship Spec

.Ship Spec
image::FUNCTION_REQUIREMENTS/setting/ship_spec/ship_spec.png[align="center",600]

이 기능은 선박의 기본 제원(Ship Specification) 및 CCRP(Central Control Reference Point) 관련 센서/기준점의 위치 좌표를 등록하거나 수정할 수 있는 2단계 구성의 입력 인터페이스입니다.

전체 입력 프로세스는 다음 두 단계로 구성됩니다:

- *Stage 1*: 선박 제원 입력

- *Stage 2*: 센서 및 CCRP 위치 설정


Stage 1에서는 선박의 IMO 번호, 선박명, 길이(Length), 너비(Beam), 설계 흘수(Scantling Draught) 등의 기본 제원을 입력합니다.

Stage 2에서는 CCRP와 관련된 센서들의 위치 좌표를 입력합니다. 이 단계에서는 GPS, 레이더, 카메라 등의 센서가 선박의 어느 위치에 설치되어 있는지를 정의하며, CCRP를 기준으로 각 센서의 상대적인 위치를 설정합니다. 이를 통해 SVM은 Sensor Fusion에서의 위치 정확도를 향상시킬 수 있습니다.

Stage 1에서의 선박 제원 입력은 Stage 2에서의 센서 위치 설정에 영향을 미치며, 선박의 길이와 너비는 CCRP 및 센서 좌표의 유효 범위를 결정하는 데 사용됩니다.

따라, Stage 1에서 값이 입력되지 않은 경우, Stage 2로 넘어갈 수 없습니다.


*선박 제원*


- IMO Number: 선박의 국제해사기구(IMO) 등록번호로, 7자리 숫자로 구성됩니다.


- Ship Name: 선박의 이름으로, 최대 50자까지 입력할 수 있습니다.


- Length: 선박의 길이로, 0에서 600미터 사이의 값을 입력할 수 있으며, 소수점 이하 1자리까지 허용됩니다.


- Beam: 선박의 너비로, 0에서 100미터 사이의 값을 입력할 수 있으며, 소수점 이하 1자리까지 허용됩니다.


- Scantling Draught: 선박의 설계 흘수로, Measured Draught보다 크거나 같아야 하며, 0에서 100미터 사이의 값을 입력할 수 있습니다. 소수점 이하 1자리까지 허용됩니다. Scantling Draught는 선박 설계 시 기준으로 설정된 최대 흘수입니다.


*Ship Spec API*

1. Ship Spec 조회
- 기능 설명 : 등록된 선박 기본 제원(IMO 번호, 선명, 길이 등)을 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/ship-specs
- Request : 없음
- Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "Get ship specs successful.",
  "data": {
    "imo_number": "1234567",
    "ship_name": "HANNARA SHIP",
    "length": 133,
    "beam": 67,
    "scantling_draught": 6.4
  }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get ship specs successful)
+
data : 선박 제원 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명

| imo_number | string | IMO 등록번호
| ship_name | string | 선박명
| length | float | 선박 길이 (m)
| beam | float | 선박 너비 (m)
| scantling_draught | float | 설계 흘수 (m)
|===


2. Ship Spec 등록 및 수정

- 기능 설명 : 선박의 제원을 최초 등록하거나, 기존 정보를 수정한다.
- HTTP Method : POST
- Endpoint : /svm-api/v1/ship-specs
- Request :
+
[source,json]
-----
{
  "imo_number": "string",
  "ship_name": "string",
  "length": 1,
  "beam": 1,
  "scantling_draught": 1
}
-----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항

| imo_number | string | IMO 등록번호 | required, 7-digit IMO number
| ship_name | string | 선박명 | required, 최대 50자
| length | float | 선박 길이 (m) | required, 0 < length < 600
| beam | float | 선박 너비 (m) | required, 0 < beam < 100
| scantling_draught | float | 설계 흘수 (m) | required, 0 < scantling_draught < 100
|===

- Response :
+
[source,json]
-----
{
  "code": 201000,
  "message": "Add ship specs successful.",
  "data": {
    "imo_number": "1234567",
    "ship_name": "4242424",
    "length": 200,
    "beam": 60,
    "scantling_draught": 1
  }
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (201000)
+
message : 응답 메시지 (Add ship specs successful)
+
data : 선박 제원 정보



====== CCRP

.CCRP
image::FUNCTION_REQUIREMENTS/setting/ship_spec/ship_spec_ccrp.png[align="center",600]

**CCRP(Central Control Reference Point)**

CCRP는 선박 상에서 모든 위치 기반 계산과 시각화의 기준이 되는 참조 지점(Reference Point)입니다.

선박에는 위치 정보를 제공하는 Radar, AIS 등의 다양한 장비들이 설치되어 있으며, 이들은 모두 선박 내 서로 다른 위치에 존재합니다.
이러한 장비들의 위치는 CCRP를 기준으로 정의되며, CCRP는 선박의 중심 위치를 기준으로 하여 각 센서의 상대적인 위치를 계산하는 데 사용됩니다.

- CCRP는 선박의 중심 위치를 기준으로 하여, 선박의 길이와 폭을 고려한 2차원 좌표계로 정의됩니다.

**CCRP의 좌표계 **

- X축 (종축): 선박의 길이 방향, Stern(선미)에서 Bow(선수) 방향이 양의 방향
- Y축 (횡축): 선박의 폭 방향, Port(좌현)에서 Starboard(우현) 방향이 양의 방향
- 원점: 기본적으로 선박의 Stern(선미) 중앙에 위치
- 좌표 단위 :
일반적으로 미터(m) 단위를 사용하며, 선박의 크기와 각 센서의 정확한 설치 위치를 반영합니다.

CCRP 이동은 수학적으로 좌표계의 평행이동(Translation)과 동일한 개념입니다.

[source]
----
CCRP 좌표 변환 공식

새로운 좌표 = 기존 좌표 - CCRP 이동량

X_new = X_old - ΔX
Y_new = Y_old - ΔY

ΔX: CCRP의 X축 이동량 (양수: 선수 방향)
ΔY: CCRP의 Y축 이동량 (양수: 우현 방향)
----
200m 길이의 선박이 있고, 선미 중앙을 기준점 (0, 0)으로 삼았습니다. 선교는 선박 앞쪽 150m 지점에 있고, 그 위 레이더는 중심선에서 우현으로 2m 떨어진 곳에 설치되어 있습니다.

레이더가 타선을 발견했는데, 레이더 화면에는 그 타선이 레이더로부터 앞쪽 200m, 우현 10m 지점에 있다고 표시됩니다. 하지만 우리가 실제로 알고 싶은 것은 선박 전체를 기준으로 한 타선의 위치입니다.

레이더는 선미 기준점에서 (150, 2) 위치에 있으므로, 레이더가 탐지한 타선의 실제 위치는 선박 전체 기준으로 계산해야 합니다. 타선의 실제 위치를 계산하면 X좌표는 200 - 150 = 50, Y좌표는 10 - 2 = 8이 됩니다. 즉, 타선은 선박의 선미 중앙에서 앞쪽 50m, 우현 8m 지점에 있습니다.

이제 CCRP를 선박 중앙(선미에서 100m 앞쪽)으로 옮겨보겠습니다. 이는 마치 좌표계의 원점을 이동시키는 것과 같습니다. 새로운 기준점에서 레이더의 위치는 (150-100, 2-0) = (50, 2)가 되고, 타선의 위치는 (50-100, 8-0) = (-50, 8)이 됩니다.

결과를 보면 타선의 위치가 (-50, 8)이 되었습니다. 이는 새로운 기준점(선박 중앙)에서 볼 때 타선이 뒤쪽으로 50m, 우현으로 8m 떨어진 곳에 있다는 뜻입니다. 실제로는 타선이 움직이지 않았지만, 우리가 기준점을 선박 중앙으로 옮겼기 때문에 타선이 상대적으로 뒤쪽에 있는 것처럼 보이게 됩니다.



*CCRP 선택*


시스템에서는 하나의 선박에 대해 두 가지 CCRP 설정값을 동시에 등록해둘 수 있으며, 이 중 어떤 기준점을 사용할지는 selected_ccrp 값을 통해 선택할 수 있습니다.

예를 들어, 센서 설치 위치가 변경되었거나, 실제 운영과 시뮬레이션 등 서로 다른 기준점이 필요한 상황에서 각각의 CCRP 설정을 미리 정의해두고 필요에 따라 전환하여 사용할 수 있도록 설계되어 있습니다.



*CCRP API*

1. CCRP 정보 조회

- 기능 설명 : 현재 저장된 CCRP 설정 정보를 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/ccrp
- Request : 없음
- Response :
+
[source,json]
-----
{
  "code": 200000,
  "message": "Get ccrp setting successful.",
  "data": {
    "selected_ccrp": 0,
    "ccrp1_x": 0,
    "ccrp1_y": 0,
    "ccrp2_x": 0,
    "ccrp2_y": 0,
    "gps1_x": 0,
    "gps1_y": 0,
    "radar1_x": 0,
    "radar1_y": 0,
    "cam_x": 0,
    "cam_y": 0
  }
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get ccrp setting successful)
+
data : CCRP 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| selected_ccrp | int | 선택된 CCRP (1: setting1, 2: setting2)
| ccrp1_x | float | CCRP1 X 좌표 (m)
| ccrp1_y | float | CCRP1 Y 좌표 (m)
| ccrp2_x | float | CCRP2 X 좌표 (m)
| ccrp2_y | float | CCRP2 Y 좌표 (m)
| gps1_x | float | GPS1 X 좌표 (m)
| gps1_y | float | GPS1 Y 좌표 (m)
| radar1_x | float | 레이더1 X 좌표 (m)
| radar1_y | float | 레이더1 Y 좌표 (m)
| cam_x | float | 카메라 X 좌표 (m)
| cam_y | float | 카메라 Y 좌표 (m)
|===

2. CCRP 정보 등록/수정

- 기능 설명 : CCRP 설정 정보를 등록/수정하여 선박의 센서들의 기준점을 통합한다.
- HTTP Method : POST
- Endpoint : /svm-api/v1/ccrp
- Request :
+
[source,json]
-----
{
  "selected_ccrp": 1,
  "ccrp1_x": 0,
  "ccrp1_y": 0,
  "ccrp2_x": 0,
  "ccrp2_y": 0,
  "gps1_x": 0,
  "gps1_y": 0,
  "radar1_x": 0,
  "radar1_y": 0,
  "cam_x": 0,
  "cam_y": 0
}
-----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| selected_ccrp | int | 선택된 CCRP (1: setting1, 2: setting2) | required
| ccrp1_x | float | CCRP1 X 좌표 (m) | required, 0 < ccrp1_x < ship_length
| ccrp1_y | float | CCRP1 Y 좌표 (m) | required, 0 < ccrp1_y < ship_beam / 2
| ccrp2_x | float | CCRP2 X 좌표 (m) | required, 0 < ccrp2_x < ship_length
| ccrp2_y | float | CCRP2 Y 좌표 (m) | required, 0 < ccrp2_y < ship_beam / 2
| gps1_x | float | GPS1 X 좌표 (m) | required, 0 < gps1_x < ship_length
| gps1_y | float | GPS1 Y 좌표 (m) | required, 0 < gps1_y < ship_beam / 2
| radar1_x | float | 레이더1 X 좌표 (m) | required, 0 < radar1_x < ship_length
| radar1_y | float | 레이더1 Y 좌표 (m) | required, 0 < radar1_y < ship_beam / 2
| cam_x | float | 카메라 X 좌표 (m) | required, 0 < cam_x < ship_length
| cam_y | float | 카메라 Y 좌표 (m) | required, 0 < cam_y < ship_beam / 2
|===
- Response :
+
[source,json]
-----

-----
- 응답 필드 설명
+
code : 응답 코드 (201000)
+
message : 응답 메시지 (Add ccrp setting successful)
+
data : CCRP 설정 정보


===== 실행 조건

- 사용자는 입력 폼에서 모든 필수 항목을 기입한 후 Submit 버튼을 눌러야 데이터가 저장됩니다.
- 기존 데이터가 존재하는 경우 Submit 버튼 클릭 시 Camera 데이터 초기화 경고 모달이 노출됩니다.
- Submit을 누르지 않을 경우 기존 데이터가 유지되며 변경사항은 반영되지 않습니다.

===== 검증 기준

.Stage 1 – Ship Spec Validation
|===
| 항목 | 조건 | 허용 소수점 | 비고

| IMO Number
| 7자리 숫자
| 없음
| 숫자 형식만 허용

| Length (m)
| 0 < Length < 600
| 소수점 1자리
| 양의 실수

| Beam (m)
| 0 < Beam < 150
| 소수점 1자리
| 양의 실수

| Scantling Draught (m)
| Measured Draught ≤ Scantling Draught < 100
| 소수점 1자리
| 측정된 드래프트 이상

|===

.Stage 2 – CCRP 및 센서 위치 Validation

|===
| 항목 | X 좌표 조건 | Y 좌표 조건 | 허용 소수점

| GPS1
| 0 < X < Length
| -Beam/2 < Y < Beam/2
| 소수점 2자리

| Radar Antenna
| 0 < X < Length
| -Beam/2 < Y < Beam/2
| 소수점 2자리

| Camera
| 0 < X < Length
| -Beam/2 < Y < Beam/2
| 소수점 2자리

| CCRP1 또는 CCRP2
| 0 < X < Length
| -Beam/2 < Y < Beam/2
| 소수점 2자리

|===

- 유효성 검사 실패 시 사용자는 입력 필드별 오류 메시지를 확인할 수 있어야 합니다.
- 길이(Length) 또는 너비(Beam)가 수정된 경우, 모든 좌표값도 그에 맞게 자동 재검증되어야 합니다.

===== 성능 요구사항

- 입력값 유효성 검사는 100ms 이내에 처리되어야 하며, 실시간 오류 표시를 제공합니다.
- Submit 시 서버 응답까지 1초 이내로 완료되어야 하며, 기존 Camera 데이터 초기화 안내는 사용자 조작을 방해하지 않아야 합니다.
- 저장된 데이터는 재접속 시 불러와 반영되어야 하며, 중복 저장은 발생하지 않아야 합니다.

===== 인터페이스 요구사항

- 각 항목은 Form 형태로 구성되며, 필드는 다음과 같은 UI 요소로 구성됩니다:

  * IMO Number: 숫자 입력 필드 (7자리 제한)
  * Ship Name: 텍스트 필드
  * Length / Beam / Draught: 소수점 제한 입력 필드
  * 센서 좌표(X, Y): 동적 범위 계산이 적용된 입력 필드
  * CCRP1 / CCRP2 선택: 라디오 버튼 또는 드롭다운
  * Submit 버튼: 조건 만족 시 활성화

- 기존 데이터 존재 시 Submit 클릭 시 다음 모달이 노출되어야 함:
  `[Camera 설정이 초기화됩니다. 계속하시겠습니까?]`

===== 기타 요구사항

- Submit 시 유효성 오류가 있는 항목은 붉은 경고 표시와 함께 명확한 설명을 제공해야 합니다.
- Scantling Draught의 조건 중 Measured Draught 값은 이전 입력 혹은 외부에서 연동된 값을 기반으로 설정됩니다.
- 모든 수치는 국제 단위계(SI)를 기준으로 하며, 내부 저장 시 소수점 이하 자릿수를 명확히 구분하여 처리해야 합니다.
