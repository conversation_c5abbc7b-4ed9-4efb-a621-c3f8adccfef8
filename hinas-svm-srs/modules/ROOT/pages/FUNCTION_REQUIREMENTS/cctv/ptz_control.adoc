==== PTZ Camera Control

===== 설명
본 기능은 SVM 시스템이 Arges PTZ 카메라를 대상으로 스트리밍 및 제어를 수행할 수 있도록 구성된 기능이다.

1. Arges에서 제공된 PTZ 카메라 스트림을 MediaMTX 서버로 전달하여 현시한다.
- 기본 스트림은 일반 CCTV와 동일하게 처리되며, PTZ 카메라는 추가적인 제어 기능을 제공한다.
2. PTZ 카메라 제어를 수행하며, 지원되는 기능은 다음과 같다.
- 좌우 이동
- 상하 이동
- 줌 인/아웃
- 카메라 와이퍼 동작
3. SVM 시스템은 `/svm-api/v1/ptz/control` API를 호출하여 PTZ 카메라 제어를 수행하며, 정상 응답(`status_code 200`)을 수신해야 한다.
- 해당 API는 내부적으로 PTZ 카메라에서 제공하는 제어 API를 호출하여 동작을 수행한다.
+
[source,text]
----
GET : http://{PTZ 카메라 IP}/ptz/control.php?{command}={value}

command: move     // 이동
value: left, right, up, down, stop     // 좌, 우, 상, 하, 정지

command: zoom     // 줌
value: tele, wide, stop     // 줌인, 줌아웃, 정지

command: wipe     // 와이퍼 동작
value: 1     // 6회 동작
----

- 해당 API는 명령 실행 시간을 설정할 수 없으므로, 명령 수행 후 반드시 `stop` 명령을 호출하여 동작을 명시적으로 종료해야 한다.
- API 호출시 인증 정보는 Basic 인증 방식으로 전달되며, 카메라의 ID와 비밀번호를 Base64로 인코딩하여 Authorization 헤더에 포함시킨다.
+
[source,text]
----
{"Authorization": (f"Basic {base64.encode({cctv.id}:{cctv.pw}})")}
----

4. 위에서 명시한 기능 외에는 추가적인 제어 기능을 제공하지 않는다.
5. PTZ 카메라는 반드시 SVM 네트워크에 연결되어 있어야 하며, 외부 네트워크에서 접근할 수 없다.
6. Arges 카메라는 RTSP 스트림을 제공하고, SVM은 해당 스트림을 MediaMTX를 통해 WebRTC 형식으로 변환하여 사용자에게 제공한다.
7. PTZ 카메라 제어 기능은 Arges에서 제공된 카메라에 한정되며, 타사 장비는 지원하지 않는다.
8. 모든 PTZ 제어 요청은 카메라 내부 시스템 로그에 기록됩니다.

===== 실행 조건
1. PTZ 카메라는 SVM 네트워크에 연결되어 있어야 하며, IP 대역이 SVM 서버에 등록되어 있어야 한다.
2. PTZ 카메라의 로그인 ID 및 비밀번호를 SVM 시스템에 등록해야 한다.
3. PTZ 카메라의 펌웨어는 `v2.1.1.357-RC250228-2` 이상이어야 한다.

===== 검증 기준
1. PTZ 카메라 스트림이 SVM 화면에서 정상적으로 출력되어야 한다.
2. 각 제어 명령(좌우 이동, 상하 이동, 줌 인/아웃, 와이퍼 동작)은 정상 동작한 후 `stop` 명령에 따라 종료되어야 한다.
3. SVM API(`/svm-api/v1/ptz/control`) 호출 시 응답 코드 `200`을 수신해야 하며, 내부적으로 Arges PTZ 카메라 제어 API가 호출되어야 한다.

===== 성능 요구사항
1. PTZ 제어 API 호출 후 카메라의 응답 지연 시간은 1초 이내여야 한다.
2. PTZ 제어 명령은 호출된 순서대로 직렬 처리되어야 하며, 중첩 호출 시 무시되거나 대기 처리되어야 한다.
3. 카메라 측 응답 실패 시 적절한 재시도 또는 오류 코드가 반환되어야 한다.

===== 인터페이스 요구사항
1. PTZ 제어는 SVM Backend에서 제공하는 HTTP REST API `/svm-api/v1/ptz/control`을 통해 수행됩니다.
2. 제어 API는 HTTP GET 방식으로 구성되며, 다음 형식을 따른다:
+
[source,text]
----
GET http://{PTZ_IP}/ptz/control.php?{command}={value}
----

3. 제어 API 응답은 다음과 같은 JSON 형식으로 구성된다:
+
[source,json]
----
{
  "content": "res=200",
  "status_code": "200",
  "headers": "headers"
}
----

===== 기타 요구사항
- N/A
