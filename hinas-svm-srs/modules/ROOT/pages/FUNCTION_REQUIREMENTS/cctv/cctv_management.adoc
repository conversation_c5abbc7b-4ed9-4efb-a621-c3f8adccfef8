==== CCTV Management


===== 설명
본 기능은 *SVM Backend Module*에서 제공하는 API를 통해 사용자가 CCTV 설정을 조회 및 등록할 수 있도록 하며, 등록된 정보는 *Storage Module(Redis)*에 저장됩니다.

저장된 CCTV 정보는 RTSP 스트리밍 서버인 *Mediamtx*의 설정 파일(`mediamtx.yml`)에 반영됩니다. 각 CCTV에 대해 RTSP URL을 생성하고, 해당 URL은 Mediamtx 설정 파일의 `paths` 항목에 `"cctv/<카메라 이름>"` 형식으로 추가됩니다. RTSP URL의 형식은 다음과 같다:
----
rtsp://<id>:<pw>@<ip>:<port>/<server_url>
----

- cctv 정보 저장 redis key : svm:cctv
- mediamtx.yml 저장 경로 : /data/hdd/cctv-config/mediamtx.yml

.mediamtx.yml 예시
[source,yaml]
----

paths:
  cctv/1:
    source: rtsp://avikus:avi1357!!@************:554//media/1/1/Profile1
  cctv/2:
    source: rtsp://avikus:avi1357!!@************:554//media/1/1/Profile1
webrtc: true
webrtcAdditionalHosts:
- svm.ingress.local
- ************
webrtcAddress: :8889
webrtcAllowOrigin: '*'
webrtcEncryption: true
webrtcHandshakeTimeout: 20s
webrtcIPsFromInterfaces: false
webrtcLocalUDPAddress: :8189
webrtcServerCert: /certs/tls.crt
webrtcServerKey: /certs/tls.key
webrtcTrackGatherTimeout: 2s
----



입력 데이터는 `CCTVSchema` 모델을 기반으로 하며, 주요 필드는 다음과 같다:

[cols="1,1", options="header"]
|===
| 필드명 | 설명

| name | 카메라 이름
| ip | 카메라 IP 주소
| port | RTSP 포트
| id | 로그인 ID
| pw | 로그인 비밀번호
| server_url | 스트리밍 경로
| type | 카메라 타입(CCTV, PTZ)
|===

처리 흐름은 아래와 같다:

----
[Client] → [SVM Backend API] → [Storage Module (Redis)] → [mediamtx.yml 파일 갱신] → [Mediamtx RTSP 스트리밍]
----

web client는 webrtc 프로토콜을 사용하여 mediamtx에 접속하여 cctv 영상을 스트리밍 한다.

===== 실행 조건
- storage module이 정상 동작하여야 함.

===== 검증 기준

*API 응답*

- GET 및 POST 방식의 API 호출 시 정상적인 응답 상태코드(200 또는 201)를 반환해야 하며, 오류 시 적절한 예외 메시지를 반환해야 한다.

*Redis 저장값 정확성 검증*

- POST 호출 이후 `svm:cctv` 키로 Redis에 CCTV 정보가 정확히 저장되어야 한다.

*설정 파일 생성 및 갱신 검증*

- CCTV 정보 등록 시 `/data/hdd/cctv-config/mediamtx.yml` 경로에 설정 파일이 생성되거나 갱신되어야 한다.

*RTSP URL 구성*

-  각 CCTV에 대해 `paths` 항목에 RTSP URL이 `"cctv/<카메라 이름>"` 형식으로 정확히 포함되어야 한다.

*WebRTC 연결*

- Web Client는 WebRTC 프로토콜을 통해 Mediamtx에 접속하여 실시간 영상 스트리밍이 가능해야 한다.

*중복 등록 방지 로직 확인*

- 동일한 카메라 이름이나 IP가 중복 등록되지 않도록 방지 로직이 구현되어 있어야 하며, 중복 요청 시 명확한 오류 메시지가 반환되어야 한다.

*RTSP URL 연결 유효성 검사*

- 등록된 RTSP URL에 대해 실제 연결 테스트를 수행했을 때, 스트림을 받을 수 있어야 한다. 스트림 수신이 불가능한 경우에는 경고 처리되거나 설정 등록이 거부되어야 한다.

*파일 정합성 및 파싱 오류 방지*

- 설정 파일 저장 이후 YAML 포맷에 오류가 없어야 하며, Mediamtx 로딩 시 에러 없이 파일을 읽고 적용할 수 있어야 한다. 유효하지 않은 값이 있을 경우 시스템은 오류 로그를 출력하고 동작을 중단하지 않아야 한다.

*Redis 연결 오류 대응*

- Redis 연결이 실패할 경우, CCTV 정보 등록 요청은 실패로 처리되어야 하며, 적절한 오류 메시지가 반환되어야 한다. 또한, 시스템은 Redis 연결 복구 후에도 이전에 등록된 CCTV 정보를 유지해야 한다.

===== 성능 요구사항

*설정 반영 시간*

- CCTV 설정 변경 요청(API POST) 이후, `mediamtx.yml` 파일 갱신까지의 시간이 2초 이내여야 한다.

*최대 카메라 수용 수*

- 시스템은 최소 10개의 CCTV 설정을 동시에 처리할 수 있어야 하며, 설정 파일에도 모두 반영되어야 한다.
- 최대 카메라 수에 10개의 Client가 동시에 접속하더라도, 모든 클라이언트에 동일한 전송 품질(전송 간격, 지연 시간)을 보장해야 한다.

===== 인터페이스 요구사항

**FileSystem – `/data/hdd/cctv-config/mediamtx.yml` **

- Mediamtx의 설정 파일이 위치하는 경로로, API 동작 시 해당 파일을 갱신하거나 생성한다.

**Web Client → Mediamtx **

- WebRTC 프로토콜을 통해 설정된 CCTV RTSP 경로에 접근하여 스트리밍 영상을 재생한다.

===== 기타 요구사항
없음.

