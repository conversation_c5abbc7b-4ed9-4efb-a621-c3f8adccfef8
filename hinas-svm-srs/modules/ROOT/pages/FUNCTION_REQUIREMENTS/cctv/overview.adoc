=== CCTV & PTZ Control

==== Overview
===== Terminology

*CCTV*

고정형 카메라로, 일정한 방향으로 촬영하며 팬/틸트/줌 기능이 없는 일반 카메라

*PTZ*

팬(Pan), 틸트(Tilt), 줌(Zoom) 기능이 있는 제어형 카메라

*Mediamtx*

RTSP 및 WebRTC 기반의 미디어 스트리밍 서버

*WebRTC*

실시간 통신을 위한 오픈 웹 표준으로, 브라우저 간 실시간 미디어 스트리밍을 지원, 브라우저 기반 실시간 영상 스트리밍을 위한 전송 프로토콜로 사용

===== Table of Contents
[cols="1,1,4", options="header"]
|===
| No.
| Function
| Description

| 1
| CCTV view
| CCTV와 PTZ 카메라의 영상을 실시간으로 표시하는 사용자 인터페이스(UI)를 제공하며, 사용자가 CCTV 영상을 모니터링하고 PTZ 카메라를 제어할 수 있는 기능

| 1
| CCTV management
| CCTV 관리 기능은 CCTV view에서 현시하고자하는 CCTV의 정보를 등록 및 삭제, 수정할 수 있는 기능을 제공

| 2
| PTZ control
| PTZ(팬, 틸트, 줌) 카메라의 제어 기능을 제공하여 사용자가 카메라의 방향과 확대/축소를 조정할 수 있는 기능을 제공

|===

===== CCTV & PTZ 범위 및 의존성

*Storage Module*

- CCTV 설정 등록/조회 및 PTZ 카메라 제어를 위한 데이터 저장소로 사용됩니다.

*Mediamtx*

- RTSP 스트림을 WebRTC로 변환하여 실시간 영상 스트리밍을 제공합니다.

*Arges PTZ Camera*

- PTZ 제어 명령을 수신 및 수행하는 Arges 하드웨어 카메라 장비

*CCTV Camera (with RTSP)*

- CCTV 및 PTZ 카메라의 RTSP 스트림을 제공하는 IP 카메라 장비로, Mediamtx를 통해 WebRTC로 변환되어 클라이언트에 전달됩니다.

*CCTV Module*

- CCTV 및 PTZ 카메라의 RTSP 스트림을 수신하고, 이를 WebRTC로 변환하여 클라이언트에 전달하는 모듈입니다.

*SVM Backend*

- CCTV 및 PTZ 설정을 관리하는 API를 제공하며, Storage Module과 연동하여 설정 정보를 저장 및 조회합니다.

- PTZ 제어 API를 Wrapping하여 Arges PTZ 카메라에 제어 명령을 전달합니다.

<<<
include::cctv_view_ui.adoc[]

<<<
include::cctv_management.adoc[]

<<<
include::ptz_control.adoc[]


