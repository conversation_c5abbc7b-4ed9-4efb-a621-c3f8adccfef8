==== CCTV View UI


===== 설명

.CCTV View
image:SYSTEM_OVERVIEW/system_overview_cctv.png[align="center",600]

.PTZ Control View
image:SYSTEM_OVERVIEW/system_overview_ptz.png[align="center",600]

1. 총 4개의 카메라 영상이 2X2 Grid로 현시됩니다.
2. 각 영상 상단에 CCTV/PTZ 카메라 명칭이 표기되고, 영상 오른쪽 상단에는 CCTV와 PTZ를 구분할 수 있는 태그가 표기됩니다.
3. CCTV/PTZ 카메라 화면을 클릭하면 해당 영상이 확대되고, 한번 더 클릭하면 축소됩니다.
4. PTZ 카메라에 한해서 확대시 영상을 제어할 수 있는 버튼들이 현시됩니다.
5. 해당 버튼을 통해 PTZ 카메라 제어를 수행하며, 지원되는 기능은 다음과 같다.
- 상하좌우 이동: 버튼 클릭시 해당 방향으로 일정 거리 이동한 후 멈춘다.
- 줌 인/아웃: scroll up으로 줌인, scroll down을 통해 줌 아웃
- 와이퍼 동작: 오른쪽 하단의 wiper 버튼 클릭을 통해 와이퍼 기능을 제어한다.


===== 실행 조건
- CCTV/PTZ 영상 스트림이 정상적으로 출력되고 있는지 확인한다

===== 검증 기준
1. 카메라 명칭이 각 화면 상단에 정확히 표기되는지 확인한다.
2. PTZ 카메라 영상이 확대된 경우에만 제어용 Control 버튼들이 화면에 표시되어야 한다.
- 상하좌우 버튼 클릭을 통해 이동을 할 수 있으며, 약1초 간 해당 방향으로 이동되는지 확인한다.
- Scroll up을 통해 줌 인, Scroll down을 통해 줌 아웃이 동작하는지 확인한다.
- 오른쪽 하단의 wiper 버튼 클릭을 통해 와이퍼 기능이 동작하는지 확인한다.

===== 성능 요구사항
*영상 응답 지연 허용 한계*

- CCTV/PTZ 영상은 WebRTC 기반으로 실시간 스트리밍되며, 사용자 입력 및 시스템 이벤트에 따른 응답 지연은 다음 조건을 만족해야 함

- 시스템은 최대 10대까지의 CCTV/PTZ 카메라 등록 및 동시 표시를 지원하며, 해당 수량에서도 모든 응답 지연 조건을 만족해야 함

- 응답 지연 시간은 사용자 인터랙션 → 영상 반영까지의 **엔드 투 엔드 기준(End-to-End Delay)**으로 측정함

[cols="1,2", options="header"]
|===
| 측정 항목 | 요구 조건

| 초기 영상 연결 지연 (Initial Load Latency) | 스트림 요청 후 첫 프레임이 UI에 표시되기까지의 시간은 최대 1000ms 이내
| 영상 확대/축소 응답 시간 | 영상 클릭 후 확대 또는 축소된 상태로 UI에 반영되기까지의 시간은 500ms 이내
| PTZ 제어 반응 시간 | 상/하/좌/우 이동, 줌 인/아웃 등의 명령 입력 후 해당 동작이 카메라 피드에 반영되기까지 1000ms 이내
| 스파이크 대응력 (Burst Handling) | 시스템 부하 급증, 네트워크 지연 등 비정상 상황에서도 모든 응답 지연 항목이 1500ms 이내로 복구되어야 함
|===


*영상 프레임*

- CCTV/PTZ 카메라 영상은 WebRTC를 통해 실시간으로 렌더링되며, 영상 RTSP 입력이 안정적인 상태에서 시스템은 4시간 연속 동작 중에도 안정적으로 다음의 프레임율 조건을 만족해야 함
- 모든 지표는 각 카메라 스트림에 개별 적용되며, 단 1개의 카메라라도 조건을 만족하지 못할 경우 전체 시스템 결함으로 판정함

[cols="1,2", options="header"]
|===
| 측정 항목 | 요구 조건

| 평균 프레임율 (Mean FPS) | ≥ 15 FPS
| 95% 백분위수 (P95) | ≥ 14 FPS
| 99% 백분위수 (P99) | ≥ 12 FPS
| 프레임 드롭 지속 시간 | 4시간 중 FPS < 10 상태가 연속 3초 이상 발생 시 결함으로 간주
| 스파이크 대응력 | 시스템 부하가 순간적으로 2배 증가하더라도 위 모든 조건을 지속적으로 만족해야 함
| 동시 스트림 조건 | 10개 영상 동시 렌더링 상태에서 위 조건을 모두 만족해야 함
|===

*RTSP 재연결 성능*

- RTSP 기반 스트림 연결이 일시적으로 끊기는 경우에도, 시스템은 자동 복구 기능을 통해 영상이 즉시 복구되어야 하며, 4시간 이상 연속 운용 중에도 아래 조건을 만족해야 함

[cols="1,2", options="header"]
|===
| 측정 항목 | 요구 조건

| 자동 재연결 시작 지연 | RTSP 연결이 끊겼을 경우, 시스템은 3초 이내에 자동 재연결 시도를 시작해야 함
| 재연결 성공 시간 | 10초 이내에 영상 스트림이 복구되어야 함 (최대 3회까지 재시도 허용)
| 재연결 중 UI 상태 | 재연결 대기 중임을 사용자에게 명확하게 표시해야 하며, 빈 화면 또는 오류 화면은 허용되지 않음
|===


*메모리 및 CPU 사용량 제한 (Client Resource Limit)*

- CCTV/PTZ 영상은 WebRTC 기반으로 디코딩되며, 프론트엔드 브라우저는 최대 10개의 스트림을 동시에 처리하는 상황에서도 다음 자원 사용량 기준을 충족해야 함

[cols="1,2", options="header"]
|===
| 측정 항목 | 요구 조건

| 메모리 사용량 | 브라우저 탭 기준, 전체 메모리 사용량은 500MB 이하를 유지해야 함 (10채널 기준)
| CPU 점유율 | 4코어 기준, CPU 사용률은 평균 60% 이하를 유지해야 함 (스파이크 시에도 최대 80% 이하)
|===


===== 인터페이스 요구사항
CCTV 및 PTZ 카메라의 영상은 WebRTC를 통해 실시간으로 브라우저에 스트리밍됩니다.

===== 기타 요구사항
N/A