==== RTSP Converter

===== 설명
각 카메라에서 전달한 RTSP 스트림을 SVM Redis에 저장하는 기능이다. 이 기능은 RTSP 스트림을 이미지 캡쳐를 한후 SVM Redis에 저장하여 SVM 백엔드에서 서라운드뷰를 만들수 있도록 지원한다.


1. 설정된 카메라 갯수에 따라 RTSP Converter를 생성함.
- 각 카메라 rtsp converter 모듈 별 실행 환경 변수

+
[source,text]
----
- inputRtspAddress: 카메라의 RTSP IP(ex: ************)
- inputRtspPort: 카메라의 RTSP PORT(ex: 554)
- inputRtspStreamUrl: 카메라의 RTSP Stream URL(ex: /media/1/Profile1)
- name: 시스템 내부에서 사용하는 이름(svm-rtsp0~n)
- outputRedisKey: 각 카메라별 SVM 서버 Redis 저장할 키(ex: bas:agent:{카메라이름})
----

2. RTSP 스트림으로부터 이미지를 캡처하여 Redis에 저장하며, 각 이미지에는 TTL(Time-To-Live) 2초가 설정됩니다.

3. 캡처된 이미지는 해상도 1280x960으로 리사이징됩니다.

4. Redis 저장 키는 `bas:agent:{카메라이름}` 형식을 사용하며, 이는 <<camera_naming_ip_assignment_rule, Camera Naming & IP Assignment Rule>>을 따른다.


===== 실행 조건
IP Camera는 아래 정보로 세팅 되어 있어야 함.

1. 카메라 관리자 페이지에서 관리자 권한으로 계정생성한다.
- ID: avikus
- PW: avi1357!!

2. 카메라 관리자 페이지에서 RTSP 스트림을 설정한다.
- 해상도 1280x960
- 프레임레이트 30fps
- bitrate 4000kbps

3. RTSP 입력 구성
- 환경변수 리스트


===== 검증 기준
* 설정된 카메라 수에 따라 동일한 수의 RTSP Converter 프로세스가 정상적으로 실행되어야 한다.
* 각 RTSP Converter는 입력된 환경 변수(inputRtspAddress, inputRtspPort, inputRtspStreamUrl, name, outputRedisKey)에 따라 개별 동작해야 한다.
* 각 카메라로부터 수신한 RTSP 스트림이 이미지로 정상적으로 캡처되고, Redis에 설정된 outputRedisKey 형식(`bas:agent:{카메라이름}`)으로 저장되어야 한다.
* Redis에 저장된 이미지는 2초(TTL) 이내로 자동 만료되어야 한다.
* Redis에 저장된 이미지 해상도는 `1280x960`으로 확인되어야 한다.
* RTSP 수신 실패, Redis 저장 실패 등의 예외 상황에 대한 로그가 출력되고, 재시도 또는 오류 메시지 출력이 수행되어야 한다.

===== 성능 요구사항
- K3S 클러스터에서 각 모듈의 CPU 사용이 150, 메모리사용량 200 이하로 유지되어야 한다.

===== 인터페이스 요구사항
- IP Camera는 RTSP 프로토콜로 이미지 전달해야함.
- Redis 프로토콜을 사용하여 이미지 정보를 SVM Redis 서버에 저장해야 한다.

===== 기타 요구사항
- N/A
