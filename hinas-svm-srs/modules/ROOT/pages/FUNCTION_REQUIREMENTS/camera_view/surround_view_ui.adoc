==== Surround View

===== 설명

Surround View UI는 자선 주변을 정합된 다중 카메라 영상 기반으로 구성한 360도 시각 정보를 제공하는 인터페이스로, 사용자가 선박 주변 상황을 직관적으로 확인할 수 있도록 한다.

다음과 같은 주요 기능으로 구성됩니다.

https://www.figma.com/design/KRxrOCqd2RUIt7WA8ouwPz/SVM-v1.0.0---24.-10.-31-?node-id=11-34105&m=dev[SVM Figma Link]

====== Zoom In/Out

.Surround View Zoom In/Out UI
image::SYSTEM_OVERVIEW/system_overview_surround_view_camera_zoom_and_rotation.png[align="center",600]

* 사용자는 Zoom 버튼 또는 마우스 클릭을 통해 4방향(좌상/우상/좌하/우하) 영역을 개별 확대할 수 있으며, 확대된 화면에서 다시 클릭하면 원래 화면으로 복귀됩니다.

* 마우스 커서를 해당 영역에 올리면 `+ 아이콘`으로 변경

* 클릭 시 해당 영역이 확대되고 커서는 `- 아이콘`으로 변경

* 다시 클릭하면 줌 아웃되어 Main View로 전환

* 좌측 Zoom 버튼 패널에서도 4방향 확대 제어 가능

* 확대된 영역 버튼은 `Pressed` 상태로 표시됨


====== Rotation (가로 회전)

.Surround View Rotation UI
image::SYSTEM_OVERVIEW/system_overview_surround_view_rotation.png[align="center",600]

* 가로 회전 버튼 클릭 시 Surround View 화면이 전체화면(Horizontal Mode)으로 전환되며, 회전된 UI는 축소된 메뉴와 함께 제공됩니다.

* 회전 모드에서는 최소한의 기능 버튼만 표시됨 (이전화면, AIS 전환, 등거리선 설정, 등거리선 ON/OFF, AI 기반 물체 탐지 토글 등)

* 원상복귀 버튼으로 다시 일반 모드로 돌아올 수 있음


====== 등거리선 현시

.Surround View Equidistance Distance UI
image::SYSTEM_OVERVIEW/system_overview_surround_view_rotation.png[align="center",600]


* 자선 중심 기준으로 거리 간격을 나타내는 3개의 원형 등거리선을 그리며, 가까운 거리부터 색상은 `빨강 → 노랑 → 노랑`으로 구분됨

* 거리: 사용자 설정값 기준 *1 / *2 / *3

* 예: 10m 설정 시 → 10m, 20m, 30m 등거리선 현시

*등거리선 상태 API*


(1) 등거리선 On/Off 조회

** 기능 설명 : 등거리선 표시 여부를 조회하는 기능
** HTTP Method : GET
** Endpoint : /svm-api/v1/preference/safety-line-setting
** Request : 없음
** Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "Get preference successful.",
  "data": {
    "is_safety_line_on": true
  }
}
----

** 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get preference successful.)
+
data : 등거리선 on/off 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| is_safety_line_on | boolean | 등거리선 표시 여부 (true: 표시, false: 미표시)
|===


(2) 등거리선 On/Off 설정

** 기능 설명 : 등거리선 표시 여부를 설정하는 기능
** HTTP Method : POST
** Endpoint : /svm-api/v1/preference/safety-line-setting
** Request :
+
[source,json]
----
{
  "is_safety_line_on": true
}
----
** Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "created preference successful.",
  "data": {
    "is_safety_line_on": true
  }
}
----
** 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (created preference successful.)
+
data : 등거리선 on/off 설정 정보


====== 등거리선 설정

.Surround View Equidistance Setting UI
image::SYSTEM_OVERVIEW/system_overview_surround_view_equidistance_guide.png[align="center",600]

* 사용자가 거리 설정 버튼을 클릭하여 주변 등거리선 간격을 직접 선택할 수 있음

* 최대 등거리 범위 / 12 → 내림값 기준으로 최소 거리 결정

* 설정 가능한 간격: 최소거리의 1배/2배/3배

* 예: 최대 범위 30 → 최소 2m → 선택 옵션: 2 / 4 / 6m

*등거리선 거리 설정 API*

(1) 등거리선 거리 설정 조회

** 기능 설명 : 등거리선 거리 설정 정보를 조회하는 기능
** HTTP Method : GET
** Endpoint : /svm-api/v1/equip-distance
** Request : 없음
** Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "Get Equip Distance successful.",
  "data": {
    "equip_distance_gap": 6,
    "min_equip_distance_gap": 6
  }
}
----

** 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get Equip Distance successful.)
+
data : 등거리선 거리 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| equip_distance_gap | int | 현재 설정된 등거리선 간격 (단위: m)
| min_equip_distance_gap | int | 최소 설정 가능한 등거리선 간격 (단위: m)
|===
+
(2) 등거리선 거리 설정
+
** 기능 설명 : 등거리선 거리 설정 정보를 갱신하는 기능
** HTTP Method : PATCH
** Endpoint : /svm-api/v1/equip-distance
** Request :
+
[source,json]
----
{
  "equip_distance_gap": 10
}
----
** 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약 사항
| equip_distance_gap | float | 현재 설정된 등거리선 간격 (단위: m) | required, integer
|===
+
** Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "Update Equip Distance successful.",
  "data": null
}
----
** 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Update Equip Distance successful.)


====== Image No Signal

* 개별 카메라 영상 수신 불가 또는 서버 연결 오류 시 `Server Error` 등의 메시지와 함께 흐림 처리 또는 마스킹이 적용됨

** 영상 수신 오류 → "Server Error"

====== Measured Draught 설정

* 사용자가 드래프트 값을 수동으로 설정할 수 있으며, 설정된 드래프트는 View 구성(예: 수심 라벨 위치 등)에 반영됨

* Feature 버튼 클릭 → 드래프트 값 입력 후 Save

* 설정값은 View 갱신에 즉시 적용되며, 설정 상태 유지


*Measured Draught 설정 API*

해당 기능에 대한 자세한 설명은 <<draught_explain, Draught 설명>> 섹션을 참고하십시오.

(1) Draught 설정 조회

** 기능 설명 : 현재 Draught 설정 값을 조회하는 기능(단위: m)
** HTTP Method : GET
** Endpoint : /svm-api/v1/draught
** Request : 없음
** Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "Get measured draught successful.",
  "data": {
    "scantling_draught": 6.4,
    "measured_draught": 5
  }
}
----
** 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get measured draught successful.)
+
data : 드래프트 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| scantling_draught | float | 선박 설계 드래프트 (단위: m)
| measured_draught | float | 현재 측정된 드래프트 (단위: m)
|===

(2) 드래프트 설정

** 기능 설명 : 드래프트 값을 갱신하는 기능(단위: m)
** HTTP Method : PATCH
** Endpoint : /svm-api/v1/draught
** Request :
+
[source,json]
----
{
  "measured_draught": 5.5
}
----
** 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약 사항
| measured_draught | float | 현재 측정된 드래프트 (단위: m) | required, 0.1 ~ 선박의 z축 높이(m) 이하
|===
+
** Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "Update measured draught successful.",
  "data": null
}
----
** 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Update measured draught successful.)


===== 실행 조건

- svm backend 모듈을 통해 Surround View 영상 스트리밍이 가능해야 함
- Redis로부터 측정된 드래프트 값 및 카메라 상태 정보를 정상 수신 가능해야 함
- 뷰 보정 및 정합이 완료된 상태여야 함

===== 검증 기준

[cols="1,3", options="header"]
|===
| 항목 | 검증 기준
| 줌 인/아웃 기능 동작 | 사용자 입력에 따라 화면이 자연스럽게 확대/축소되어야 함
| 회전 기능 동작 | 회전 버튼 조작 시 뷰가 중심 기준으로 회전 해야하며, AR 아이콘도 동일하게 회전 위치에 현시되어야 한다.
| 등거리선 표시 | 등거리선이 정해진 간격으로 정확하게 표시되어야 하며 중심 기준으로 선박 외곽 형태로 현시되어야함.
| 등거리선 설정 기능 | 사용자 입력으로 간격 값이 변경되면 등거리선 간격이 즉시 반영되어야 함
| 영상 연결 상태 | 영상 미수신 시 각 Server Error 메시지 표기되어야함.
| 드래프트 설정 기능 | 측정 드래프트 값을 수동으로 설정하면, 뷰 구성에 반영되어야 함
|===

===== 성능 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항
| 인터랙션 반응 속도 | 줌/회전/설정값 변경 시 UI 반응은 200ms 이내여야 함
| 영상 디코딩 처리 | 각 카메라 영상은 15fps 이상 및 latency 1s 미만 실시간 재생이 가능해야 함
|===

===== 인터페이스 요구사항

[cols="1,3", options="header"]
|===
| 인터페이스 | 설명
| 영상 | surround view 영상
|===

===== 기타 요구사항

없음.
