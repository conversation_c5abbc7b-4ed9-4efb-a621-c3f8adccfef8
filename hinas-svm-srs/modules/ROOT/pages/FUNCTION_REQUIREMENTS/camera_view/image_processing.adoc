==== Image Processing

===== 설명

본 기능은 SVM 시스템에서 각 카메라의 스트림을 캡처하여 SVM-REDIS에 저장된 이미지를 기반으로,
SVM-BACKEND 및 BAS2D를 활용하여 어안렌즈로부터 입력된 왜곡 이미지를 디워핑(Dewarping)하고
Surround View 및 개별 Side View를 생성하여 화면에 표시하는 기능이다.

본 챕터의 Image Processing 기능을 이해하기 위해, 다음과 같은 기술 개념과 구성 요소에 대한 이해가 필요합니다.

1. Fisheye Lens 및 디워핑(Dewarping)
- Fisheye(어안) 렌즈는 넓은 시야각 확보를 위해 고의적인 왜곡을 포함한 광학 설계가 적용된 렌즈입니다.
- 디워핑이란 이러한 왜곡된 이미지를 수학적으로 보정하여 정사 형태(rectilinear)의 이미지로 복원하는 처리 과정입니다.
- 본 시스템에서는 각 카메라의 intrinsic(내부 파라미터) 및 distortion(왜곡 계수)를 기반으로 디워핑을 수행합니다.

2. Homography (호모그래피)
- Homography는 서로 다른 두 평면 간의 좌표 변환 관계를 수학적으로 표현하는 3x3 행렬입니다.
- 디워핑 이후의 영상은 Homography를 통해 정규화(normalized) 이미지 기준의 Surround View나 Side View로 변환됩니다.

3. BAS2D (영상 보정 및 합성 모듈)
- BAS2D는 SVM 시스템 내에서 디워핑을 수행하고, 복수 카메라의 출력을 통합하여 Surround View 또는 Side View를 생성하는 전용 영상 처리 라이브러리입니다.
- BAS2D는 각 카메라의 왜곡, 위치, 회전 정보를 바탕으로 다중 영상의 보정 및 정렬을 자동 수행합니다.

4. SVM-REDIS (이미지 수신 저장소)
- 각 카메라의 RTSP 스트림은 별도의 모듈(rtsp_to_redis)을 통해 프레임 단위로 캡처되어 Redis에 저장됩니다.
- 저장된 이미지는 후속 디워핑 및 뷰 생성 과정의 입력 데이터로 사용됩니다.

5. Surround 클래스
- 본 클래스는 카메라의 메타데이터(위치, 회전, 파라미터 등)를 기반으로 디워핑 및 뷰 생성을 위한 생성기(topview_creater, sideview_creater)를 구성합니다.
- 구성 정보가 변경될 경우 `refresh_creaters()` 메서드를 통해 무중단으로 재구성됩니다.

6. WebSocket 통신
- 디워핑된 이미지(topview, sideview)는 WebSocket을 통해 프론트엔드로 실시간 전송됩니다.
- 전송 대상은 사전에 정의된 채널(surround-view 등)로 구분되며, base64 인코딩된 JPEG로 전송됩니다.

프로세스는 다음과 같은 세 개의 주요 흐름으로 구성되고, 도식화한 이미지는 아래와 같다.


.Image Processing Process
image::FUNCTION_REQUIREMENTS/camera_view/image_processing.jpg[Image Processing Process]

1. Surround 클래스 초기화 및 View Creater 생성
+
1.1 Surround 클래스 생성
+
Surround 클래스는 다음과 같은 Storage Module의 정보를 기반으로 생성됩니다.

- camera_count : 카메라 댓수
- selected_sideview : 현시할 개별 카메라의 index 리스트(0 ~ 4개까지 가능, 인덱스는 0부터 시작됨.)
- input_channel_list : 카메라에서 들어온 이미지 캡쳐본이 svm-redis에 저장되는 키
- topview_shape : 서라운드뷰의 이미지 크기
- sideview_shape : 개별 카메라뷰의 이미지 크기
- draught : 선박의 흘수
- camera : 등록된 카메라의 정보
* index : 카메라의 인덱스 정보(0부터 시작하여, 오른쪽 기준으로 순차적으로 증가)
* name : 카메라의 위치 네임
* position : 카메라의 상대 위치 좌표 (x, y, z)
* rotation : 카메라의 회전 각도 (yaw, pitch, roll)
* sideview_scale : 개별 카메라뷰의 스케일
* ip : 카메라의 IP 주소
* intrinsic : 카메라의 내부 파라미터
** fx, fy : 초점거리 (focal length)
** cx, cy : 주점(principal point) 좌표
* distortion : 카메라의 왜곡 계수
** k1, k2, k3, k4 : 방사왜곡 보정 계수
* homography_norm2top : 정규화 이미지에서 탑뷰로 변환하는 Homography 행렬

+
1.2 뷰 생성기(topview_creater, sideview_creater) 구성
+
Surround 클래스 인스턴스는 다음 정보를 활용하여 topview_creater 및 sideview_creater를 생성한다.

- distortion_model_list : 각 카메라의 intrinsic 및 distortion 파라미터, 원본 소스 크기, fov 를 포함하는 리스트
- lookup_table : 각 카메라의 위치와 회전 정보를 포함하는 리스트
** position : 카메라의 상대 위치 좌표 (x, y, z)
** rotation : 카메라의 회전 각도 (yaw, pitch, roll)
** sideview_scale : 개별 카메라뷰의 스케일
** topview_scale : 서라운드뷰의 스케일
- topview_shape : 서라운드뷰의 이미지 크기
- sideview_shape : 개별 카메라뷰의 이미지 크기
- draught : 선박의 흘수

- 이 과정은 서버 시작 시 한 번만 수행되며, Surround 클래스 데이터가 변경되면
Surround.refresh_creaters() 메서드를 통해 갱신한다.

2. 이미지 디워핑 및 View 생성
+
2.1 이미지 캡처 및 유효성 검증

. input_channel_list에 포함된 각 Redis 키에서 이미지를 가져온다.
. 이미지가 정상적인 JPEG이면 해당 카메라 이름 기준으로 camera_status에 true를 저장한다.
. 이미지가 없거나 JPEG가 아닌 경우 false로 저장한다.

+
2.2 디워핑 및 View 생성

. 검증된 이미지를 topview_creater 및 sideview_creater에 전달한다.
. 생성기들은 BAS2D를 사용해 각 카메라의 이미지를 디워핑하여 다음 이미지를 생성한다:
  * topview_image : 왜곡 보정된 Surround View 이미지
  * sideview_images : 개별 카메라 뷰 디워핑 이미지 리스트
. 카메라 이미지가 존재하지 않을 경우 dummy_data를 이용하여 해당 영역을 대체한다.
. 생성된 nparray 이미지들은 클래스 변수에 JPEG로 인코딩되어 저장되어야 한다.
. 이 과정은 서버가 종료될 때까지 주기적으로 반복됩니다.

3. 이미지 전송 및 Frontend 현시

+
3.1 WebSocket 연결

+
서버 시작 시 Frontend와 사전에 정의된 WebSocket 채널을 통해 연결을 수립한다.
+
- surround-view : topview_image 전송
- top-left-view : 상단 왼쪽 카메라 뷰
- top-right-view : 상단 오른쪽 카메라 뷰
- bottom-left-view : 하단 왼쪽 카메라 뷰
- bottom-right-view : 하단 오른쪽 카메라 뷰

+
3.2 이미지 전송 로직

. 클래스 변수에 저장된 topview_image, sideview_images가 None이 아닌 경우:
  * jpeg base64로 인코딩된 후 WebSocket으로 전송
  * 전송 후 해당 클래스 변수는 `None`으로 초기화
. None일 경우, 이미지가 생성될 때까지 루프를 돌며 대기
. 생성 완료 시 WebSocket으로 다시 전송됨


===== 실행 조건

1. 시스템 초기화 조건

- 서버가 정상적으로 기동되어 있어야 한다.
- SVM-REDIS 서비스가 실행 중이어야 하며, 각 카메라의 이미지 채널이 사전 등록되어 있어야 한다.
- Storage Module에 카메라 구성 정보(camera_count, position, rotation, intrinsic 등)가 모두 정의되어 있어야 한다.


2. 입력 조건

- 각 카메라에서 RTSP 또는 유사한 스트림으로 영상이 입력되고 있어야 한다.
- rtsp_to_redis 모듈이 각 카메라의 프레임을 캡처하여 SVM-Redis에 저장할 수 있어야 한다.
- 입력되는 이미지는 JPEG 형식이어야 하며, Redis의 input_channel_list에 정의된 키에 저장되어야 한다.

3. 처리 조건

- Surround 클래스 및 생성기(topview_creater, sideview_creater)는 서버 시작 시 1회 이상 생성되어 있어야 한다.
- 클래스 내부의 refresh_creaters() 메서드는 Surround 클래스 구성이 변경되었을 때만 호출되어야 한다.
- 입력 이미지가 JPEG 형식이 아닐 경우 해당 카메라 상태는 `False`로 표시되며, 디워핑 과정에서는 dummy_data로 대체되어야 한다.
- 디워핑은 BAS2D에서 비동기 또는 동기 호출 방식으로 수행 가능해야 하며, 결과는 topview_image 및 sideview_images에 저장되어야 한다.

4. 출력 조건

- topview_image 및 sideview_images는 클래스 변수에 저장되며, 전송 후에는 None으로 초기화되어야 한다.
- WebSocket 연결이 성공적으로 이루어진 상태에서만 Frontend로 이미지 전송이 수행됩니다.
- 이미지가 존재하지 않으면, 다음 프레임이 도착할 때까지 반복 대기한다.

5. 반복 및 유지 조건

- 서버는 종료 전까지 이 프로세스를 주기적으로 반복 수행해야 한다.
- 입력 이미지가 지속적으로 SVM-Redis에 저장되지 않을 경우, 해당 카메라 상태를 False로 유지한다.
- 전체 시스템의 지속적인 동작을 위해 Redis와 BAS2D, Frontend WebSocket의 연결 상태는 주기적으로 점검되어야 한다.

===== 검증 기준

1. 초기화 검증

- 서버 기동 시 Surround 클래스 및 View Creater(topview_creater, sideview_creater)가 정상적으로 생성되어야 한다.
- Storage Module에 등록된 카메라 정보(camera_count, intrinsic, distortion, position, rotation 등)는 누락 없이 로드되어야 한다.
- Storage-Module, SVM-Redis, WebSocket 등 외부 의존 모듈은 모두 기동 상태를 유지해야 한다.

2. 입력 검증

- 각 카메라에서 입력되는 스트림은 SVM-Redis의 input_channel_list에 정의된 키에 따라 저장되어야 한다.
- 저장된 이미지는 JPEG 형식을 만족해야 하며, 디코딩이 가능해야 한다.
- JPEG 형식이 아닌 이미지가 수신될 경우, 해당 카메라의 상태는 camera_status에 False로 저장되어야 한다.

3. Surround 및 View 생성기 검증

- Surround 클래스는 설정된 카메라 수, 이미지 크기, 흘수 등의 파라미터를 반영하여 생성되어야 한다.
- topview_creater 및 sideview_creater는 distortion_model_list와 lookup_table 정보를 기반으로 정확하게 구성되어야 한다.
- Surround 클래스 구성이 변경되었을 경우, refresh_creaters() 메서드가 호출되어 생성기를 재구성해야 한다.

4. 디워핑 처리 검증

- 유효한 JPEG 이미지가 존재할 경우, BAS2D를 통해 디워핑된 topview_image 및 sideview_images가 생성되어야 한다.
- 생성된 디워핑 이미지는 왜곡이 보정되어 있어야 하며, 설정된 이미지 크기와 일치해야 한다.
- 이미지가 존재하지 않거나 오류가 발생한 경우, dummy_data(회색이미지)로 해당 영역이 대체되어야 한다.
- 생성된 이미지들은 클래스 변수에 저장되어야 한다.

5. WebSocket 전송 검증

- 서버 기동 시, Frontend와 정의된 WebSocket 채널들이 모두 연결되어야 한다.
- 클래스 변수에 저장된 이미지(topview_image, sideview_images)는 base64로 인코딩되어 WebSocket을 통해 전송되어야 한다.
- 이미지 전송 후 클래스 변수는 None으로 초기화되어야 한다.
- 이미지가 존재하지 않는 경우에는 새로운 이미지가 생성될 때까지 전송을 보류하고 루프를 유지해야 한다.

6. 반복 및 예외 처리 검증

- 전체 이미지 처리 프로세스는 서버 종료 시까지 주기적으로 반복되어야 한다.
- SVM-Redis에서 이미지가 수신되지 않는 경우, 해당 카메라 상태는 camera_status에 지속적으로 False로 유지되어야 한다.
- Storage Module, SVM-Redis, WebSocket 등 외부 시스템과의 연결이 끊어질 경우, 오류 로그를 남기고 재연결이 시도되어야 한다.

7. 전체 통합 검증

- 복수 카메라 입력이 동시에 들어올 경우, 각 카메라 뷰는 정상적으로 분리되고 Surround View에 정확하게 반영되어야 한다.


===== 성능 요구사항

- 실시간 프레임 처리 성능 및 메모리 사용량은 시스템 요구사항 범위 내에서 유지되어야 한다.
** cpu: 2000m
** memory: 3000Mi
- 각 카메라 영상의 레이턴시는 1초이내여야 한다.
- websocket을 통해 전송되는 이미지는 14fps 이상이어야한다.

===== 인터페이스 요구사항

본 기능은 외부 시스템 또는 모듈들과 다음과 같은 방식으로 인터페이스한다.

1. SVM-REDIS 인터페이스

- 목적: 각 카메라의 스트림 이미지(JPEG)를 실시간 저장 및 조회하기 위함
- 방식: Redis get/set Key-Value 구조 사용
- 입력 키 목록: input_channel_list 내 각 카메라별 채널명 (예: bas:agent:bow, bas:agent:stern 등)
- 데이터 형식: base64 인코딩된 JPEG 바이너리 또는 바이너리 스트림 그대로 저장
- 읽기 주체: backend 프로세스가 주기적으로 Redis에서 이미지 조회 (비동기 방식)

2. BAS2D 디워핑 처리 모듈

- 목적: 어안렌즈(Fisheye)로부터 입력된 왜곡 이미지를 디워핑하여 정사 이미지로 변환
- 방식: 내부 모듈 호출 (Python 객체 참조 : Surround)
- 입력:
  * 카메라별 디워핑 대상 이미지 리스트
  * 디워핑 파라미터 (intrinsic, distortion, homography 등 포함)
- 출력:
  * topview_image: 디워핑된 전체 서라운드 뷰 이미지
  * sideview_images: 각 카메라별 디워핑된 뷰 이미지 리스트
- 데이터 형식: numpy ndarray

3. WebSocket 인터페이스 (Frontend 연동)

- 목적: 디워핑된 이미지를 실시간으로 Frontend에 전송하여 현시
- 방식: WebSocket 채널을 통해 base64 이미지 전송
- 채널 목록:
  * surround-view : 전체 topview_image
  * top-left-view, top-right-view, bottom-left-view, bottom-right-view → 각 sideview 이미지
- 데이터 형식: base64로 인코딩된 JPEG 문자열
- 전송 조건:
  * 이미지 클래스 변수가 None이 아닐 경우에만 전송
  * 전송 후 변수 초기화 (None 처리)
  * 다음 이미지가 생성되기 전까지 대기 유지

4. Storage Module 인터페이스

- 목적: 카메라 구성 정보 및 초기 파라미터(캘리브레이션 데이터 포함) 조회
- 제공 데이터:
  * 카메라 수, 위치, 회전, intrinsic, distortion, homography 정보 등
- 사용 시점: 서버 시작 시 1회 로딩 및 이후 변경 시 재반영

5. rtsp_to_redis 모듈

- 목적: RTSP 스트림을 캡처하여 Redis에 실시간 저장
- 방식: GStreamer
- 출력 대상: SVM-Redis에 input_channel_list에 정의된 키로 저장
- 연동 방식: 독립 실행 모듈, backend와 직접 통신하지 않음

===== 기타 요구사항
1. 로그 및 모니터링

- 이미지 수신, 디워핑 처리, 전송 단계별로 주요 이벤트 로그를 남겨야 한다.
- 로그는 INFO, WARNING, ERROR 수준으로 구분되며, 파일 또는 표준 출력으로 저장되어야 한다.

2. 예외 처리 및 안정성

- Redis 연결이 실패할 경우 재시도 로직이 포함되어야 하며, 오류는 로그로 남겨야 한다.
- 카메라 이미지가 누락되거나 형식 오류가 발생할 경우 dummy_data로 대체하여 시스템 동작을 유지해야 한다.
- WebSocket 연결이 일시 중단될 경우 자동 복구 또는 재연결 시도가 수행되어야 한다.

3. 보안 요구사항

- SVM-Redis는 인증 및 접근 제어 설정이 가능해야 한다.

4. 유지보수 및 설정 관리

- 설정 변경 후 Surround.refresh_creater() 호출을 통해 무중단 갱신이 가능해야 한다.
- 코드 변경 없이 설정 변경만으로 뷰 스케일, 카메라 위치 등을 수정할 수 있어야 한다.