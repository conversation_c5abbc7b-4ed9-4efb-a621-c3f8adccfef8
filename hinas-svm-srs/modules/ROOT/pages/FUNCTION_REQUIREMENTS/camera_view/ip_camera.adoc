
==== Camera Naming & IP Assignment Rule

===== 설명

HiNAS SVM 시스템에 연결되는 IP 카메라들은 설치 위치에 따라 고유한 이름(Name)과 IP 주소(IP Address)를 가져야 하며, 시스템 내부에서 일관된 식별 및 통신을 보장하기 위해 고정된 규칙을 따릅니다.
본 항목에서는 선박에 설치되는 카메라 수에 따라 카메라 이름과 IP 주소를 자동으로 할당하는 규칙을 정의합니다.

====== 용어 정의

[cols="1,3", options="header"]
|===
| 용어 | 설명

| IP 주소 (IP Address)
| 네트워크 상에서 각 카메라를 식별하기 위한 고유 주소입니다. HiNAS SVM에서는 ************부터 시작하여 순차적으로 할당됩니다.

| Name (카메라 이름)
| Redis 등 내부 시스템에서 카메라 데이터를 구분하기 위한 키 문자열로, 설치 위치에 따라 bas:agent:bow, bas:agent:stbd 등으로 명명됩니다.

| bow / stern
| 각각 선박의 전방(선수) / 후방(선미)을 의미합니다.

| stbd / port
| 각각 선박의 우현(오른쪽) / 좌현(왼쪽)을 의미하며, 복수 카메라가 설치된 경우 -1, -2 등의 접미어를 통해 구분합니다.

| Redis 키
| SVM Backend에서 식별하고 처리하기 위해 사용하는 고유 키입니다. Name과 동일하게 구성됩니다.
|===


[[camera_naming_ip_assignment_rule]]
====== 카메라 이름 및 IP 할당 규칙

.Icon Design per Camera Configuration
image::FUNCTION_REQUIREMENTS/camera_view/camera_count_icon.png[align="center",300]


카메라 이름은 다음과 같은 패턴을 따릅니다:

[source]
----
bas:agent:{방향}[-번호]
----

- `{방향}`: bow, stern, stbd, port 중 하나
- `[-번호]`: 방향별 복수 카메라가 있을 경우 `-1`, `-2`, `-3` 등으로 확장
- 예시:
  - 단일 우현 카메라: `bas:agent:stbd`
  - 복수 우현 카메라: `bas:agent:stbd-1`, `bas:agent:stbd-2`, ...


- 이름은 카메라의 실제 설치 위치를 반영한다.

- IP 주소는 `************`부터 카메라 수만큼 순차적으로 증가한다.

아래 표는 **카메라 수(n)** 별로 할당되는 IP와 name 부분을 정리한 것이다:

.카메라 수: 4대
[cols="1,1,1", options="header"]
|===
| IP | Name | 설치 방향

| ************ | bas:agent:bow | 정면
| ************ | bas:agent:stbd | 우현
| ************ | bas:agent:port | 좌현
| ************ | bas:agent:stern | 선미
|===

.카메라 수: 6대
[cols="1,1,1", options="header"]
|===
| IP | Name | 설치 방향

| ************ | bas:agent:bow | 정면
| ************ | bas:agent:stbd-1 | 우현 1
| ************ | bas:agent:port-1 | 좌현 1
| ************ | bas:agent:stbd-2 | 우현 2
| ************ | bas:agent:port-2 | 좌현 2
| ************ | bas:agent:stern | 선미
|===

.카메라 수: 8대
[cols="1,1,1", options="header"]
|===
| IP | Name | 설치 방향

| ************ | bas:agent:bow | 정면
| ************ | bas:agent:stbd-1 | 우현 1
| ************ | bas:agent:port-1 | 좌현 1
| ************ | bas:agent:stbd-2 | 우현 2
| ************ | bas:agent:port-2 | 좌현 2
| ************ | bas:agent:stbd-3 | 우현 3
| ************ | bas:agent:port-3 | 좌현 3
| ************ | bas:agent:stern | 선미
|===

.카메라 수: 10대
[cols="1,1,1", options="header"]
|===
| IP | Name | 설치 방향

| ************ | bas:agent:bow | 정면
| ************ | bas:agent:stbd-1 | 우현 1
| ************ | bas:agent:port-1 | 좌현 1
| ************ | bas:agent:stbd-2 | 우현 2
| ************ | bas:agent:port-2 | 좌현 2
| ************ | bas:agent:stbd-3 | 우현 3
| ************ | bas:agent:port-3 | 좌현 3
| ************ | bas:agent:stbd-4 | 우현 4
| ************ | bas:agent:port-4 | 좌현 4
| ************ | bas:agent:stern | 선미
|===


[Note]
====
- `bow` 계열 키는 전방 영역을 의미함.  
- `stbd`, `port`는 각각 우현/좌현을 나타내며, 다수의 카메라가 있을 경우 `-1`, `-2` 등으로 확장됨  
- `stern`은 항상 마지막 카메라에 할당되어 선미 영역을 담당
====

===== 실행 조건
- hinas 365에서 카메라 갯수 선택 후 설치 파일 생성


===== 검증 기준

[cols="1,3", options="header"]
|===
| 항목 | 검증 기준

| IP 주소 일관성 | 각 카메라의 `IP`는 `************`부터 순차적으로 증가해야 하며, 중복되거나 누락된 주소가 없어야 한다.

| 카메라 수와 키 매핑 정확성 | 카메라 수(`n`)와 `IP` / `Name` 쌍이 정확히 n개씩 존재해야 한다.

| 방향 키 규칙 검증 | `bow`, `port`, `stbd`, `stern` 접미사는 실제 카메라 위치에 맞게 일관성 있게 사용되어야 하며, 좌우 대칭 구조를 따른다.
|===


===== 성능 요구사항

[cols="1,3", options="header"]

없음.

===== 인터페이스 요구사항

해당 기능은 설치 설정 시점에만 사용되며, 외부 시스템과의 실시간 인터페이스 요구사항은 존재하지 않습니다.


===== 기타 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항

| 설정 자동화 | 카메라 수가 변경될 경우, HiNAS 365를 통해 설치 파일을 새로 생성 하면, IP 및 Redis 키는 규칙에 따라 자동 할당되며 수동 설정이 없어야 한다.

| 방향 일관성 | 좌/우측 카메라(`port`, `stbd`)는 항상 페어로 존재해야 하며, `-1`, `-2` 등의 접미사는 짝수 개수 기준으로 확장되어야 한다.

| 중복 방지 | 동일한 IP 주소 또는 Redis 키가 설정 내에서 중복되지 않도록 초기화 시점에 유효성 검사 로직이 포함되어야 한다.

|===
