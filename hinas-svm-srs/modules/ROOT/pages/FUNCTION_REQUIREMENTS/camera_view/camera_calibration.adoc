==== Camera Calibration

===== 설명


아래 이미지는 Camera Calibration 전후의 영상 비교를 통해 왜곡 보정 효과를 보여줍니다.

.Camera Calibration Results
image::FUNCTION_REQUIREMENTS/camera_view/camera_calibration_1.png[align="center",600]

.Camera Calibration Results 2
image::FUNCTION_REQUIREMENTS/camera_view/camera_calibration_2.png[align="center",600]

Camera Calibration 처리 이후, 다음과 같은 개선 효과가 있습니다.

* 등거리선 왜곡이 현저히 감소하였습니다.

** Calibration 이전 영상에서는 수평선과 주변 해수면의 곡률 왜곡이 눈에 띄게 발생하고 있습니다.

** Calibration 이후 영상에서는 수평선과 파형이 더 자연스럽고 실제 시야에 가까운 형태로 보정되었습니다.

* 중앙 및 주변부의 비율 왜곡이 개선되어, 실제 거리감과 시각적 일관성이 향상되었습니다.

* 해상도 및 중심부 초점의 정확도 향상으로 인해, 객체 위치 판단 및 영상 융합 품질이 향상됩니다.


본 기능은 카메라 제조사에서 제공하는 HTTP API를 활용하여, 각 카메라의 내부 보정값(왜곡 계수 및 내부 파라미터)을 설정하고 조회하는 기능입니다.
이 기능을 통해 SVM의 Surround View 영상 정합(Stitching)에 필요한 보정 정보를 카메라 내부에 직접 저장함으로써, 별도의 외부 보정 데이터 관리 없이 일관된 왜곡 보정 처리가 가능해집니다.

[IMPORTANT]
해당 기능은 Arges 카메라의 ""버전에 한정되어 제공되며, 다른 제조사의 카메라 또는 구버전 Arges 카메라에서는 지원되지 않을 수 있습니다.


카메라 API는 인증 절차를 포함하며, 다음과 같은 형식으로 호출됩니다.

*1. 보정값 저장*

카메라에 보정값을 저장할 때는 아래 API를 호출합니다.

[source,url]
----
GET http://{camera_ip}/setup/system/ext_config.php?app=set&intrinsic_fx=...&intrinsic_cx=...&distortion_k1=...&fov=180.0
----

파라미터에는 다음과 같은 항목들이 포함됩니다.

* `intrinsic_fx`, `intrinsic_fy`: x, y 방향의 초점거리 (focal length)
* `intrinsic_cx`, `intrinsic_cy`: 주점(principal point) 좌표
* `distortion_k1` ~ `k4`: 방사왜곡 보정 계수
* `fov`: 카메라의 수평 시야각(Field of View)

해당 API를 호출할 때는 Digest 인증이 필요하며, 인증 절차는 다음과 같이 수행합니다.

. 인증이 필요한 API에 빈 Authorization 헤더로 먼저 요청 → HTTP 401 응답 수신
. 응답 헤더의 `WWW-Authenticate` 값을 파싱하여 `realm`, `nonce`, `opaque`, `algorithm`, `qop` 정보를 추출
. 위 정보를 기반으로 Digest 인증 문자열 생성 후 Authorization 헤더로 추가하여 재호출
. 최종적으로 HTTP 200 응답을 수신하면 보정값 저장 성공

*2. 보정값 조회*

이미 저장된 보정값은 다음 API를 통해 조회할 수 있습니다.

[source,url]
----
GET http://{camera_ip}/setup/system/ext_config.php?app=get
----

응답은 `res=200` 상태와 함께 내부 파라미터 및 왜곡 계수 등이 쿼리 스트링 형식으로 포함되어 있으며, 다음과 같은 정보들이 포함됩니다.

* `intrinsic_fx`, `fy`, `cx`, `cy`
* `distortion_k1` ~ `k4`
* (추가적으로 `homography_h00` ~ `h22` 값이 존재할 수 있음)
* `fov`

예시 응답:

[source,text]
----
res=200&intrinsic_fx=385.215&intrinsic_fy=385.351&...&distortion_k4=-0.0003...&fov=180.0
----

이 기능은 각 카메라의 왜곡 보정 계수(`distortion`) 및 내부 파라미터(`intrinsic`)를 카메라 내부에 직접 저장하고 조회하는 기능을 제공한다. 제조사에서 제공하는 Digest 인증 기반 API를 사용하여 보정값을 설정하거나 확인할 수 있다.

이렇게 저장된 보정값은 각 카메라별로 고유하게 관리되며, RTSP 스트리밍 기반 SVM Camera Streaming 기능에서 해당 정보를 활용하여 이미지 캡처 시 실시간으로 보정된 시야를 확보하게 됩니다. 이를 통해 서라운드 뷰 합성 과정에서 왜곡 없는 정확한 정합 결과를 보장할 수 있다.


===== 실행 조건
지원하는 IP Camera 정보는 아래와 같다.

* 카메라 제조사: Arges
* 카메라 모델: Arges IP Camera
* 카메라 펌웨어 버전: v이상



IP Camera는 아래 정보로 세팅 되어 있어야 함.

1. 카메라 관리자 페이지에서 관리자 권한으로 계정생성한다.
- ID: avikus
- PW: avi1357!!

2. 카메라 관리자 페이지에서 RTSP 스트림을 설정한다.
- 해상도 1280x960
- 프레임레이트 30fps
- bitrate 4000kbps

3. RTSP 입력 구성
- 환경변수 리스트

===== 검증 기준
* 1. 카메라 보정값 저장 API 호출 시, HTTP 응답 코드가 `200 OK`이어야 한다.
* 2. 저장 API 호출 이후, 조회 API(`?app=get`)로 동일 카메라에 요청 시 설정한 보정 파라미터와 동일한 값이 반환되어야 한다.
* 4. 응답 값 내 모든 보정 파라미터(`intrinsic_fx`, `cx`, `distortion_k1`, …)가 누락 없이 포함되어 있어야 한다.
* 5. 인증 로직이 미리 정의된 Digest 인증 방식으로 정확히 수행되어야 하며, 인증 실패 시 재시도 또는 명확한 에러 로그가 기록되어야 한다.

===== 성능 요구사항
- N/A

===== 인터페이스 요구사항
- 카메라 제조사에서 제공된 API를 사용하여 카메라 내부에 보정값을 저장하고 조회해야 합니다.

===== 기타 요구사항
- N/A
