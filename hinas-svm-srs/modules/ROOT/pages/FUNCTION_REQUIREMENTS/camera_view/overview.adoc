=== Surround & Side View

==== Overview

===== Terminology
이 섹션에서는 본 챕터에서 반복적으로 사용되는 주요 구성 요소 및 용어를 간략히 정의합니다.

- *RTSP to Redis Converter*: IP 카메라로부터 수신한 RTSP 스트림을 Redis에 실시간으로 저장하는 모듈
- *SVM Backend*: Redis로부터 영상 데이터를 수신하고, Surround/Side View를 생성하는 주 처리 모듈
- *Bas2D Library*: 영상 기하 보정 및 파노라마 정합성을 위한 내부 영상 처리 라이브러리
- *Calibration*: 각 카메라의 내부/외부 파라미터를 설정하고 보정하는 절차
- *Fish-eye Image*: 어안 렌즈 특유의 왜곡이 포함된 원본 영상

===== Table of Contents
[cols="1,1,4", options="header"]
|===
| No.
| Function
| Description

| 1
| Surround View
| Surround View의 화면 구성 및 기능에 대해서 설명합니다.

| 2
| Side View
| Side View의 화면 구성 및 기능에 대해서 설명합니다.

| 3
| IP Camera
| 카메라의 이름과 IP 주소 할당 규칙에 대한 정의

| 4
| Rtsp Converter
| IP 카메라로부터 RTSP 스트림을 수신하고, 이를 처리하여 이미지로 변환

| 5
| Image Processing
| SVM-BACKEND 및 BAS2D를 활용하여 어안렌즈로부터 입력된 왜곡 이미지를 디워핑(Dewarping)하고 Surround View 및 개별 Side View를 생성하여 화면에 표시하는 기능


| 6
| Camera Calibration
| 카메라 제조사에서 제공하는 HTTP API를 활용하여 각 카메라의 내부 보정값(왜곡 계수 및 내부 파라미터)을 설정하고, 설정된 값을 조회하는 기능

|===

본 문서는 HiNAS SVM의 Surround View 및 Side View 기능에 대한 요구사항을 정의합니다.
본 챕터에서는 다음 내용을 순차적으로 설명합니다.



1. Surround View

2. Side View

3. IP 카메라 이름 및 IP 주소 할당 규칙

4. RTSP 스트림의 수신 및 처리 방식

5. SVM Backend에서 Bas2D Library를 활용한 Surround View 및 Side View 생성 절차

6. 고품질 영상 생성을 위한 개별 카메라의 보정(Calibration) 방법


1, 2장에서는 Surround View와 Side View의 화면 구성 및 기능에 대해 설명합니다.
3장에서는 IP 카메라의 이름 및 IP 주소 할당 규칙을 정의합니다.
RTSP 스트림 처리 방식에 대해서는 4장에서 다루며, 5장에서는 SVM Backend가 Bas2d Lib을 활용해 Surround View 및 Side View 생성 프로세스를 상세히 설명합니다.
마지막으로 6장에서는 카메라 개별의 Calibration에 대해서 설명합니다.



===== Surround View 및 Side View 범위

*RTSP to Redis Converter*

- 선박에 설치된 IP 카메라로부터 RTSP(RTP over TCP/UDP) 영상 스트림을 수신합니다

- 수신한 각 채널의 영상 프레임을 Redis 서버에 실시간으로 PUT 하여, 후속 처리 모듈(SVM Backend 등)이 참조할 수 있도록 공유 메모리 역할을 수행합니다.

- 스트림 끊김, 시간 동기화, 프레임 유실 등 예외 상황에 대한 최소한의 에러 핸들링도 포함됩니다.

*SVM Backend*

- Redis에 저장된 카메라 영상 데이터를 수신하여 영상 처리 파이프라인을 실행합니다.

- Fish-eye 원본 영상을 기반으로 Warping, Blending, Masking 등의 처리 과정을 거쳐 Surround View 및 Side View 영상으로 변환합니다.

- 영상 보정값(Scale, Pitch, Yaw 등)을 적용하고, Inference 및 Overlay 데이터를 병합합니다.

- 최종 결과를 WebSocket 또는 HTTP API로 SVM Frontend에 전달합니다.


*SVM Frontend*

- SVM Backend로부터 전달받은 영상 스트림 및 UI 데이터를 사용자 인터페이스 상에 표시합니다.

- Surround View 및 Side View를 시각적으로 구성하며, 카메라 확대, 회전, 선택 등의 인터랙션을 제공합니다.

- 등거리선, 타겟 정보 패널, 녹화 상태 등 부가 UI 요소도 함께 구성되어, 운항자가 실시간 정보를 직관적으로 파악할 수 있도록 지원합니다.

*Bas2D Library*

- SVM Backend 내에서 사용되는 2D 영상 처리 전용 라이브러리로, 영상의 기하 변환, 투시 보정, 경계 블렌딩 등 시각적 정합성 확보에 필요한 핵심 알고리즘을 포함합니다.

- Side View 영상 정렬, Surround View 파노라마 구성 시 영상 간 이음새(seam) 처리에 필수적으로 사용됩니다.

- 모든 영상 변환 연산은 실시간 처리를 고려한 최적화된 방식으로 수행되며, GPU 가속을 지원할 수 있습니다.


===== Surround View 및 Side View 의존성

Surround View 및 Side View 기능은 여러 구성 요소의 정상 동작에 의존합니다. 각 기능은 하위 계층의 데이터 수신, 처리, 전달이 선행되어야 하며, 일부 구성요소가 실패할 경우 전체 기능이 제한되거나 중단될 수 있습니다.


*IP Camera (RTSP 송신)*

- 모든 기능의 입력 소스로서, RTSP 프로토콜을 통해 실시간 영상을 송신합니다.

- 영상이 정상적으로 전송되지 않으면 이후 모든 처리 단계에 영향이 발생합니다.

*RTSP to Redis Converter*

- RTSP 스트림을 Redis로 변환하여 전달합니다.
- Converter가 비정상 동작 시, SVM Backend는 영상 데이터를 수신하지 못하므로 Surround/Side View 생성 불가.

*Redis*

- 영상 프레임을 공유하는 실시간 메모리 버퍼 역할을 수행합니다.

- 데이터 정합성 유지 및 시간 동기화를 위해 Redis는 반드시 안정적으로 운영되어야 합니다.

*SVM Backend*

- Redis로부터 영상을 수신하고 영상 처리 및 변환을 수행합니다.

- Calibration 설정값, 영상 보정 알고리즘(Bas2D), Inference 처리 등 다수 모듈과 연동됩니다.

- Backend 장애 시 영상 생성이 불가하며, Frontend에 영상이 전달되지 않습니다.

*Bas2D Library*

- 영상 기하 보정 및 시야 연결을 위한 핵심 알고리즘을 제공합니다.

- 해당 라이브러리에 의존하여 영상 간의 시각적 정합성을 유지합니다.

*SVM Frontend*

- Backend로부터 전달된 영상을 화면에 렌더링합니다.

- WebSocket 또는 HTTP API 연결이 필수이며, 통신 장애 시 영상이 표시되지 않음.

- 사용자 인터랙션(UI 이벤트), Overlay 현시 기능 등도 포함됩니다.

*Calibration 데이터*

- Surround View 및 Side View 영상의 왜곡 보정과 정확한 시야 확보에 필수적입니다.

- Ship Spec, CCRP, Camera 위치 정보가 정확히 입력되어 있어야 정상 작동합니다.


[IMPORTANT]
====
모든 구성 요소는 실시간 동기화와 연속 프레임 수신을 전제로 하며,
단일 구성요소의 실패가 전체 영상 시스템에 영향을 미칠 수 있습니다.
시스템 운영 시, 각 계층별 상태 점검 및 로그 모니터링이 필수적입니다.
====

<<<
include::surround_view_ui.adoc[]

<<<
include::side_view_ui.adoc[]

<<<
include::ip_camera.adoc[]

<<<
include::rtsp_converter.adoc[]

<<<
include::image_processing.adoc[]

<<<
include::camera_calibration.adoc[]


