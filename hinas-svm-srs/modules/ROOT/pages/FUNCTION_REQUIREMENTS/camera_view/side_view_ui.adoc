==== Side View UI

===== 설명

Side View UI는 자선의 좌우측면에 장착된 복수의 카메라를 기반으로, 선박 주변 상황을 측면에서 실시간으로 확인할 수 있는 인터페이스이다. 항해 중 장애물 식별 및 안전 거리 확보를 위한 시각 정보를 제공하며, 다음과 같은 기능으로 구성됩니다.

Side View UI 및 Flow의 경우 아래 링크를 참조한다.

https://www.figma.com/design/KRxrOCqd2RUIt7WA8ouwPz/SVM-v1.0.0---24.-10.-31-?node-id=17-37526&m=dev[HiNAS SVM Side View UI Figma]

====== Side View 현시
.Side View UI
image::SYSTEM_OVERVIEW/system_overview_side_view.png[align="center",600]

- 우측에 4개의 Side View 화면이 구성되며, 각 화면 상단에는 카메라 명칭이 표시됩니다. 카메라가 어떤 방향을 보고 있는지 시각적으로 명확하게 나타낸다.

====== Side View Zoom In/Out
.Side View Zoom In UI
image::SYSTEM_OVERVIEW/system_overview_side_view_zoom.png[align="center",600]

- 개별 Side View 화면을 클릭하면 해당 영상이 확대되어 전체 화면으로 전환되며, 다시 클릭하면 축소되어 4분할 화면으로 돌아온다.


====== Side View Select (카메라 선택 기능)
.Side View Camera Selection UI
image::SYSTEM_OVERVIEW/system_overview_side_view_camera_selector.png[align="center",600]

- 사용자 정의 Camera Set을 구성 가능

- 최대 4개의 카메라를 선택하여 View에 표시

- 카메라 수에 따라 명칭 및 위치 자동 설정 (좌측 선택 UI 자동 배치)

- 카메라 위치를 잘못 지정(예: PORT를 우측, STBD를 좌측)에 저장 시 경고 메시지 출력

- 선택된 카메라는 View 상단 우측에 아이콘으로 식별

*Side View 선택 API*

(1) 현시 선택된 Side View List 조회

** 기능 설명 : 현재 선택된 Side View 카메라 목록을 조회하는 기능
** HTTP Method : GET
** Endpoint : /svm-api/v1/preference/selected-sideview
** Request : 없음
** Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "Get preference successful.",
  "data": {
    "selected_sideview": [
      3,
      2,
      4,
      1
    ]
  }
}
----
** 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get preference successful.)
+
data : 선택된 Side View 카메라 목록 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| selected_sideview | array of integers | 선택된 Side View 카메라 ID 목록 (예: [3, 2, 4, 1])
|===

(2) Side View 카메라 선택 설정

** 기능 설명 : Side View에 표시할 카메라를 설정하는 기능
** HTTP Method : POST
** Endpoint : /svm-api/v1/preference/selected-sideview
** Request :
+
[source,json]
----
{
  "selected_sideview": [3, 2, 4, 1]
}
----
** 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약 사항
| selected_sideview | List[int] | 현시할 카메라의 인덱스 | 최대 4개
|===
+
** Response :
+
[source,json]
----
{
  "code": 200000,
  "message": "created preference successful.",
  "data": {
    "selected_sideview": [
      1,
      2,
      3
    ]
  }
}
----
** 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (created preference successful.)
+
data : 선택된 Side View 카메라 목록 정보

====== Video Recording Status

- 개별 카메라가 녹화되지 않을 경우 오류 메시지를 View 상단에 표시

- "Recording error" 메시지 클릭 시, 에러 발생 카메라 목록을 팝업으로 제공

====== Side View 등거리선 On/Off

- 버튼 클릭을 통해 Side View의 등거리선을 ON/OFF 설정

- Surround View의 등거리선 On/Off 설정과 연동되어 동기화됨

====== Image No Signal

*  영상 미수신 또는 서버 오류 시 다음 메시지 표시

** "Please select a camera" (카메라 미선택)

** "Server Error"

** "Camera Error"

===== 실행 조건

- 이미지가 svm backend로부터 정상적으로 수신되어야 함

===== 검증 기준

[cols="1,3", options="header"]
|===
| 항목 | 검증 기준
| View 표시 정확성 | 선택된 4개의 카메라 View가 정확히 매핑되어 출력되어야 함
| 영상 확대 동작 | View 클릭 시 영상이 정확히 확대되고, 다시 클릭 시 축소되어야 함
| 카메라 선택 UI | 최대 4개 선택 및 위치 규칙 위반 시 경고 메시지가 출력되어야 함
| 영상 오류 처리 | RTSP 오류 시 지정된 오류 메시지 및 마스킹이 정확히 적용되어야 함
| 레코딩 상태 표시 | 녹화 중단 시 View 상단에 오류 메시지가 표시되어야 함
| 등거리선 ON/OFF 연동 | Surround View와 Side View 간 등거리선 설정이 연동되어야 함
|===

===== 성능 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항
| 영상 재생 성능 | 각 카메라 영상은 최소 13fps 이상 및 1초 이내의 지연으로 실시간 재생되어야 함
|===


*등거리선 검증*

Surround View와 Side View는 동일한 자선 기준 좌표계와 거리 기준(min equidistance)을 기반으로 등거리선을 시각화한다. 동일한 물체가 두 View에 동시에 표시될 경우, 해당 물체의 기준점은 **같은 등거리선의 동일한 위치(같은 각도, 같은 점)**에 있어야 한다. 이 일치성은 거리 감각의 신뢰성 확보를 위한 필수 조건이다.

- 동일한 물체(예: 주변 Tug선)의 기준점(예: 선체 중심 또는 선수부)은 Surround View와 Side View 모두에서 **같은 등거리선 상의 동일한 위치(같은 점, 같은 각도 방향)**에 표시되어야 한다.

- 등거리선은 자선 중심을 기준으로 방사형으로 균일하게 생성되며, 두 View 간의 거리 간격(min_equidistance × 배수) 및 각도 기준이 완전히 동일해야 한다.

- View 해상도, 왜곡 보정 여부, 좌표 변환 차이 등 모든 렌더링 요소는 결과적으로 기준점의 위치 일치를 보장해야 하며, 위치 오차 허용 범위는 ±1픽셀 또는 ±1도 이내로 제한한다.

- 검증 기준

** 총 검사 프레임 수: M 프레임

*** 기준점이 두 View 모두에 유효하게 표시된 프레임만 검증 대상으로 간주한다.

** 검증 방식

*** 각 프레임마다 기준점이 동일한 등거리선 상의 동일 위치(같은 거리, 같은 각도 좌표)에 있는지 비교

*** 위치 불일치가 감지되면 해당 프레임은 일치하지 않는 것으로 처리

** 합격 조건:

*** 전체 M 프레임 중 80% 이상의 프레임에서 기준점이 같은 등거리선의 동일한 위치에 표시되어야 함

*** 일치율 = (일치한 프레임 수 / M) × 100 ≥ 80%

*등거리선 검증 실제 예시*

*상황*

- 자선 기준으로 동북방(45도 방향)에 **Tug선 A**가 위치해 있음
- 이 **Tug선 A**는 자선으로부터 정확히 **10m 거리**에 위치 (즉, `min_equidistance × 2`)
- 기준점은 Tug선의 중심점 (도면상 빨간 점)

*요구되는 화면 결과*

- Surround View에서 Tug선 A의 중심은 `"10m 등거리선"`의 45도 위치에 있어야 함
- Side View에서도 Tug선 A의 중심은 같은 10m 등거리선의 45도 위치에 있어야 함

*검증 절차 예시*

- 10,000 프레임짜리 실시간 영상 리플레이 중
- 프레임마다 Tug선 A의 중심이 두 View 모두에서 *10m 거리*, *45도 방향* 위치에 있는지 확인
- 허용 오차: 거리 ±1픽셀, 각도 ±1도

*검증 결과 예시*

- 총 프레임 수: 10,000
- 일치 프레임 수: 8,450
- 오차 또는 센서 노이즈 등으로 불일치 프레임 수: 1,550
- *일치율: 84.5% → 기준(80%) 이상 → 통과*



===== 인터페이스 요구사항

[cols="1,3", options="header"]
|===
| 인터페이스 | 설명
| 영상 | side view 영상
|===

===== 기타 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 설명
| 카메라 위치 검증 | PORT/STBD 위치 위반 시 경고 모달 출력
| 설정값 유지 | 선택된 카메라 셋은 세션 내 지속적으로 유지되어야 함
| View 레이아웃 고정 | 4분할 화면 배치는 위치 및 순서가 고정되어야 함
|===
