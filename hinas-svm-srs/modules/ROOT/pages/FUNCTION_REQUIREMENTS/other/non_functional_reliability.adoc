==== 시스템 안정성 요구사항

===== Blackout Test (전체 전원 차단 후 복구 테스트)

- 전원 공급 중단(Blackout) 이후 시스템이 복구될 경우, **1분 이내에 모든 핵심 모듈이 자동 기동 및 정상 상태로 진입**해야 함
- 검증 대상 모듈:
** SVM Backend
** Inference Module
** Redis
** RTSP to Redis
** Video Recorder
- 정상 상태 조건:
** 각 모듈의 헬스체크 API 또는 내부 상태값 기준으로 **"Ready" 상태**가 확인되어야 함
** Redis 연결 및 데이터 접근 가능 여부
** WebSocket 연결 가능 여부

===== 모듈 단위 재연결 복원 시나리오

- 개별 모듈(Network 단절, 프로세스 재시작 포함)이 중단되었다가 복원되는 경우, **해당 모듈은 10초 이내 재연결 또는 복구**되어야 함
- 상태 복원 시, 이전의 캐시 또는 연결 정보를 기반으로 세션 유지 또는 자동 재동기화가 이루어져야 함
- 예외 상황 예시:
** RTSP to Redis가 재시작되었을 때, Redis에 누락 없이 이미지 스트림이 재전송되어야 함
** Inference 모듈이 재기동 시, Redis에서 최신 이미지 또는 상태를 기반으로 추론 재개

===== 저장 공간 관리 정책

- 시스템은 주기적으로 디스크 사용량을 점검하고, **HDD 또는 SSD 사용량이 70% 이상인 경우 자동 정리 루틴을 실행**해야 함

- 정리 방식 상세:
** 정리 대상: `/data/hdd/logs/*`, `/data/hdd/videos/*` 전체 파일
** 우선순위: **가장 오래된 파일부터 삭제**
** 용량 감소 목표: 사용량을 **50% 이하**로 줄일 때까지 반복
** 정리 주기: **5분마다 디스크 상태 점검 후 조건 충족 시 자동 수행**
** 삭제 후 로그 기록 필수 (`/var/log/cleanup.log` 등)

- 저장소 위치 및 용도 구분:
** SSD (468GB): 설정 파일, 헬스 체크 상태, 실시간 동작 정보
** HDD (3.2TB): 영상 녹화 파일, 로그 데이터, MinIO Object Storage 데이터

===== 리소스 사용량 제한

각 모듈의 리소스 사용 한계는 다음과 같음:

[cols="1,1,1,1", options="header"]
|===
| Module | CPU (milli) | Memory (MB) | GPU

| SVM Backend     | 2000 | 1500 | -
| Inference       | 1000 | 2000 | -
|===
