==== DB Migration: Init Container 설정

===== 설명
이 기능은 SVM 시스템의 정상적인 동작을 위해 필요한 초기 설정값을 구성하는 역할을 한다. 설정 변수들은
시스템 동작 및 환경 구성에 필요한 다양한 정보를 포함하고 있으며, 각 변수는 고유한 기능을 수행한다.

Init Container는 컨테이너 실행 시 가장 먼저 실행되며, Storage Module에 해당 키에 대한 값이
존재하지 않을 경우 사전에 정의된 기본값을 저장한다. 이때 저장되는 데이터는 다음과 같다.

- Init Container 초기 세팅 변수 리스트
+
1) default_camera
기본 카메라 설정 정보로, 세팅 페이지에서 기본 설정 시 사용됩니다. 사용 카메라 수에 맞춰 설정된 calibration 정보를 camera에 저장하는 데 활용됩니다.
+
2) camera_models
사용 가능한 카메라 모델 목록이며, 각 항목에는 제조사와 모델명이 포함됩니다.
+
3) status_code
시스템의 상태 코드를 나타내며, 정상 작동 여부를 판단하는 데 사용됩니다.
+
4) safety_line
선박의 등거리선 사용 여부를 판단하는 데 사용됩니다.
+
5) camera_model
현재 사용 중인 카메라 모델 정보이며, 제조사와 모델명을 포함한다.
+
6) camera_count
시스템에 등록된 카메라 개수이며, 설치 시 변수로 전달됩니다.
+
7) safety_line_setting
등거리선 거리 설정 변수로, 선박의 안전거리를 조정하는 데 사용됩니다.
+
8) selected_sideview
현시할 뷰를 의미하며, 인덱스로 설정된 개별 사이드뷰를 선택한다.
+
9) input_channel_list
입력 채널 목록이며, 각 카메라 스트림에서 캡처된 이미지가 들어오는 Redis key를 포함한다.
+
10) topview_shape
서라운드뷰 설정 정보로, 높이, 너비, 스케일, 전후 경사(front/back slope) 등의 값을 포함한다.
+
11) draught
선박의 흘수 정보를 나타내며, 이에 따라 서라운드뷰의 높이를 조정한다.
+
12) sideview_shape
사이드뷰 설정 정보로, 높이, 너비, 스케일 등을 포함한다.
+
13) azimuth_mode
선박의 방위각 모드를 설정하는 변수이며, NORTH_UP, COURSE_UP, HEAD_UP 등을 지원한다.
+
14) collision_risk_mode
충돌 위험 모드를 설정하는 변수로, 해당 기능의 사용 여부를 판단한다.
+
15) prediction_vector_length
예측 벡터의 길이를 설정하는 변수이다.
+
16) target_range_status_setting
목표 범위 상태 설정 변수이다.
+
17) target_range_setting
AIS 뷰에서 목표 범위를 설정하는 변수이며, scale, interval, ring_count 항목으로 구성됩니다.
+
18) inference_status_setting
개별 뷰에서 인퍼런스 추론 기능의 사용 여부를 판단한다.
+
19) ccrp
레이더, GPS, AIS, 속도계 등 모든 수평 측정 센서가 참조하는 공통 기준점(CCRP)을 설정하는 데이터이다.


===== 실행 조건

- Init Container는 메인 애플리케이션 컨테이너보다 먼저 실행되어야 한다.
- Storage Module(Redis)에 설정 키가 존재하지 않을 경우에만 기본값을 저장한다.
- 시스템이 최초 기동되거나, 설정 초기화가 수행된 경우에 실행됩니다.
- Init Container는 다음 조건을 만족해야 한다:
+
. Storage Module에 연결할 수 있어야 함
. 설정값을 쓰기 가능한 권한을 보유해야 함
+
- 기본값은 SVM 시스템 버전에 따라 관리되며, 버전 간 호환성을 유지해야 한다.
- Init Container 실행 후, 메인 컨테이너는 초기 설정값이 존재한다는 전제 하에 작동한다.

===== 검증 기준

- Init Container가 메인 컨테이너보다 먼저 실행되었는지 확인할 것
- Storage Module에 각 설정 키가 존재하지 않을 경우, 사전 정의된 기본값이 저장되었는지 확인할 것
- 초기화된 각 설정 값이 아래 기준에 부합하는지 검증할 것:
+
. `default_camera`, `camera_models`, `camera_count` 등은 설정된 시스템 환경과 일치해야 함
. `status_code`는 시스템 정상 상태를 의미하는 값으로 초기화되어야 함
. `topview_shape`, `sideview_shape` 등은 수치 값이 사양 범위 내에 있어야 함
. `azimuth_mode`, `collision_risk_mode` 등 선택 항목은 지원하는 enum 값 중 하나여야 함
. `input_channel_list`, `inference_status_setting` 등은 항목 개수 및 형식이 유효해야 함
+
- 설정값 저장 이후, 메인 컨테이너가 해당 설정을 참조하여 오류 없이 기동되는지 확인할 것
- 동일 조건에서 여러 번 기동 시에도 설정 값이 중복 저장되지 않는지 확인할 것

===== 성능 요구사항
N/A

===== 인터페이스 요구사항

Init Container는 다음 외부 구성 요소와 인터페이스해야 한다:
+
. **Storage Module (Redis)**
.. Redis 서버 주소 및 인증 정보는 환경 변수로 제공됩니다.
.. Key-Value 형태로 초기 설정값을 저장함
.. 저장 시 기존 값이 없을 경우에만 저장하도록 해야 함
+
. **Configuration Source (json file, dict)**
.. 초기값 정의를 위한 설정 세트는 json file 또는 프로그램 내부 dict 형태로 제공됨
.. 필수 설정 항목이 누락된 경우, Init Container는 실패 상태로 종료되어야 함
+
- 인터페이스 오류 발생 시, Init Container는 실패 상태로 종료되며, 전체 파드의 실행을 중단해야 한다.

===== 기타 요구사항

- Init Container는 경량 이미지로 구성되어야 하며, 실행 시간이 10초를 초과하지 않아야 한다.
- 설정값은 시스템 재기동 시에도 유지되어야 한다.
- 모든 설정 항목은 향후 확장을 고려하여 유연한 Key-Value 구조로 구성되어야 한다.
- Init Container에서 사용되는 기본값 목록은 버전별로 관리되어야 하며, 버전 간 호환성을 유지할 수 있도록 설계되어야 한다.
- 보안을 위해 Storage Module(Redis) 인증이 반드시 적용되어야 하며, 인증 정보는 Secret을 통해 주입되어야 한다.