==== Feature Toggle
===== 설명
Feature Toggle 기능은 시스템에서 활성화된 기능 목록을 프론트엔드에 제공하여, 사용자 인터페이스가 해당 설정에 따라 동적으로 구성될 수 있도록 지원합니다.

본 API는 SVM 시스템의 기능별 활성화 여부를 조회하는 용도로 사용되며, 기능별 개발 또는 배포 상태에 따라 활성화 여부가 변경될 수 있습니다.

Feature Toggle의 경우 Helm 차트를 통해서, ENV로 값을 넣어주는 형태로 구성되며, GitOps를 통해서 운영하고 있기 때문에, Helm 차트의 Values.yaml 파일에서 설정된 값에 따라 기능이 활성화 또는 비활성화할 수 있습니다.

[source,yaml]
----
featureToggle:
  aisView: true
  auth: true
  cctv: true
  debug: true
  inference: true
  sideView: true
  surroundView: true
----

*Feature 조회 API*

- 기능 설명 : 현재 저장된 feature 정보를 조회하여, frontend에서 사용한다.
이 정보에서 true 세팅된 정보의 기능만 사용가능하도록 화면이 재구성됩니다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/feature
- Request : 없음
- Response :
+
[source,json]
-----
{
    "code": 200,
    "message": "Success",
    "data": [
        {
            "id": "surround_view",
            "enabled": true
        },
        {
            "id": "side_view",
            "enabled": true
        },
        {
            "id": "ais_view",
            "enabled": true
        },
        {
            "id": "debug",
            "enabled": true
        },
        {
            "id": "inference",
            "enabled": true
        },
        {
            "id": "cctv",
            "enabled": false
        },
        {
            "id": "auth",
            "enabled": false
        }
    ]
}
-----
- 응답 필드 설명:
+
code : 응답 코드 (200)
+
message : 응답 메시지 (Success)
+
data : feature 정보 리스트
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| id | string | 기능 ID (예: surround_view, side_view 등)
| enabled | boolean | 기능 활성화 여부 (true: 활성화, false: 비활성화)
|===




===== 실행 조건
- 요청 시 별도의 인증 및 파라미터는 필요하지 않으며, 시스템이 정상적으로 기동되어 있어야 한다.
- Redis 또는 ENV 설정에서 feature toggle 정보를 정상적으로 로딩할 수 있어야 한다.


===== 검증 기준

- 응답 형식은 명세된 JSON 구조를 정확히 따라야 하며, 모든 설정된 기능에 대한 항목이 포함되어야 한다.
- 실제 Helm 설정(`values.yaml`)과 API 응답 결과 간의 일치 여부를 검증한다.
- enabled가 `true`인 항목만 프론트엔드에서 활성화되고 있는지 확인한다.
- 비정상적인 환경 변수 설정 시(예: 잘못된 key, boolean이 아닌 값), 서버는 이를 무시하고 기본값을 적용하거나 명확한 에러 로그를 남겨야 한다.
- 아래 `featureToggle` 구성의 **모든 조합**에 대해 API 응답 및 동작 여부를 검증해야 한다:

[source,yaml]
----
featureToggle:
  aisView: true | false
  auth: true | false
  cctv: true | false
  debug: true | false
  inference: true | false
  sideView: true | false
  surroundView: true | false
----

- 총 `2⁷ = 128`개의 조합에 대해 시스템이 정확하게 feature toggle 상태를 반영하는지 단위 테스트 또는 시나리오 테스트로 검증해야 한다.
- 조합별 테스트는 다음을 포함해야 함:
  * Helm 설정값과 API 응답값의 일치 여부
  * true/false 상태에 따라 프론트엔드에서 기능 화면이 정상적으로 활성/비활성화되는지 확인
  * 비활성화된 기능 요청 시 적절한 예외처리 또는 숨김 처리


===== 성능 요구사항
- API 응답 시간은 100ms 이내를 유지해야 한다.

===== 인터페이스 요구사항

- HTTP 상태 코드는 200(성공), 500(서버 오류) 등 명확한 상태 코드를 반환해야 한다.
- 응답은 공통 응답 스펙(code, message, data)에 맞춰야 하며, 데이터 타입은 명확히 지정되어야 한다.
- RESTful 표준을 따라야 하며, GET 메서드를 사용해야 한다.


===== 기타 요구사항

- 테스트 환경에서는 모든 기능이 활성화된 상태로 운영되며, 운영 환경에서는 기능별 배포 전략에 따라 제어됩니다.