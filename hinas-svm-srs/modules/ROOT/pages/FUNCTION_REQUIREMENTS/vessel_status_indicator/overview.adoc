=== Vessel Status Indicator +



==== Overview
Vessel Status Indicator에서는 자선 의 현재 센서 정보 및 항해 상태를 실시간으로 시각적으로 표시하는 기능을 제공합니다.

이를 이해하기 위해서는 VDR에서 들어오는 데이터가 SVM 화면상에 현시되기 까지의 과정을 이해해야 합니다. 이 과정은 크게 다음과 같이 구성됩니다:

.SVM Vessel Status Indicator Flow Chart
image::FUNCTION_REQUIREMENTS/vessel_status_indicator/svm_nmea_data_flow_chart.png[align="center",800]
VDR(Voyage Data Recorder)은 항해 관련 데이터를 UDP 또는 TCP 프로토콜을 통해 서버의 `6501` 포트로 전송합니다.

해당 데이터는 K3S 환경 내 Sensor Data Proxy에서 수신되며, 이후 NMEA Parser로 전달됩니다.

*NMEA Parser Proto는 수신된 데이터를 파싱하여 Core Redis에 저장합니다.

- AIS(Vessel) 데이터는 `nmea:vdm` 키에 저장됩니다.
- 레이더(Radar) 데이터는 `nmea:ttm` 키에 저장됩니다.

자선(Own Ship)과 타선(Target Ship)의 데이터 처리 흐름은 다음과 같이 구분됩니다:

- **타선**: `nmea:vdm` 및 `nmea:ttm` 데이터를 NMEA Parser와 Filter를 통해 처리합니다. (`nmea:vdm` 형식은 NMEA Proto와 호환되지 않기 때문에, 별도 구조를 유지하여 처리하고 있습니다.)
- **자선**: `nmea:own` 형식의 데이터를 NMEA Proto를 통해 직접 처리합니다.


Core Redis에 저장된 데이터는 다음과 같은 방식으로 후속 처리됩니다.

- Sensor Fusion 모듈은 Redis로부터 `nmea:vdm`, `nmea:ttm` 등의 타선 데이터를 가져오고,
- `nmea:own` 자선 데이터와 결합하여,
- 각 선박의 위치와 이동 경로를 계산 및 추적합니다.

결과는 `svm:target`, `svm:lost_target` 키로 SVM Redis에 저장됩니다.

최종적으로, SVM Backend는 해당 데이터를 SVM Redis에서 읽어와 WebSocket을 통해 SVM Frontend에 전달하며,
이를 기반으로 화면에 실시간 상태가 시각화됩니다.

===== Terminology
이 섹션에서는 본 챕터에서 반복적으로 사용되는 주요 구성 요소 및 용어를 간략히 정의합니다.

* **VDR (Voyage Data Recorder)**
  선박의 항해 및 센서 데이터를 기록하고 전송하는 장치입니다.

* **NMEA Parser**
  VDR로부터 수신한 NMEA 0183 문자열을 파싱하여 구조화된 데이터로 변환하는 모듈입니다.

* **Core Redis**
  파싱된 센서 데이터(AIS, ARPA, 자선)를 저장하는 Redis 인스턴스입니다.

* **Sensor Fusion**
  자선 및 타선 데이터를 통합 분석하여 위치, 속도, 예측 정보를 생성하는 처리 모듈입니다.

* **SVM Redis**
  Sensor Fusion 결과를 저장하는 Redis로, 화면 표시용 정제 데이터를 포함합니다.

* **SVM Backend**
  Redis 데이터를 주기적으로 읽어 WebSocket으로 Frontend에 전송하는 서버 측 구성 요소입니다.

* **SVM Frontend**
  Backend에서 전달받은 데이터를 기반으로 자선 상태 정보를 UI로 표시하는 화면 구성 요소입니다.

* **STW (Speed Through Water)**
  자선이 물을 기준으로 이동하는 속도입니다.

* **SOG (Speed Over Ground)**
  자선이 지면 기준으로 실제 이동한 속도입니다.

* **COG (Course Over Ground)**
  자선이 지면 기준으로 진행한 방향입니다.

* **HDG (Heading)**
  자선의 실제 선수 방향입니다.

* **Wind Speed / Direction**
  풍속계로 측정한 풍속과 풍향 정보입니다.

* **WebSocket**
  실시간 데이터 전송을 위한 클라이언트–서버 간 양방향 통신 프로토콜입니다.

===== Table of Contents
[cols="1,1,4", options="header"]
|===
| No.
| Function
| Description

| 1
| vessel status indicator UI
| 자선의 현재 센서 정보 등의 실시간 정보를 시각적으로 표시하는 UI를 제공한다.

| 2
| vessel status streaming 
| 자선의 현재 항해 상태, 센서 정보, 전력 상태 등의 실시간 정보를 시각적으로 표시하는 기능을 제공한다.
|===

본 문서는 HiNAS SVM의 Vessel Status Indicator 기능에 대한 요구사항을 정의합니다.
본 챕터에서는 다음 내용을 순차적으로 설명합니다.


1. vessel status indicator UI

2. vessel status streaming

1장에서는 자선의 현재 센서 정보 등 실시간 데이터를 시각적으로 표시하는 사용자 인터페이스(UI) 기능을 설명합니다.
2장에서는 자선의 항해 상태, 센서 정보, 전력 상태 등의 실시간 정보를 VDR로부터 수신한 후, Sensor Fusion 및 SVM Backend를 거쳐 화면에 표시하는 전체 처리 과정을 설명합니다.



===== Vessel Status Indicator 범위 및 의존성
본 기능은 자선의 항해 정보(STW, SOG, COG, HDG) 및 기상 정보(풍속, 풍향)를 실시간으로 수신하고, 이를 시각적으로 표시하는 사용자 인터페이스(UI) 구성 요소이다. 해당 기능은 다음과 같은 시스템 컴포넌트로 구성된다:

*Core Redis*

- VDR(Vessel Data Recorder)로부터 UDP 6501 포트로 수신된 센서 데이터를 NMEA 프로토콜 기반으로 파싱하여,
  자선 및 AIS, ARPA 관련 정보를 Core Redis에 저장한다.

*Sensor Fusion*

- Core Redis로부터 자선의 현재 위치(Latitude, Longitude) 및 예측 위치 정보를 수신한 후,
  이를 정규화하고 예측 모델을 통해 가공된 데이터를 SVM Redis에 저장한다.

*SVM Redis*

- Sensor Fusion에서 정제된 자선의 현재 및 예측 위치 정보를 저장하는 Redis 인스턴스로,
  이후 UI 구성에 사용됩니다.

*SVM Backend*

- Core Redis 및 SVM Redis에서 데이터를 주기적으로 조회하고,
  이를 정규화한 뒤 WebSocket을 통해 클라이언트에 전달한다.

*SVM Frontend*

- SVM Backend로부터 수신한 데이터를 기반으로 자선 상태 인디케이터 UI를 구성하며,
  사용자가 항해 및 기상 상태를 직관적으로 인식할 수 있도록 시각화한다.
- 운항 모드(Surround View / Target Information Display)에 따라 표시 항목을 구분하며,
  관련 정보는 화면 좌측 상단에 고정되어 주기적으로 갱신됩니다.


자선의 항해 및 기상 정보는 VDR에서 NMEA 형식으로 UDP 6501 포트를 통해 실시간 전송됩니다. 해당 데이터는 Core Redis에 저장되며, Sensor Fusion 모듈은 이를 기반으로 자선의 현재 위치 및 예측 위치를 계산하여 정규화된 형태로 SVM Redis에 저장합니다. 이후 SVM Backend는 Core Redis와 SVM Redis로부터 데이터를 주기적으로 조회하고, 정제된 정보를 WebSocket을 통해 클라이언트로 전송합니다. 최종적으로 SVM Frontend는 이 데이터를 기반으로 자선 상태 인디케이터 UI를 구성하여, 운항자가 항해 및 기상 상태를 직관적으로 파악할 수 있도록 화면 좌측 상단에 고정 표기합니다. 표시 항목은 Surround View 모드와 Target Information Display 모드에 따라 구분되어 운영됩니다.

<<<
include::vessel_status_indicator_ui.adoc[]


<<<
include::vessel_status_streaming.adoc[]
