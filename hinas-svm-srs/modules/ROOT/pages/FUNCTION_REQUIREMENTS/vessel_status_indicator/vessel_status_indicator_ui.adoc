==== Vessel Status Indicator UI


===== 설명
자선 상태 인디케이터 UI는 자선의 항해 및 기상 상태를 화면에 시각적으로 표시하여, 운항 중인 선박의 실시간 정보를 사용자에게 제공하는 기능이다. Mode에 따라 두 가지 UI 형태로 나뉘어 사용되며, 상황에 따라 적절한 정보를 표시한다.

====== Surround View Mode

[cols="1,2,2,2", options="header"]
|===
| 항목 | 설명 | 사용 값 (Redis Key) | 표기 형식
| STW (Speed Through Water) | 자선의 수중속도(kn) | stw_speed | --.-
| Wind Direction (Th) | 상대풍 방향 (선체 기준, deg) | wind_direction | ---.-
| Wind Speed | 풍속(kn) | wind_speed | ---.-
|===

[[surround_view_vessel_status]]
.surround view vessel status UI
image::SYSTEM_OVERVIEW/system_overview_vessel_status_indicator_surround_view.png[align="center",600]

====== Target Information Display mode

[cols="1,2,2,2", options="header"]
|===
| 항목 | 설명 | 사용 값 (Redis Key) | 표기 형식
| HDG (Heading) | 자선의 침로(degree) | hdg | ---.-
| COG (Course Over Ground) | 자선의 지표 진행 방향(degree) | cog | ---.-
| SOG (Speed Over Ground) | 자선의 지표속도(kn) | sog | --.-
|===

[[target_information_display_view_vessel_status]]
.target information display view vessel status UI
image::SYSTEM_OVERVIEW/system_overview_vessel_status_indicator_target_infromation_indicator.png[align="center",600]

데이터가 전달되지않아 현시 할 수 없다면, "-"을 자리수에 맞춰 표기한다.



===== 실행 조건

자선 항해 정보 (HDG, COG, STW, SOG 등) 및 기상 정보 (풍속, 풍향)를 주기적으로 수신 가능해야 함

UI는 실시간으로 정보 반영을 위해 1초 이내 갱신 주기를 가져야 함

===== 검증 기준

*데이터 일치성*

- 표시되는 UI 항목이 websocket으로 수신한 실시간 값과 일치해야 함

*단위 표기*

- 풍속(kn), 풍향(degree), 속도(kn) 등 단위가 정확히 표시되어야 함

*뷰 모드별 항목 차등 적용*

- Surround View에서는 STW, 풍속, 풍향만, Target Info View에서는 HDG, COG, SOG 포함된 정보가 정확히 반영되어야 함

*위치 고정성*

- 화면 좌측 상단 고정 노출 유지

===== 성능 요구사항

*시스템 자원 사용*

- UI 갱신 시 CPU 점유율이 5% 이내 유지되어야 하며, 메모리 증가가 누적되지 않아야 함
- 다수의 UI 구성요소와 병렬로 구동되어도 프레임 드롭 또는 렌더링 지연 없어야 함

*데이터 지연 허용 한계*

- 수신된 Redis 기반 항해/기상 데이터는 100ms 이내 UI에 반영되어야 하며, VDR에서 들어온 데이터가 프론트엔드 렌더링 되기까지의 전체 지연 시간은 최대 2초 이내여야 함
- 시스템이 연속 4시간 이상 동작한 상태에서 다음과 같은 지연 통계 지표를 만족해야 함:

[cols="1,2", options="header"]
|===
| 측정 항목 | 요구 조건

| 평균 지연 시간(Mean Latency) | ≤ 500ms
| 95% 지연 백분위수(P95) | ≤ 1000ms
| 99% 지연 백분위수(P99) | ≤ 1500ms
| 최대 지연 시간(Max Latency) | ≤ 2000ms (초과 시 경고 로그 출력)
| 누적 지연 초과 횟수 (2초 이상) | 4시간 동안 0건 (단 1건이라도 존재하면 결함으로 간주)
| 스파이크 대응력 (Burst Handling) | 수신량이 순간적으로 2배 이상 급증하더라도 위 모든 지연 통계를 만족해야 함
|===



===== 인터페이스 요구사항

[cols="1,3", options="header"]
|===
| 인터페이스 | 설명

| Key: stw_speed | 수중속도 STW
| Key: sog | 지표속도 SOG
| Key: cog | 진행 방향 COG
| Key: hdg | 침로 HDG
| Key: wind_direction | 풍향
| Key: wind_speed | 풍속
|===

===== 기타 요구사항

*단위 통일성*

- 풍속(kn), 방향(degree) 등 단위 표기에 대한 일관성을 유지해야 함

*장애 대응*

- 데이터 수신 실패 시 -- 표기 등 fallback 제공

*UI 시인성*

- Vessel Status Indicator는 사용자가 최소 1m 거리에서 화면을 관찰할 때 명확히 인지할 수 있도록 설계되어야 합니다.
- 사용자가 선박 조타실 또는 운항 위치에서 일반적인 시야각(약 30~40도)으로 바라보는 조건에서도, Vessel Status Indicator의 모든 정보가 명확히 식별 가능해야 합니다.
- Vessel Status Indicator의 수치값은 색상 대비, 굵기등은 주간과 야간, 그리고 다양한 밝기 조건에서도 시인성을 유지해야 하며, 국제 해사
표준(예: IMO HMI 지침)을 준수하는 색상 대비가 확보되어야 합니다.