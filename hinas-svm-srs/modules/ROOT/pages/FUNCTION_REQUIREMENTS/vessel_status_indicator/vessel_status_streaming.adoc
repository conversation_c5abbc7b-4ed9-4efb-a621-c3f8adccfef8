==== Vessel Status Streaming


===== 설명
본 기능은 자선(Own Vessel)의 실시간 항해 및 위치 정보를 클라이언트에게 웹소켓을 통해 스트리밍하는 기능이다. 클라이언트가 `/svm/own-ship` 네임스페이스에 연결되면, 서버는 백그라운드 태스크를 시작하여 Redis에서 정보를 주기적으로 조회하고, 이를 가공한 뒤 `updated-own-ship-data` 이벤트를 통해 클라이언트에 전송한다.

데이터는 두 개의 Redis 인스턴스에서 각각 가져온다:

* Core Redis (`nmea:own`)  
  - 자선의 항해 센서 정보 (COG, HDG, SOG, STW, UTC, WindSpeed, WindDirection, WindReference, WindSpeedUnit 등)
  - 이 데이터가 없을 경우 `NoSignalError` 예외를 발생시키며, `no_signal_error` 이벤트로 클라이언트에 알림

* SVM Redis (`svm:own`)  
  - 자선의 현재 위치 정보 (Latitude, Longitude)
  - 자선의 예측 위치 정보 (prediction_position: latitude, longitude, distance, bearing)

조회된 정보는 다음과 같이 정규화된 스키마(`OwnShipSchema`)로 변환되어 전달된다:

[cols="1,2", options="header"]
|===
| 필드 | 설명

| cog | 자선의 진행 방향(Course Over Ground), 단위: 도(degree)
| hdg | 자선의 침로(Heading), 단위: 도(degree)
| stw_speed | 자선의 수속(STW, Speed Through Water), 단위: knots
| sog | 자선의 지표속도(SOG, Speed Over Ground), 단위: knots
| wind_speed | 이론풍속. 상대풍(R 기준)일 경우 변환 계산됨. 단위: knots
| wind_direction | 이론풍향. 상대풍(R 기준)일 경우 변환 계산됨. 단위: 도(degree)
| current_position | 자선의 현재 위도/경도 좌표 (`Latitude`, `Longitude`)
| prediction_position | 자선의 예측 위치 정보 (`latitude`, `longitude`, `bearing`, `distance`)
| issued_time | 데이터 기준 UTC 시각
|===

* 상대풍(`WindReference == "R"`)인 경우에는 침로(HDG), COG, SOG 값을 기반으로 상대풍 → 이론풍 변환 로직이 적용됩니다.
* 풍속 단위가 m/s일 경우 knots로 변환되며, 모든 풍속은 최종적으로 knots 단위로 제공됩니다.

정상적으로 데이터를 전송할 수 없을 경우, 다음과 같은 이벤트로 클라이언트에게 오류 상황을 알린다:

데이터 전송 주기는 0.2초이며, 클라이언트는 실시간으로 자선 상태 정보를 수신할 수 있다.

===== 실행 조건
1. svm redis, core redis가 정상 동작

===== 검증 기준

[cols="1,3", options="header"]
|===
| 항목 | 검증 기준

| 실시간 데이터 수신 | 클라이언트가 `/svm/own-ship` 네임스페이스에 연결된 이후 0.2초 간격으로 `updated-own-ship-data` 이벤트를 수신해야 한다.

| 데이터 정합성 | 전송되는 JSON 데이터는 `OwnShipSchema` 구조를 따르며, 필수 필드가 누락되지 않아야 한다.

| Redis 연결 정상 여부 | Core Redis(`nmea:own`)와 SVM Redis(`svm:own`)에 정상적으로 접근 가능한 경우에만 데이터 전송이 이루어져야 한다.

| 예외 응답 처리 | 다음 상황에 따라 지정된 이벤트가 전송되어야 한다:
- Core Redis에 데이터 없음 → `no_signal_error`
- Redis 또는 네트워크 오류 → `server_error`

| 풍속 단위 처리 | 입력 데이터가 m/s 단위인 경우, 정확하게 knots 단위로 변환되어야 한다.
|===


*무결성 검증*

- 전송 전 서버는 Schema 유효성 검사(validation)를 통과하지 못한 데이터는 송신하지 않아야 한다.

===== 성능 요구사항


*전송 주기 정확도*

- 0.2초 ±50ms 이내 간격으로 WebSocket 이벤트가 전송되어야 한다.


*연속 오류 대응*

- Redis 오류 또는 NoSignal 상태 발생 시에도 이벤트는 중단되지 않고 주기적으로 오류 상태가 전송되어야 한다.


*확장성 (Scalability)*

- 10개의 클라이언트가 동시에 /svm/own-ship에 접속하더라도
모든 클라이언트에 동일한 전송 품질(전송 간격, 지연 시간)을 보장해야 함


*CPU/메모리 자원 제한*

* 단일 WebSocket 세션 기준

** CPU 점유율 ≤ 3%

** 메모리 사용량 ≤ 50MB

* 10개 동시 세션 기준

** CPU 점유율 ≤ 20%

** 총 메모리 사용량 ≤ 400MB

**SVM Redis, Core Redis 데이터 정합성 검증**

- nmea:own(Core Redis)의 항해 정보와 svm:own(SVM Redis)의 위치 정보는 동일한 시점의 데이터로 간주될 수 있어야 한다.

- 시간 정합성 기준 : (ΔT = |T_core - T_svm|)

- 시스템이 연속 4시간 이상 운용된 상태에서, 다음 조건을 모두 만족해야 함.

[cols="1,2", options="header"]
|===
| ΔT 범위 | 요구 조건

| ΔT ≤ 100ms (완전 동기화 기준)
| 95% 이상의 데이터 쌍이 ΔT ≤ 100ms 조건을 만족해야 함

| ΔT ≤ 200ms (허용 가능한 정합 범위)
| 99% 이상의 데이터 쌍이 ΔT ≤ 200ms 조건을 만족해야 함

| ΔT > 300ms (정합성 오류)
| 0.1% 이하 (연속 5회 초과 시 오류 이벤트 발생)

| ΔT 측정 샘플 수
| 최소 72,000샘플 이상 (0.2초 간격 × 4시간) 기준으로 평가
|===

*상대풍 변환 처리 성능*

[cols="1,2", options="header"]
|===
| 항목 | 처리 시간 기준

| 단일 변환 처리 시간
| ≤ 5ms (상대풍 → 절대풍 계산 시간)

| 전체 처리 지연 (계산 + 스키마 구성 + 이벤트 직전 상태)
| ≤ 20ms
|===

===== 인터페이스 요구사항

**WebSocket Client → `/svm/own-ship`**

- 클라이언트는 해당 네임스페이스로 소켓 연결을 수행한다.


*Redis Key: `nmea:own`*

- 항해 정보 (COG, HDG, SOG, STW, 풍향, 풍속, WindReference 등)를 포함한 Core Redis 키


*Redis Key: `svm:own`*

- 자선의 현재 위치와 예측 위치를 포함하는 SVM Redis 키

===== 기타 요구사항

*상대풍 변환*

- `WindReference`가 "R"인 경우, 침로(HDG), 지표속도(SOG), 진행방향(COG)을 기반으로 상대풍을 절대풍으로 변환해야 한다.

*단위 통일*

- 모든 풍속 데이터는 최종적으로 knots 단위로 통일되어야 한다. 입력이 m/s인 경우 적절히 변환되어야 한다.

*연결 유지 상태 확인*

- 서버는 SVM Redis, Core Redis 연결에 대해 유지상태를 체그하고,  연결이 끊어졌는지 여부를 실시간 감지해야 함
- 소켓 연결 유지 상태는 서버가 관리하며, 클라이언트가 연결을 종료하지 않는 한 데이터 전송이 지속되어야 한다.