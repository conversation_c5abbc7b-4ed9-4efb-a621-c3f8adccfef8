==== Track Control System

===== 설명

Track Control System은 IEC 62065 표준에 따라 선박을 사전 계획된 항로를 따라 자동으로 제어하는 시스템입니다. 이 시스템은 바람, 조류 및 기타 영향을 보상하기 위해 Cross-Track Error를 기반으로 수정을 수행하며, 단순히 목적지 waypoint로의 방위각만을 기준으로 하지 않습니다.

Track Control System의 주요 기능:
* 단일 waypoint 또는 연속된 waypoint로 구성된 항로를 따라 선박 조종
* Rhumb line 또는 Great circle sailing 지원
* Cross-Track Error 기반 항로 수정
* 선박의 조종 특성에 맞는 자동 또는 수동 조정
* 다양한 기상, 속도, 적재 조건에서의 적응

지원되는 Track Control System 카테고리:
* Category C: 직선 구간 및 회전에서의 완전한 track control를 만족해야함

===== 실행조건

Track Control System의 정상 동작을 위한 실행 조건은 다음과 같습니다:

====== 필수 조건 (IEC 62065 5.1.1.2, 5.1.1.3 기준)

* *Primary Position-fixing System*: IMO 승인 전자 위치 측정 시스템(EPFS)이 정상 동작해야 합니다
* *Sensor Data Validity*: 필요한 위치, 방향, 속도 센서 데이터가 유효하고 선택되어야 합니다
* *Track Validation*: 사전 계획된 항로가 타당성 및 기하학적/선박 의존적 한계의 정확성에 대해 검증되어야 합니다
* *Starting Requirements*: 다음 중 하나의 조건을 만족해야 합니다:
  - TO-waypoint 또는 사전 계획된 항로의 leg 선택 및 TO-waypoint로의 방위각과 실제 방향 간 최대 허용 차이 선택
  - 사전 계획된 항로로 이동하기 위한 항로 정의 (선박 조종 특성 만족 필요)

* *Position Monitoring (5.1.1.4)*: 선박 위치를 제2 또는 추가 독립 위치 소스로 연속 모니터링

====== 핵심 기능 요구사항 (IEC 62065 5.1.1 기준)

* *Course Change Alert Capability (5.1.1.5, 5.1.1.6)*: Waypoint 연속 항로에서 침로 변경 경보 기능 제공

[NOTE]
====
**Course Change Alert 상세 조건 참조**

Early Course Change Warning (ECCW) 및 Actual Course Change Warning (ACCW)의 구체적인 발생 조건, 타이밍, escalation 절차에 대해서는 본 문서의 "Course Change Alert 조건" 섹션을 참고하시기 바랍니다.
====

* *Waypoint Modification Restriction (5.1.1.7)*: Track control 모드에서 활성 항로의 TO-waypoint, FROM-waypoint, NEXT-waypoint 및 관련 데이터 수정 불가 (새 항로 생성 및 시작 요구사항 만족 시까지)
* *Turn Performance (5.1.1.8)*: 설정된 radius 또는 preset rate of turn 기반 radius 내에서 회전 수행, 선박 회전 능력 내에서 동작
* *Adaptation Capability (5.1.1.9)*: 다양한 기상, 속도, 적재 조건에서 선박의 조종 특성에 대한 수동 또는 자동 조정 가능
* *Tolerance Management (5.1.1.10)*: 정상적인 yaw/sway motion, 센서 데이터 해상도, 통계적 위치 오차로 인한 불필요한 러더 동작 방지
* *Override Function (5.1.1.11)*: Override facilities 신호를 받아 track control 모드 종료 및 override facilities로 전환
* *Heading Control Mode (5.1.1.12)*: Track control 시스템이 heading control 모드로 운영 가능 (포함된 경우)
* *Manual Changeover (5.1.1.13)*: 모든 러더 각도 및 모든 조건에서 수동 조타로 전환 가능
* *Heading Control Changeover (5.1.1.14)*: 정상 운영 조건에서 heading control로 전환 가능 (시스템 포함 시)

[NOTE]
====
**Manual Override 상세 요구사항 참조**

Override Function, Manual Changeover의 구체적인 구현 방법, 사용자 인터페이스, 테스트 절차 등에 대해서는 별도의 Manual Override 기능 요구사항 문서를 참고하시기 바랍니다.
====
* *Steering Mode Indication (5.1.1.15)*: 현재 운영 중인 조타 방법에 대한 적절한 표시 제공

====== 선박 운항 조건

* *속도 범위*: 최소 조종 속도에서 30노트까지
* *회전율 제한*: 최대 회전율 10°/s 이하
* *조종성 요구사항*: 선박의 조종 특성이 계획된 항로의 기하학적 요구사항을 만족해야 함

====== 환경 조건

* *센서 정확도*: 위치, 방향, 속도 센서의 정확도가 요구 기준을 만족해야 함
* *통신 상태*: 외부 시스템(Autopilot, BMS 등)과의 통신이 정상이어야 함
* *전원 공급*: 시스템 안전 운영에 필요한 전원이 정상 공급되어야 함

====== Alert 발생 조건 (IEC 62065 5.1.3 기준)

Track Control System은 다음과 같은 경보 조건들을 모니터링하고 발생시켜야 합니다:

* *Early Course Change Warning (ECCW)*: WOT 6분 전~3분 전 warning 발생
  - 30초 내 미확인 시 WOT 2분 30초 전에 Early Course Change Alarm (ECCA)으로 변경
* *Actual Course Change Warning (ACCW)*: WOT 30초 전 warning 발생
  - 30초 내 미확인/미승인 시 Actual Course Change Alarm (ACCA)으로 변경
  - WOT 후 30초 내 미확인/미승인 시 Back-up Navigator Alarm 발생
  - 확인/승인 여부와 관계없이 선박은 자동으로 항로를 따라야 함

* *Power Supply Alert (5.1.3.1)*: 안전 운영에 영향을 주는 전원 공급 장애 또는 감소 시 warning 발생
* *Position Monitoring Alert (5.1.3.2)*: 주 위치와 보조 위치 간 편차가 사전 설정 한계 초과 시 warning 발생
* *Heading Monitoring Alert (5.1.3.3)*: 방향 모니터링에서 편차가 사전 설정 한계 초과 시 warning 발생
* *Sensor Failure Alert (5.1.3.4)*: 위치, 방향, 속도 센서의 장애 또는 경보 상태 시
  - Track control 성능 저하 시 적절한 우선순위 경보 발생
  - 안전한 조타 모드에 대한 사용자 안내 자동 제공
  - 30초 내 미확인 시 Back-up Navigator Alarm 발생
* *Faulty Signal Prevention (5.1.3.5)*: 장애 또는 alarm 우선순위 경보 상태로 태그된 센서 신호 선택 불가
* *Cross-track Alert (5.1.3.6)*: 실제 위치가 항로에서 사전 설정 cross-track 한계를 초과 시 alarm 발생
* *Course Difference Alert (5.1.3.7)*: 실제 방향이 항로 침로에서 사전 설정값을 초과 시 warning 발생
* *Low Speed Alert (5.1.3.8)*: 전후방향 대수속도가 track control 최소 조종 속도 미만 시 warning 발생
* *End of Track Alert (5.1.3.9)*: 마지막 waypoint 통과 3-6분 전 warning 발생, 30초 내 미확인 시 alarm으로 변경
* *Track Control Stopped Alert (5.1.3.10)*: 시스템이 자동으로 heading control로 전환하거나 운영 불가 시 warning 발생, 30초 내 미확인 시 alarm으로 변경


[NOTE]
====
Alert 시스템의 상세한 우선순위, Back-up Navigator Alarm (NA) 처리, 원격 확인 등에 대해서는
별도의 Alert Management 기능 요구사항 문서를 참고하시기 바랍니다.
====

===== 검증기준

Track Control System의 검증 기준은 IEC 62065 Chapter 6 테스트 요구사항을 기반으로 합니다:

====== 기능 검증 (IEC 62065 6.4.3 기준)

* *Track Following Accuracy*: 선박의 실제 침로와 위치가 사전 계획된 항로에서 설정된 한계를 초과하지 않아야 함
  - Cross-track limit: Scenario별 설정값 (35m/60m/100m/200m) 이내 유지
* *Waypoint Transition*: TO-waypoint 통과 시 정확한 다음 waypoint로의 자동 전환
* *Turn Performance*: 설정된 radius 또는 rate of turn에 따른 정확한 침로 변경 수행
  - ROT ±10% 달성 후 steady turn 완료까지 RMS rudder < 20% of maximum
* *Track Display*: 그래픽 화면에서 선박이 항로를 따라 운항하는 모습이 정확히 표시
* *Information Display*: 활성화된 항로 참조 및 leg별 자동 전환 정보가 정확히 표시

====== 안전성 검증 (IEC 62065 6.4.6 기준)

* *Manual Changeover*: 모든 러더 각도 및 모든 조건에서 수동 조타로의 전환 가능
  - 단일 조작으로 전환 가능 (시스템에 포함된 경우)
  - Track control 시스템 장애 상황에서도 전환 가능
* *Override Function*: Override facilities 신호에 대한 즉시 반응 (5초 이내)
* *Fall-back Operation*: 주요 센서 장애 시 적절한 fall-back 방안 자동 동작
* *Alert Generation*: 시스템 이상 상황 시 적절한 우선순위의 경보 발생

====== 성능 검증 (IEC 62065 6.4.4 기준)

* *Adaptation Capability*: 다양한 조건에서의 자동/수동 적응 능력
  - 속도 변화 적응 (10kn ↔ 20kn 변화)
  - 조류 변화 적응 (5kn 수직/북향 조류)
  - 해상 상태 적응 (Sea state 2 ↔ 5 변화)
  - 적재 조건 적응 (제조사 문서 확인)
* *Rudder Activity Control*: 불필요한 러더 동작 방지
  - 주기적 yaw motion (0.1Hz, ±4°) 대응
  - 센서 해상도 저하 대응 (heading 1/6°, position 1/100 min)
  - 통계적 위치 오차 대응
* *Steering Bias Compensation*: 선박 조타 편향 보상 능력 (+2° rudder offset 보상)

===== 성능 요구사항

====== 정확도 요구사항 (IEC 62065 Scenario 기준)

* *Cross-Track Limit*: 시나리오별 설정 한계 내 유지
  - Scenario 1 (Ship A, 20kn): 35m 이내
  - Scenario 2 (Ship C): 100m 이내
  - Scenario 3 (Ship B): 60m 이내
  - Scenario 4 (Ship B, Long term): 200m 이내 (일반), 50m 이내 (RL), 1000m 이내 (GC)
* *Course Difference Limit*: 시나리오별 설정 한계 내 유지
  - Scenario 4 RL: 25° 이내
  - Scenario 4 GC: 제조사 지정 최대값
* *RPM 정확도*: Along-track speed control 시 계획된 RPM와의 편차 0.1RPM 이내
* *Turn Performance*: ROT ±10% 달성 후 RMS rudder < 20% of maximum rudder

====== 응답 성능

* *Override 응답*: Override 신호에 대한 응답 (5초 이내)
* *Manual Changeover*: 단일 조작으로 수동 전환 (시스템 포함 시)
* *Alert Response*: 경보 발생 시간 (이상 상황 감지 후 3초 이내)
* *Mode Transition*: 제어 모드 전환 응답 시간

====== 운영 성능 (IEC 62065 6.4.3, 6.4.4 기준)

* *Long Term Operation*: Scenario 4에서 4시간 이상 연속 운영
* *Multi-waypoint Navigation*: 연속된 waypoint를 통한 복잡한 항로 수행
* *Rhumb Line/Great Circle*: RL 및 GC 항해 모드 지원
* *Ship Model Compatibility*: Ship A/B/C 모델에서의 정상 동작
* *Environmental Tolerance*: 다양한 해상 및 기상 조건에서의 성능 유지
  - Speed Change Adaptation: 회전 중 속도 변화 (10kn ↔ 20kn) 적응
  - Current Adaptation: 조류 변화 (최대 수직 5kn) 적응
  - Sea State Adaptation: 해상 상태 변화 (Sea state 2, 5) 적응
  - Loading Condition Adaptation: 다양한 적재 조건에서의 적응

===== 인터페이스 요구사항

====== 센서 인터페이스 (IEC 62065 5.4.1, 5.4.2 기준)

* *Position Interface*: IEC 61162-1 표준에 따른 EPFS 인터페이스
* *Heading Interface*: 자이로컴퍼스 또는 자기컴퍼스와의 표준 인터페이스
* *Speed Interface*: SOG 및 STW 센서와의 인터페이스
* *Secondary Position*: 보조 위치 소스와의 인터페이스
* *Status Information*: 모든 연결된 센서의 상태 및 장애 정보 제공 능력
* *Plausibility Check*: 상태/장애 정보를 제공하지 않는 센서에 대한 타당성 검사 수행

====== 제어 시스템 인터페이스

* *Autopilot Interface*: IEC 61162-1 HTC/HTD 문장을 통한 autopilot 제어
* *BMS Interface*: 속도 제어를 위한 Bridge Management System 인터페이스
* *Override Interface*: Override facilities와의 인터페이스
* *Manual Steering*: 수동 조타 시스템과의 인터페이스

====== 사용자 인터페이스 (IEC 62065 5.2 기준)

* *Operational Controls (5.2.1.1)*: 연속 waypoint 간 침로 수용/계산, radius/rate of turn 조정, 사용자 설정 가능한 track control 관련 한계값, 경보 한계값, 기능 및 기타 제어 매개변수 조정 수단 제공
* *Changeover Controls (5.2.1.2)*: 조타 모드 선택 스위치 또는 override facility는 주 조종 위치 또는 그 근처에 위치, 단일 운항자 조작으로 전환 가능
* *Information Display (*******)*: 주 조종 위치 근처에서 다음 정보를 명확하고 지속적으로 표시
  - 조타 모드
  - 실제 선택된 위치, 방향, 속도의 소스
  - 선택된 방향, 속도, 위치 센서의 상태 및 장애 및 관련 모니터링 기능
  - Track course 및 실제 heading (수치 표시)
  - 실제 위치, cross-track distance (방향 포함) 및 대지속도 (수치 표시)
  - TO-waypoint 및 NEXT-waypoint
  - TO-waypoint까지의 시간 및 거리 (수치 표시, wheel-over 기준)
  - 다음 track course (수치 표시)
  - 선택된 항로 식별
  - 모든 표시 정보는 IEC 62288 디스플레이 일반 요구사항 준수
* *Steering Mode Indication (*******)*: Track control 시스템의 모든 작업 스테이션에서 조타 모드 표시
* *Alert Display*: 경보 및 경고 메시지 표시

====== 데이터 인터페이스

* *Track Data*: Waypoint 및 항로 데이터 입력 인터페이스
* *Ship Data*: 선박 제원 및 조종 특성 데이터 인터페이스
* *Configuration Data*: 시스템 설정 및 한계값 데이터 인터페이스
* *Log Data*: 운항 기록 및 시스템 로그 데이터 출력

===== 기타 요구사항

====== 표준 준수

* *IEC 62065 (Ed 2.0)*: Track control systems 운영 및 성능 요구사항 준수
* *IMO MSC.74(69) Annex 2*: Track Control Systems 성능 표준 준수
* *IEC 61162-1*: Digital interfaces 표준 준수
* *IMO MSC.302(87)*: Bridge Alert Management 표준 준수

====== 안전 요구사항 (IEC 62065 5.5 Fall-back arrangements 기준)

* *Track Control Failure (5.5.1)*: Track control 장애 시 'track control stopped warning' 발생, 오토파일럿 heading control 자동 전환
  - 직선 구간: 실제 heading을 마지막 heading으로 설정
  - 곡선 구간: 실제 COG가 대략 유지되도록 곡선 구간 종료 heading으로 설정
* *Position Sensor Failure (5.5.2)*: 위치 센서 장애 또는 불능 상태 시 **track control 중단**
  - 선택된 위치 센서 연결 해제 또는 무효 정의 시 track control 불가
  - 대체 위치 센서로 자동 전환 (가능한 경우)
  - 위치 센서 없이는 track control 운영 불가
* *Heading System Failure (5.5.3)*: 방향 측정 시스템 장애 또는 불능 상태 시 **track control 중단**
  - 선택된 heading 센서 연결 해제 또는 무효 정의 시 track control 불가
  - 대체 heading 센서로 자동 전환 (가능한 경우)
  - Heading 센서 없이는 track control 운영 불가
* *Speed Sensor Failure (5.5.4)*: 속도 센서 장애 시 **성능 저하 경고 발생**
  - 선택된 속도 센서 연결 해제 또는 무효 정의 시 low performance warning 발생
  - Track control 운영은 계속 가능하나 성능 저하
  - 대체 속도 센서로 자동 전환 (가능한 경우)
* *Sensor Selection*: 장애 또는 alarm 우선순위 경보 상태로 태그된 센서 신호는 선택 불가
* *Emergency Response*: 비상 상황 시 즉시 수동 제어로 전환

[NOTE]
====
Fall-back arrangements의 상세한 조건 및 절차에 대해서는 별도의 Operation Detail 기능 요구사항 문서를 참고하시기 바랍니다.
====

====== 환경 요구사항

* *Marine Environment*: 해상 환경에서의 동작 보장
* *Temperature Range*: 선박 운항 환경의 온도 범위에서 동작
* *Vibration Resistance*: 선박 진동 환경에서의 안정적 동작
* *EMC Compliance*: 전자기 적합성 기준 준수

====== 유지보수 요구사항

* *Error-Diagnosis*: 시스템 자가 진단 기능 (control System Setting page에서 error diagnosis )

====== 문서화 요구사항

* *Operation Manual*: Track control system 운영 매뉴얼
* *Setting Parameter Guide*: 설정 가이드


[NOTE]
====
**추가 상세 요구사항 참조**

* **Starting Condition 및 Failure Condition**: Track Control System의 상세한 시작 조건 및 장애 조건에 대해서는 별도의 Operation Detail 기능 요구사항 문서를 참고하시기 바랍니다.

* **Alert System**: Track Control System에서 발생하는 경보의 상세한 관리 방법, 우선순위, 사용자 인터페이스 등에 대해서는 별도의 Alert Management 기능 요구사항 문서를 참고하시기 바랍니다.
====
