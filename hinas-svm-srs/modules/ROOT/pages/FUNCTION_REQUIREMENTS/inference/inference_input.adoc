==== Inference Input

===== 설명
본 기능은 Inference 서비스에 필요한 입력 데이터(inference_input)를 Redis에 저장하는 기능이다. /inference/storage HTTP POST 요청을 통해 호출되며, 클라이언트 요청이 발생하면 다음과 같은 작업을 수행한다:

Redis에서 카메라 설정 정보(camera)와 탑뷰 설정 정보(topview_shape)를 조회한다.

조회된 정보를 기반으로 각 카메라에 대해 RTSP URL, 위치, 회전 정보, 내/외부 파라미터 등을 포함한 InferenceCamera 객체를 생성한다.

Surround 객체를 통해 Homography 행렬을 계산하여, 정규화 이미지 기준의 탑뷰/사이드뷰 및 월드좌표 변환용 행렬을 각 카메라에 적용한다.

모든 정보를 조합한 InferenceInput 객체를 Redis의 inference_input 키에 저장한다.

전송되는 InferenceInput은 다음 정보를 포함한다:

[cols="1,2", options="header"]
|===
| 필드 | 설명
| image_shape | 정규화된 Inference 이미지 해상도 (Topview 기준)
| draft | 사용되지 않음 (0으로 설정)
| camera_count | 사용되는 카메라 수
| camera | 각 카메라에 대한 상세 정보, InferenceCamera(RTSP, 위치, 회전, 내부/외부 파라미터, Homography 등)
| distortion_table | 미사용
| append_rtsp_url | RTSP URL 뒤에 붙는 카메라 프로바이더별 스트림 경로
| camera_model | 카메라 제조사 (arges)
| scale | Topview 스케일
|===

각 InferenceCamera 객체는 다음 정보를 포함한다:

[cols="1,2", options="header"]
|===
| 필드 | 설명
| cam_id | 카메라 고유 ID (index)
| camera_name | 카메라 이름 (예: bow, stern 등)
| position | 카메라의 상대 위치 좌표 (선체 기준)
| rotation | 카메라의 회전 각도
| rtsp_url | 카메라의 RTSP 스트림 주소 (rtsp://<id>:<password>@<camera_ip>:554/media/1/1/Profile1)
| homography_norm2top | 정규화 이미지에서 탑뷰로 변환하는 Homography 행렬
| homography_norm2side | 정규화 이미지에서 사이드뷰로 변환하는 Homography 행렬
| homography_world2side | 월드 좌표계에서 사이드뷰로 변환하는 Homography 행렬
| intrinsic | 카메라의 내부 파라미터 (fx, fy, cx, cy)
| distortion | 카메라의 왜곡 계수 (k1, k2, k3, k4)
|===

arges 카메라 기준 RTSP URL은 다음과 같이 구성된다:

[source,text]

rtsp://:@<camera_ip>:554/media/1/1/Profile1

포트는 554 고정이며, 스트림 경로는 /media/1/1/Profile1로 설정됨

===== 실행 조건

Redis에 camera 키로 유효한 카메라 설정 정보가 저장되어 있어야 함

Redis에 topview_shape 키가 존재하지 않을 경우, 기본값으로 입력됨

Surround 내부에 calibration 객체 및 관련 파라미터가 초기화되어 있어야 함

===== 검증 기준

[cols="1,3", options="header"]
|===
| 항목 | 검증 기준

| 카메라 정보 정합성 | Redis에 저장되는 각 카메라의 RTSP, Homography, Intrinsic, Distortion 정보가 실제 Redis 내 설정값과 일치해야 함
| Topview 정보 반영 | topview_shape의 height/width/scale 값이 정상적으로 반영되어야 함
| Homography 계산 | 각 카메라에 대해 Homography 계산이 오류 없이 수행되어야 함
| Redis 저장 성공 | 최종적으로 inference_input 키에 InferenceInput 객체가 JSON 형태로 저장되어야 함
|===

===== 성능 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항

| 처리 시간 | Redis 조회 및 계산을 포함하여 전체 작업은 10초 이내에 완료되어야 함
|===

===== 인터페이스 요구사항

[cols="1,3", options="header"]
|===
| 인터페이스 | 설명

| HTTP POST /inference/storage | Inference 입력 데이터를 Redis에 저장하는 API 엔드포인트
| Redis Key(Storage Redis): camera | 카메라 설정값 (RTSP, 위치, 회전 등)이 저장된 Redis 키
| Redis Key(Storage Redis): topview_shape | Topview 해상도 및 기타 설정값이 저장된 Redis 키
| Redis Key(Storage Redis): inference_input | 본 기능을 통해 저장되는 Inference 입력값의 최종 키
|===

===== 기타 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항

| 카메라 프로바이더 대응 | 카메라 제조사별 RTSP 경로가 정확하게 분기되어야 하며, 본 문서에서는 arges 기준으로 고정됨
| Surround 객체 사용 | Surround.calibration 설정값이 미리 초기화되어 있어야 하며, 내부에서 Homography 계산에 사용됨
| 예외 처리 | Redis에서 카메라 정보가 조회되지 않으면 작업 중단하고 저장하지 않아야 함
|===
