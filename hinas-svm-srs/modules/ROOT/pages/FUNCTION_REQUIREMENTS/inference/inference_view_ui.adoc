==== Inference View UI

===== 설명

Inference View UI는 자선 주변 영상에서 탐지된 객체(Ship, Motor Boat, Land)를 시각적으로 표시하여, 사용자가 위험 요소를 보다 직관적으로 식별할 수 있도록 지원하는 기능입니다. 본 기능은 UI 내 On/Off 버튼을 통해 사용자가 직접 활성화 여부를 제어할 수 있으며, 주요 특징은 다음과 같습니다.

.Inference View
image::SYSTEM_OVERVIEW/system_overview_inference.png[align="center",600]

Inference UI 는 하단의 Figma 링크에서 확인할 수 있습니다.

https://www.figma.com/design/KRxrOCqd2RUIt7WA8ouwPz/SVM-v1.0.0---24.-10.-31-?node-id=37-65119&m=dev[HiNAS SVM Inference Figma]

On/Off 버튼: Inference 기능의 활성화 여부를 제어할 수 있는 UI 요소입니다. 해당 버튼의 조작은 Surround View 및 Side View에 동시에 반영됩니다.

1) 표현 방식:

탐지된 객체가 선박 기준 가까운 위치일 경우, 객체의 윤곽이 자선 중심 기준의 호 형태로 시각화됩니다.

표시 대상 객체는 Ship, Motor Boat, Land로 한정됩니다.

2) 뷰별 현시 조건:

- Surround View: 회전, 확대/축소 상태에서도 항상 표시

- Side View: 화면 확대 상태에서만 표시

===== 실행 조건

- inference module로 부터 데이터를 정상 수신해야 합니다.

- Surround View 및 Side View 기능이 정상 동작 중이어야 합니다.

===== 검증 기준

*On/Off 제어 동작*

- Inference 토글 버튼 동작 시 각 View에 현시 여부가 정확히 반영되어야 합니다.


*객체 탐지 현시*

- Ship, Motor Boat, Land 객체가 탐지된 경우, 호 형태로 시각화되어야 합니다.


*View별 조건 만족*

- Surround View는 항상, Side View는 확대 상태에서만 탐지 결과가 표시되어야 합니다.


===== 성능 요구사항

*표시 반응 속도*

- 탐지 결과가 갱신된 후, UI에 반영되기까지의 시간은 500ms 이내여야 합니다.

*View 연동 처리*

- Inference 기능이 활성화될 경우, Surround View와 Side View에 동시에 적용되어야 합니다.

===== 인터페이스 요구사항

*Inference Toggle Button*

- 사용자가 Inference 기능을 On/Off할 수 있도록 제공되는 UI 요소입니다.

*객체 탐지 결과 입력*

- 탐지 대상의 위치 및 유형 정보가 입력됩니다.

===== 기타 요구사항


*탐지 대상 제한*

- 탐지 및 표시 대상은 Ship, Motor Boat, Land로 제한됩니다.

*비활성 시 숨김 처리*

- Inference 기능이 비활성화된 경우, 모든 탐지 관련 시각 요소는 화면에서 제거되어야 합니다.

*View 동기화*

- Inference 기능의 표시 여부는 Surround View 및 Side View 간 동기화되어야 합니다.

