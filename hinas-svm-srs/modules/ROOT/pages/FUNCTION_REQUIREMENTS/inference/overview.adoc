=== Inference
==== Overview
===== Terminology
이 섹션에서는 본 챕터에서 반복적으로 사용되는 주요 구성 요소 및 용어를 간략히 정의합니다.

- *Fisheye Camera* : 넓은 시야각 확보를 위한 광각 렌즈 카메라입니다. 영상 왜곡이 심하므로, Homography 변환 등을 통해 보정이 필수적입니다.

- *RTSP (Real-Time Streaming Protocol)*: 카메라로부터 실시간 영상을 수신하는 데 사용되는 스트리밍 프로토콜입니다.

- *Homography Matrix*: 2차원 평면 상의 점들 사이의 투영 관계를 표현하는 3×3 행렬입니다. Fisheye 영상에서 정규화된 Topview나 Sideview 좌표계로의 변환 등에 사용됩니다.

- *DeepStream SDK*: NVIDIA에서 제공하는 GPU 기반 실시간 영상 분석 프레임워크로, 복수 스트림 병렬 처리 및 객체 탐지 기능을 제공합니다.

- *Redis*: Inference Input, 추론 결과, UI 출력 데이터 등을 공유하는 고속 인메모리 데이터 저장소입니다.

- *WebSocket*: 클라이언트와 서버 간 실시간 양방향 통신을 지원하는 프로토콜로, Inference 결과 전송에 사용됩니다.

===== Table of Contents
[cols="1,1,4", options="header"]
|===
| No.
| Function
| Description

| 1
| Inference UI
| Inference View UI는 선박 주변에서 탐지된 객체를 시각적으로 표시하여 위험 요소를 직관적으로 식별할 수 있도록 지원하는 기능입니다.

사용자는 On/Off 버튼을 통해 표시 여부를 제어할 수 있으며, 설정은 Surround View와 Side View에 동시에 적용됩니다.

탐지 대상은 Ship, Motor Boat, Land로 한정되며, Surround View에서는 항상 표시되며, Side View에서는 확대 시에만 나타납니다.


| 2
| Inference Input
| Inference Input 기능은 Inference에 필요한 카메라 설정과 변환 행렬 등을 계산하여, Redis에 입력 데이터로 저장하는 역할을 합니다. 이는 추론 모듈이 정확하게 작동하기 위한 초기 구성 단계입니다.

| 3
| Inference Module
| Inference Module은 Fisheye 카메라 영상을 기반으로 객체를 탐지하고, Side/Surround View에 맞춰 데이터를 변환하여 제공하는 모듈입니다.

AIS나 RADAR로 감지되지 않는 근거리 객체를 보완하며, 결과는 실시간으로 Redis에 저장됩니다.

Nvidia DeepStream과 TensorRT 기반으로 동작하며, 고속 처리와 보안 통신을 지원합니다.

| 4
| Inference Data Streaming
| Inference Data Streaming은 각 카메라의 추론 결과를 실시간으로 클라이언트에 전송하는 기능입니다.

서버는 SVM Redis에서 최신 추론 데이터를 주기적으로 조회하고, 이를 정규화된 스키마로 변환하여 WebSocket 이벤트로 전송합니다.

|===


본 문서는 HiNAS SVM의 Inference 기능군에 대한 요구사항을 정의합니다.
Surround View 및 Side View 영상 기반 위에 객체 탐지 결과를 실시간으로 시각화하고, 거리 정보 등을 운항자에게 제공하는 기능을 포함합니다.

본 챕터에서는 다음 내용을 순차적으로 설명합니다:

1. Inference 입력 데이터 구성 방식 (Inference Input)

2. 추론 처리 파이프라인 및 객체 탐지 방식 (Inference Module)

3. 클라이언트와의 실시간 연동 방식 (Inference Data Streaming)

4. 탐지 결과의 UI 표시 방식 (Inference View UI)

===== Inference 범위

**Inference Input**

- RTSP 기반 카메라 설정값, Homography 보정행렬, 해상도 정보 등을 Redis에 저장합니다.
- Surround View/Side View에 활용될 좌표 변환 기반 입력값을 구성합니다.
- 각 카메라별 RTSP, 위치, 회전, 내부 파라미터, 보정값 등이 포함됩니다.

*Inference Module*

- 입력된 RTSP 스트림에서 객체를 탐지하고, 좌표계 변환 및 거리 계산을 수행합니다.
- NVIDIA DeepStream 기반의 추론 파이프라인으로 구성되며, fisheye 보정 및 다각형 처리 로직을 포함합니다.
- 탐지 결과는 Redis의 `svm:inference` 키에 JSON 형식으로 저장됩니다.

*Inference Data Streaming*

- Redis에 저장된 추론 결과를 WebSocket을 통해 주기적으로 클라이언트에 전송합니다.
- 카메라별 객체 리스트와 거리 정보 등을 포함한 정규화된 데이터 형식을 사용합니다.
- 전송 주기는 0.2초로 설정되어 실시간 표시를 지원합니다.

*SVM Frontend*

- Surround View 및 Side View 화면에 탐지된 객체를 시각적으로 표시합니다.
- 객체는 호 형태의 라벨로 표시되며, 사용자는 On/Off 버튼을 통해 기능을 제어할 수 있습니다.
- 각 View 상태(Side 확대 여부 등)에 따라 표시 조건이 달라집니다.

===== Inference 의존성

*IP Camera (RTSP 송신)*

- Fisheye 카메라에서 실시간 RTSP 영상이 송신되지 않으면 추론이 불가능합니다.

*SVM Redis*

- Inference Input 및 추론 결과를 저장하는 공유 메모리로 사용되며, 데이터 정합성과 성능 유지가 필수적입니다.

*Storage Module*

- 카메라 보정값, Homography 행렬, LUT 등 추론에 필요한 보정 데이터를 제공하며, API 호출을 통해 각 단계에서 참조됩니다.

*DeepStream Engine*

- 추론 처리에 사용되는 GPU 기반 추론 프레임워크이며, 네트워크·모델 구성에 따라 정확도와 성능이 좌우됩니다.

*SVM Backend*

- 추론 결과의 실시간 전송을 담당하며, 네트워크 장애 발생 시 UI 현시가 중단될 수 있습니다.

*SVM Frontend*

- 표시 조건(View 확대 여부, On/Off 상태 등)에 따라 탐지 결과의 시각적 표시 여부가 결정됩니다.


<<<
include::inference_view_ui.adoc[]

<<<
include::inference_input.adoc[]

<<<
include::inference_module.adoc[]

<<<
include::inference_data_streaming.adoc[]
