==== Inference Data Streaming

===== 설명
본 기능은 각 카메라에 대한 객체 탐지 추론(Inference) 결과를 클라이언트에 실시간으로 스트리밍하는 기능이다. 클라이언트가 /svm/inference 네임스페이스에 연결되면, 서버는 백그라운드 태스크를 실행하여 SVM Redis로부터 추론 결과를 주기적으로 조회하고, 이를 정규화된 스키마로 변환한 후 updated-inference-data 이벤트를 통해 전송한다.

데이터는 SVM Redis의 svm:inference 키에 저장되어 있으며, 카메라 이름을 key로 갖는 nested JSON 형태로 관리됩니다. 각 카메라에 대한 추론 결과가 존재하지 않을 경우, 해당 camera_name에 대해 inference_value가 None으로 설정됩니다.

전송되는 데이터는 다음과 같은 정규화된 스키마(InferenceSchema)로 구성된다:

[cols="1,2", options="header"]
|===
| 필드 | 설명

| camera_name | 해당 추론 결과가 수신된 카메라의 이름 (예: "bow", "stbd-1")
| inference_value | 추론 객체 리스트. 객체가 없는 경우 None 반환
|===

inference_value는 객체의 ID, 신뢰도, 객체 타입, 중심점 거리, 가장자리 거리, 추론된 다각형을 포함한다:

[cols="1,2", options="header"]
|===
| 필드 | 설명

| infer_id | 객체 고유 ID
| confidence | 모델이 예측한 신뢰도 (0.0 ~ 1.0)
| class_type | 객체 분류 타입 (예: 선박, 암벽 등)
| center_distance | 자선 중심 기준 거리 정보 (거리, own_point, object_point 포함)
| edge_distance | 자선 측면 기준 거리 정보 (거리, own_point, object_point 포함)
| points | 객체 외곽 다각형 좌표 리스트
|===

전송 주기는 0.2초이며, 클라이언트는 실시간으로 다수 카메라의 추론 데이터를 동시에 수신할 수 있다.

===== 실행 조건

SVM Redis가 정상적으로 연결되어 있어야 함 (svm:inference 키 존재 필요)

카메라 스트리밍 및 추론 모듈이 정상 동작 중이어야 함

===== 검증 기준

[cols="1,3", options="header"]
|===
| 항목 | 검증 기준

| 실시간 데이터 수신 | 클라이언트가 /svm/inference 네임스페이스에 연결된 이후, 0.2초 간격으로 updated-inference-data 이벤트를 수신해야 한다.

| 데이터 정합성 | 각 카메라에 대한 JSON 응답은 InferenceSchema 형식을 따르며, 필수 필드가 누락되지 않아야 한다.

| Redis 연결 정상 여부 | svm:inference 키에 정상적으로 접근 가능한 경우에만 데이터 전송이 이루어져야 한다.

| 예외 응답 처리 | 다음 상황에 따라 지정된 이벤트가 전송되어야 한다:

Redis에 데이터 없음 → no_signal_error

Redis 또는 네트워크 오류 → server_error
|===

===== 성능 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항

| 전송 주기 정확도 | 0.2초 ±50ms 이내 간격으로 WebSocket 이벤트가 전송되어야 한다.

| 연속 오류 대응 | Redis 오류 또는 NoSignal 상태 발생 시에도 이벤트는 중단되지 않고 오류 상태가 주기적으로 전송되어야 한다.
|===

===== 인터페이스 요구사항

*WebSocket Client → `/svm/inference`*

- 클라이언트는 해당 네임스페이스로 소켓 연결을 수행한다.


*Redis Key: `svm:inference`*

- 각 카메라 이름을 key로 갖는 추론 결과 JSON 데이터가 저장된 Redis 키

===== 기타 요구사항

*객체 미검출 대응*

- 객체가 없을 경우, 해당 카메라에 대해 inference_value는 None으로 전송되어야 한다.

*연결 유지*

- 소켓 연결 유지 상태는 서버가 관리하며, 클라이언트가 연결을 종료하지 않는 한 데이터 전송이 지속되어야 한다.


