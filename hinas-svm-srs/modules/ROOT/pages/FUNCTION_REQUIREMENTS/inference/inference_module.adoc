==== inference module

===== 설명
해당 모듈은 fish-eye 카메라 센서를 통해 취득한 stream에서 객체를 인지하고 거리를 추정하는 역할을 담당한다. 특히 AIS 데이터가 없거나, 근거리에 위치하여 RADAR 음영 영역이 발생하는 상황에서 근거리 객체에 대한 고밀도의 정보를 제공한다. 인지된 객체 정보는 side/surround view 형태에 맞게 변환되어 전달되며, 이후 거리 탐지가 가능한 다른 센서 데이터와 융합되어 추가적인 정보를 제공한다.

inference 모듈은 Nvidia의 DSL(Deepstream Service Library)를 기반으로 구현되어 있으며, inference 모듈에서 객체 인지을 위해 사용하는 인퍼런스 엔진(engine) 파일은 ultralytics 라이브러리 기반으로 학습된 웨이트(weight) 파일을 타겟 GPU에 맞춰 변환하여(Tensor RT) 사용한다.

.svm inference app의 파이프라인

.SVM Inference App Pipeline
image::FUNCTION_REQUIREMENTS/inference/inference_flow_diagram.png[align="center",600]


해당 모듈의 파이프라인은 위 그림과 같이 구성됩니다.

1) rtsp stream source 입력 & streammux & pgie
각 Fisheye 카메라로부터 RTSP 스트림을 입력으로 받아 처리하며, 스트림의 FPS는 20이고 해상도는 1280×960이다.

각 카메라에서 수신한 스트림은 `nvstreammux`를 통해 하나의 배치(batch)로 구성됩니다. 이후 PGIE(primary GPU inference engine)는 인스턴스 세그멘테이션을 수행하는 모델로, 추론 결과로는 마스크(mask), 바운딩 박스(bounding box), 클래스(class), 신뢰도(score)가 출력됩니다.

클래스는 다음과 같다:

- 0: Ship
- 1: MotorBoat
- 2: Navigation Aid
- 4: Land

2) mask validation check
본 함수는 fisheye 카메라 이미지 좌표계에서 Side View 또는 Surround View 이미지 좌표계로의 좌표 변환을 수행하기 전에, 탐지 마스크 결과 중 유효하지 않은 위치에 해당하는 부분을 제거한다.

- Side View 변환의 경우, fisheye 카메라 이미지 좌표계에서 카메라 렌즈의 FOV(Field of View)를 벗어난 위치를 유효하지 않은 영역으로 판단한다.
- Surround View 변환의 경우, FOV를 벗어난 위치뿐만 아니라 fisheye 카메라 이미지 좌표계에서 수평선보다 높은 위치도 유효하지 않은 영역으로 판단한다.

유효 영역 판단을 위해 카메라 렌즈의 FOV는 storage module api call로 획득한 카메라 캘리브레이션 값을 기반으로 생성해둔 각 카메라별 FOV 유효 마스크를 사용한다.

수평선을 기준으로 픽셀의 위치가 수평선보다 위에 있는지 여부 판단은, fisheye 카메라 이미지 좌표계에서 카메라 정규 좌표계로의 변환을 위해 storage module api call로 획득한 카메라 캘리브레이션 값을 기반으로 생성해둔 각 카메라별 LUT(Look-Up Table)를 사용한다.
    
3) find contour & coord transformation

각 view에 맞는 유효성 판단을 거쳐 필터링된 마스크 결과에서 contour를 추출한다.
추출한 contour의 각 포인트는 LUT를 이용해 fisheye 이미지 좌표계에서 카메라 정규 좌표계로 변환됩니다.

이후, storage module api call을 통해 전달받은 각 카메라별로 각 view에 해당하는 3x3 homography matrix를 사용하여 카메라 정규 좌표계 상의 포인트를 최종적으로 원하는 Side View 또는 Surround View 이미지 좌표계로 변환한다.

4) distance prediction (비활성화)
Surround View 좌표계로 변환된 처리를 거친 객체의 contour를 이용해 두 종류의 거리를 계산한다.

- 자선 중심점(center)으로부터  contour까지의 최단거리
- 자선의 외곽(edge)으로부터 가장 가까운 거리

여기서 자선의 외곽은 카메라 위치를 기준으로한 임의의 직사각형 형태로 설정됩니다. 측정을 위해 contour의 포인트 중 자선의 중심점 또는 외곽과 가장 가까운 포인트를 탐색하고, 해당 포인트와의 픽셀 기준 거리를 계산한다. 이후 mpp(meter per pixel)를 곱해 실제 거리로 변환한다.

최종적으로 계산된 거리와 해당 포인트 정보를 inference_data_streaming에서 정의한 center_distance 및 edge_distance의 형식에 맞추어 전달한다.

5) region filtering

surround view를 구성할 때 각 카메라는 고유의 담당 영역을 가진다. 각 카메라로부터 추론한 객체 contour를 surround view로 변환했을 때, 자선 중심점으로부터 가장 가까운 포인트가 카메라의 담당 영역을 벗어나는 경우, 해당 contour는 surround view에서 제거됩니다.
이를 제거하지 않는다면 한 객체에 대한 결과가 여러 카메라로에서 중복으로 검출될 수 있기 때문이다.

6) Redis-set
    
모듈의 최종 결과를 JSON 형태로 Redis의 `svm:inference` 키에 전송한다. 전송되는 결과는 inference_data_streaming 형식을 따른다.

===== 실행 조건
- SVM 카메라 스트리밍이 정상 동작 중이어야 한다.

- SVM Redis가 정상적으로 연결되어 있어야 한다. (svm:inference_input 키 존재 필요)

===== 검증 기준
[cols="1,3", options="header"]
|===
| 항목 | 검증 기준

| 실시간 처리 속도 | 각 카메라에 대해 객체 탐지 및 후처리를 수행한 후 전송까지의 전체 과정이 10FPS로 처리되는지 로그를 통해 확인한다.

| Redis `storage:inference_input` 연결 정상 여부 | storage module api call으로 정상적인 접근이 가능한 경우에만 모듈 실행 및 데이터 수신이 수행한다. 정상적인 접근이 불가능한 경우 5초마다 재접속 시도가 반복되는지 확인한다. 이후 Redis 연결이 복구되었을 때, 모듈이 정상 동작을 재개하는지 확인한다.

| RTSP 스트리밍 상태 기반 동작 | 카메라의 RTSP 스트림을 3초 이상 중단시킨 후 해당 카메라에 대한 동작이 중지되는지 확인하고, 스트림 재개 시 해당 카메라의 동작이 3분 이내 정상적으로 재개되는지 확인한다. 

| 예외 응답 처리 | Redis에 입력 데이터가 없는 경우 no_signal_error, Redis 또는 네트워크 연결이 끊긴 경우 connection_error 이벤트가 전송되는지를 확인한다.
|===
===== 성능 요구사항
[cols="1,3", options="header"]
|===
| 항목 | 요구 사항

| 실시간 처리 속도 및 확장성 | 각 카메라에 대해 객체 탐지 및 후처리를 수행한 후 전송까지의 전체 과정이 10FPS 이상으로 처리되어야 한다. 또한 최대 10대의 카메라가 연결된 경우에도 모든 카메라에서 평균 10FPS 이상의 성능을 유지해야 한다.

| 객체 검출 | bbox의 mAP@0.7 기준 객체 검출 정확도는 0.7 이상이어야 한다. 

| 메모리 사용량 제한 | svm-inference-app의 gpu 사용량이 3GB를 초과하지 않아야하며, 누수 없이 일정 수준을 유지해야 한다. 

| 모듈 재실행 | 모듈 중단된 후 재실행되었을 때, 3분 이내에 다시 정상적으로 동작을 재개해야 한다.

|===

===== 인터페이스 요구사항
[cols="1,3", options="header"]
|===
| 인터페이스 | 설명

| Redis Key: storage:inference_input | 각 카메라 이름을 key로 갖는 inference 서비스에 필요한 입력 JSON 데이터가 저장된 Redis 키

| Redis Key: svm:inference | 각 카메라 이름을 key로 갖는 추론 결과 JSON 데이터가 저장된 Redis 키
|===

===== 기타 요구사항
|===
| 항목 | 요구사항

| Redis 보안 연결 | Redis 서버와의 통신 시 SSL/TLS 암호화를 사용해야 하며, 인증서 기반의 보안 연결을 통해 데이터가 암호화된 상태로 전송되어야 한다. 잘못된 인증서로 접근 시 연결이 차단되어야 하며, 인증서가 없는 경우 시스템은 Redis에 연결되지 않아야 한다.

| 배포 자동화 | Docker 환경에서 배포되며, 자동화된 구성 스크립트를 제공해야 한다.
|===
