==== Target Selection & Rendering
===== 설명

===== Target 선택
해당 기능은 운항자가 화면 내 물표(Target)를 마우스로 선택하고, 최대 4개까지 선택된 물표의 정보를 확인하고 상호작용할 수 있도록 하는 기능입니다.

선택된 물표는 A, B, C, D로 라벨링되며, 가장 최근 선택된 물표의 AIS 정보가 정보창에 표시됩니다. 선택한 물표는 Bracket으로 강조됩니다.

.Target Information Target Selection
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_target_selection.png[align="center",600]


===== Target 렌더링

Target 렌더링 주기는 화면에 표시되는 타선(Target Ship) 정보의 업데이트 주기를 의미하며, 사용자가 선택한 Range Scale(거리 범위)에 따라 자동으로 조정됩니다. 이는 시스템 부하를 최적화하면서도 가시성과 실시간성을 확보하기 위한 정책입니다.

렌더링 주기는 짧은 Range Scale일수록 높은 주기로 업데이트되며, 반대로 넓은 Range Scale에서는 비교적 긴 주기를 갖습니다. 이를 통해 근거리에서는 빠른 반응성을, 원거리에서는 자원 효율적인 처리를 보장합니다.

[cols="1,1", options="header"]
|===
| Range Scale (nm) | 렌더링 주기 (ms)

| 0.5 | 300
| 0.75 | 600
| 1.5 | 900
| 3 | 1200
| 6 | 2000
| 12 | 3000
| 18 | 4000
| 기타 | 5000
|===

렌더링 대상은 거리 기준으로 **최대 1000개까지** 표시할 수 있으며,
초과할 경우 경고 메시지를 Side View와 Target Information Display 하단에 출력하고 초과 항목은 렌더링되지 않습니다.
렌더링 대상이 1000개를 초과하는 경우 아래와 같이 처리됩니다:

- **표시 가능 수 초과 시**, `"Target display limit(1000) exceeded."` 라는 경고 메시지를 출력합니다.
- **초과된 항목은 렌더링에서 제외**되며, 사용자 설정을 통해 제한값 조정이 가능해야 합니다.



===== 실행 조건
- 화면 내 물표가 1개 이상 표시되고 있음
- 마우스 입력을 수신할 수 있는 상태
- AIS 또는 ARPA 데이터가 유효하게 수신되고 있음
- 선택된 물표가 4개 미만인 경우, 새로운 물표 선택 가능
- 선택된 물표가 4개인 경우, 하나를 해제해야 새로운 물표를 선택할 수 있음


===== 검증 기준


**Target Selection**

- 아이콘 선택 시 라벨의 할당이 아래 Figma 로직에 따라 적용 되어야 함.

**Target Selection Label Logic Diagram**

https://www.figma.com/design/KRxrOCqd2RUIt7WA8ouwPz/SVM-v1.0.0---24.-10.-31-?node-id=32-53983&t=oqHcZ6gDlhwlBRM0-4[SVM SRS Figma Target Information Display] 에서, 3.2.4 Target Selection을 참고하여 Target Icon의 로직을 확인할 수 있습니다.


- 물표 선택 시 정보창에 해당 물표의 AIS 정보가 표시되어야 함

- 선택된 물표는 Bracket으로 강조되어야 함





===== 성능 요구사항

- 물표 선택 시 UI 반영까지 300ms 이내로 처리되어야 함
- 5개째 선택 시 Toast 메시지는 1초 이내에 출력되어야 함
- 좌/우 방향키 탐색 시 정보창 전환 반응은 200ms 이내여야 함
- 선택 해제 및 라벨 재부여 로직은 사용자 입력 이후 500ms 이내에 처리되어야 함


*Target Rendering*

- 각 Range Scale 선택 시, 정의된 렌더링 주기가 적용되는지 확인

- 실측 기준으로 ±100ms 이내 오차 범위 내에서 타선 정보가 갱신되는지 검증

- 시스템 부하 상황에서도 각 주기에 따라 안정적으로 렌더링이 수행되는지 성능 테스트로 검증

- 시스템 부하 상황(메모리 사용률 70% 이상, CPU 점유율 80% 이상)에서도 아래 조건을 만족해야 함:

** 동시 렌더링 대상 수가 500개 이상일 때도 설정된 렌더링 주기 내에 타선 정보가 정확히 갱신되어야 함

** 99개의 Dangerous Target과 400개의 일반 Target을 포함한 복합 상황에서도 각 프레임 주기 내 처리 누락 없이 렌더링되어야 함

** 렌더링 지연률은 전체 주기의 5% 이하, 평균 프레임 지연시간은 ±100ms 이내



===== 인터페이스 요구사항

- 마우스 좌클릭: 물표 선택 및 정보창 표시
- 마우스 우클릭: 물표 선택 해제
- 키보드 `<`, `>` 키: 선택된 물표 간 순차 이동
- Toast 메시지: 중앙 하단에 3초간 표시
- SnackBar 메시지: 상단 중앙 또는 지정 위치에 표시
- 정보창 탭 표시: 선택된 대상 개수 표시, 가장 최근 대상 강조

===== 기타 요구사항
- 물표 라벨은 A, B, C, D 순서로 자동 부여되며, 중간 번호가 해제되면 해당 번호를 재사용