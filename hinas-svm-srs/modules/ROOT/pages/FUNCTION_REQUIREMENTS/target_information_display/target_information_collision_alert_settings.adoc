==== Collision Risk Alert Settings

===== 설명

본 기능은 Target 선박과의 충돌 위험을 판단하기 위한 기준값을 설정/조회할 수 있도록 하는 기능이다. 항로 유형에 따라 opensea(외해 항로) 및 congested(혼잡 해역) 설정을 각각 지원하며, 설정된 값은 storage module 키 svm:sensor_fusion:ca에 저장됩니다.

====== Collision Risk Mode

- Congested Mode: 혼잡한 해역에서의 운항을 위한 설정
- Open Sea Mode: 개방된 해역에서의 운항을 위한 설정


1) Collision Risk Mode 설정 조회

- 기능 설명 : Collision Risk Mode 설정을 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/preference/collision-risk-mode
- Request : 없음
- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "Get preference successful.",
    "data": {
        "is_congested_mode": false
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get preference successful.)
+
data : Collision Risk Mode 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| is_congested_mode | boolean | 혼잡 해역 모드 여부 (true: 활성화, false: 비활성화)
|===


2) Collision Risk Mode 설정 변경

- 기능 설명 : Collision Risk Mode 설정을 변경한다.
- HTTP Method : PUT
- Endpoint : /svm-api/v1/preference/collision-risk-mode
- Request :
+
[source,json]
----
{
    "is_congested_mode": true
}
----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| is_congested_mode | boolean | 혼잡 해역 모드 여부 (true: 활성화, false: 비활성화) | required, true or false
|===

- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "update preference successful.",
    "data": {
        "is_congested_mode": true
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (update preference successful.)
+
data : Collision Risk Mode 설정 정보



====== Collision Risk Setting


.Target Information Display Collision Alert Settings
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_collision_alert_setting.png[align="center",700,300]



/collision-setting API를 통해 설정을 조회하거나 갱신할 수 있으며, 충돌 판단 시 사용되는 CPA (Closest Point of Approach), TCPA (Time to CPA), 거리, 경고 임계값 등 다양한 값들을 구성할 수 있다.

설정 값은 다음과 같은 필드로 구성된다:

[cols="1,2,2", options="header"]
|===
| 필드 | 타입 | 설명
| min_tcpa | int | 최소 TCPA (분 단위), 0보다 커야 함
| max_tcpa | int | 최대 TCPA (분 단위), 60 이하
| min_cpa | float | 최소 CPA (해리), 0보다 커야 함
| max_cpa | float | 최대 CPA (해리), 7 이하
| min_dist | float | 최소 거리 (해리), 0보다 커야 함
| max_dist | float | 최대 거리 (해리)
| alarm_threshold | int | 위험 판단 경고 임계값 (0 ~ 99)
|===

===== 실행 조건

Redis에 sensor_fusion:ca 키가 존재해야 하며, 없을 경우 기본 구조를 생성하여 저장

PUT /collision-setting 요청 시 요청 본문은 TargetCollisionSettingRequest 형식으로 전달되어야 함

===== 검증 기준

[cols="1,3", options="header"]
|===
| 항목 | 검증 기준

| 설정 값 유효성 | 각 설정 항목에 대한 유효성 검사 수행 (예: min < max, 최대 범위 제한 등)
| storage module 저장 확인 | 설정 값이 storage module의 svm:sensor_fusion:ca 키에 정상적으로 반영되어야 함
| 조회 일치 여부 | 설정 후 다시 조회 시, 요청한 값이 그대로 반영되어야 함
|===

===== 성능 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항

| 처리 시간 | 조회 및 갱신 작업은 500ms 이내에 완료되어야 함
|===

===== 인터페이스 요구사항

[cols="1,3", options="header"]
|===
| 인터페이스 | 설명

| GET /collision-setting | 현재 설정값을 조회하는 API
| PUT /collision-setting | 설정값을 갱신하는 API
| storage module Key: svm:sensor_fusion:ca | 설정값이 저장되는 storage module 키
|===

===== 기타 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항

|  |
| 예외 처리 | 요청 값이 유효하지 않거나 storage module 오류 발생 시 적절한 예외 처리 필요
| 설정 구조 유지 | 저장 구조는 TargetCollisionSettingSchema 포맷을 따라야 함
|===
*항로별 분리 설정*

- 혼잡 해역과 외해 항로에 대해 별도의 설정을 지원

*설정 영속성*

- 모든 설정 값은 시스템 재시작 이후에도 유지되어야 함
- Storage Module에 지속적으로 저장되어야 하며, 손실되지 않아야 함

*설정 변경 영향 범위*

- 일부 설정값은 변경 즉시 시스템에 반영되어야한다.
