=== Target Information Display

==== Overview
===== Terminology
이 섹션에서는 본 챕터에서 반복적으로 사용되는 주요 구성 요소 및 용어를 간략히 정의합니다.


*Target (타선)*

자선(본선) 외부에 존재하는 AIS 또는 ARPA 신호 기반의 외부 선박 또는 물체를 지칭합니다. 화면 상에 아이콘으로 표시되며, 위험도, 방향, 거리 등 다양한 정보를 포함합니다.

*Target Icon (타선 아이콘)*

타선을 시각적으로 나타내는 그래픽 요소입니다. 센서 종류(AIS, ARPA, Fusion), 타선 상태(정상, 소실, 위험 등)에 따라 다양한 형태와 색상으로 표시됩니다.

*Prediction Vector (예측 벡터)*

Target 또는 자선의 향후 예상 위치를 선형 벡터 형태로 시각화한 요소입니다. 3, 6, 12, 18분 등의 시간 간격을 기준으로 설정할 수 있으며, 움직임 예측에 활용됩니다.

*Azimuth Mode (방위각 모드)*

화면 내 12시 방향 기준을 설정하는 모드입니다. North Up(진북 기준), Course Up(COG 기준), Head Up(HDG 기준)의 세 가지 모드가 존재하며, 선택한 모드에 따라 화면 전체가 회전합니다.

*Range Ring (거리 환산 링)*
자선 기준으로 등거리 간격으로 표시되는 원형 링으로, 타선과의 상대 거리를 직관적으로 파악할 수 있도록 돕는 기능입니다.

*Dangerous Target (위험 타선)*
충돌 가능성이 높은 타선으로, CPA/TCPA 등의 기준값을 초과할 경우 시스템에 의해 자동으로 식별되어 강조 표기됩니다.

*Collision Risk Mode (충돌 위험 모드)*
자선이 항해 중인 환경(혼잡 해역, 개방 해역 등)에 따라 Collision Alert 기준값을 달리 설정하는 운용 모드입니다.

*Lost Target (소실된 타선)*
일정 시간 이상 센서로부터 갱신되지 않아 추적이 중단된 타선을 의미합니다. 화면 상에서 아이콘이 진북 방향으로 고정되며, 깜빡임 효과와 함께 표시됩니다.

*Target Risk (타선 위험도)*
타선의 충돌 위험 정도를 일반, 경고, 위험로 표시하는 등급입니다. CPA, TCPA, 상대 거리 등을 종합 분석하여 산정됩니다.

===== Table of Contents
[cols="1,1,4", options="header"]
|===
| No.
| Function
| Description

| 1
| Target Information View
| Target Information Display 기능 및 화면 구성, 아이콘 구성에 대해서 설명합니다.

| 2
| Prediction Vector
| Target 선박의 3, 6, 12, 18분 후의 예측 벡터를 표시하는 기능

| 3
| Range Ring
| Target Information Display 화면에 Range Ring을 표시하는 기능

| 4
| Azimuth Mode
| Target 선박의 Azimuth Mode를 설정하는 기능

| 5
| Target Selection & Rendering
| Target Information Display 에서 관심있는 Target을 선택하는 기능, Target 아이콘을 렌더링하는 기능

| 6
| Dangerous Target
| Target 선박의 위험도를 판단하여 위험한 Target을 표시하는 기능

| 7
| Target Ship Information
| Target 선박의 상세 정보를 조회하는 기능

| 8
| Collision Risk Alert Settings
| Target 선박과의 충돌 위험을 판단하기 위한 기준값을 설정/조회하는 기능

| 9
| Collision Risk Alert
| Target 선박과의 충돌 위험을 판단하여 경고하는 기능

| 10
| Sensor Fusion
| 작성중

| 11
| Target Data Streaming
| Sensor Fusion 에서 도출된 target 데이터를 Client에 실시간으로 전송하는 기능

|===

본 문서는 HiNAS SVM의 Target Information Display 기능에 대한 요구사항을 정의합니다.
본 챕터에서는 다음 내용을 순차적으로 설명합니다.

1. Target Information View

2. Prediction Vector

3. Range Ring

4. Azimuth Mode

5. Target Selection

6. Dangerous Target

7. Target Ship Information

8. Collision Risk Alert Settings

9. Collision Risk Alert

10. Sensor Fusion

11. Target Data Streaming

1장에서는 기본적인 Target Information Display의 인터페이스 및 아이콘을 설명합니다.
2~4장에서는 Prediction Vector, Range Ring, Target Selection 등 사용자가 주변 상황을 직관적으로 파악할 수 있도록 돕는 기능과 설정을 설명합니다.
5~7장에서는 표적(Target)의 선택 및 위험성 판단에 대한 기준과 동작 방식, 그리고 AIS 기반의 상세 선박 정보 제공 기능을 다룹니다.
8~9장에서는 TCPA/CPA 기반의 충돌 위험 예측 설정과 해당 위험에 대한 경고 발생 조건, 알림 방식 등을 정의합니다.


10장에서는 AIS, ARPA 등 다양한 센서로부터 수신된 데이터를 통합 처리하는 방식(Sensor Fusion)에 대해 설명하며,
11장에서는 이와 같이 처리된 Target 정보를 실시간으로 스트리밍하여 클라이언트 화면에 반영하는 데이터 연동 구조를 정의합니다.

각 장의 구성은 사용자 또는 시스템이 정보를 처리하는 절차적 흐름인 "인지 → 판단 → 분석 → 알림" 에 맞춰 설계되었습니다.
즉, 사용자가 상황을 시각적으로 인지하고, 주목할 대상(Target)을 선택하여 위험성을 판단한 뒤, 시스템이 이를 종합 분석하여 충돌 경보를 알리고 대응할 수 있도록 구성된 구조입니다.

10장과 11장에서는 이러한 일련의 기능이 내부 시스템 로직과 실시간 통신 구조로 어떻게 구현되고 연계되는지를 설명하며,
단순한 사용자 조작 수준을 넘어, 센서 통합, 데이터 처리, 클라이언트 반영까지 포함하는 시스템 아키텍처 수준의 설명으로 기능 흐름을 완결합니다.


===== Target Information Display 범위 및 의존성.

*SVM Backend*

- SVM Frontend API 연동하여 설정 값 저장
- Target 관련 정보를 WebSocket을 통해 클라이언트로 전송
- Dangerous Target 판단 로직, Prediction Vector 생성 등의 판단 기능 구현


*Sensor Fusion*

- AIS, ARPA, Radar 등 다양한 센서 데이터를 융합하여 타선(Target) 리스트 생성
- Fusion 결과를 기반으로 통합된 Target 데이터 구조 제공

*Core Redis*

- VDR(Vessel Data Recorder)로부터 UDP 6501 포트로 수신된 센서 데이터를 NMEA 프로토콜 기반으로 파싱하여,
  자선 및 AIS, ARPA 관련 정보를 Core Redis에 저장.

*SVM Redis*

- Sensor Fusion에서 정제된 자선과 타선의 예측 위치 정보를 저장하는 Redis 인스턴스


*SVM Frontend*

- SVM Backend로부터 수신한 데이터를 기반으로 Target Information Display UI를 구성하며,
  사용자가 주변 상황을 직관적으로 인식할 수 있도록 시각화합니다.

<<<
include::target_information_view.adoc[]

<<<
include::target_information_prediction_vector.adoc[]

<<<
include::target_information_range_ring.adoc[]

<<<
include::target_information_azimuth_mode.adoc[]

<<<
include::target_information_target_selection.adoc[]

<<<
include::target_information_dangerous_target.adoc[]

<<<
include::target_information_target_ship_information.adoc[]

<<<
include::target_information_collision_alert_settings.adoc[]

<<<
include::target_information_collision_alert.adoc[]

<<<
include::sensor_fusion.adoc[]

<<<
include::target_data_streaming.adoc[]