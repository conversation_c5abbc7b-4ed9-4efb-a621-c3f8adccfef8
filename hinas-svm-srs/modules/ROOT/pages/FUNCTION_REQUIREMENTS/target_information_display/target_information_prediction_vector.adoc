==== Prediction Vector
===== 설명
본 기능은 AIS 또는 ARPA 기반의 타선(Target)에 대해, 3분 / 6분 / 12분 / 18분 후의 위치를 예측한 Vector를 화면에 점선 형태로 시각화하여 운항자가 타선의 이동 방향과 충돌 위험을 사전에 인지할 수 있도록 지원합니다.

사용자는 화면 우측의 Vector 예측 메뉴를 통해 예측 시간(Off, 3 min, 6 min, 12 min, 18 min)을 선택할 수 있으며, 선택된 예측 시간에 따라 자선 및 타선의 이동 벡터가 표시됩니다.
벡터는 점선 형태로 시각화되며, 자선 및 타선 모두 동일한 시간 기준의 예측 벡터를 표시합니다.
기본 예측 시간은 12분이며, 사용자가 메뉴를 닫거나 화면을 이동해도 해당 설정은 유지됩니다.

.Target Information Display Prediction Vector
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_prediction_vector.png[align="center",600]



*Prediction Vector API*


1. Prediction Vector Length 설정 조회

- 기능 설명 : Prediction Vector Length 설정을 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/preference/prediction-vector-length
- Request : 없음
- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "Get preference successful.",
    "data": {
        "prediction_vector_length": 18
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get preference successful.)
+
data : Prediction Vector Length 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| prediction_vector_length | integer | 예측 벡터 길이 (단위: 초)
|===

2. Prediction Vector Length 설정 변경

- 기능 설명 : Prediction Vector Length 설정을 변경한다.
- HTTP Method : PUT
- Endpoint : /svm-api/v1/preference/prediction-vector-length
- Request :
+
[source,json]
----
{
    "prediction_vector_length": 18
}
----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| prediction_vector_length | integer | 예측 벡터 길이 (단위: 초) | required, 0, 3, 6, 12, 18만 입력 가능
|===
- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "update preference successful.",
    "data": {
        "prediction_vector_length": 6
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (update preference successful.)
+
data : Prediction Vector Length 설정 정보

===== 실행 조건

- Target 정보가 AIS 또는 ARPA 데이터로 수신되고 있어야 함

- 예측 Vector를 계산할 수 있는 속력(SOG) 및 방향(COG) 데이터가 유효해야 함

===== 검증 기준

*메뉴 아이콘*

- Vector 아이콘에 rollover 시, 글자색이 파란색으로 변경되며 서브 메뉴가 표시되어야 함

*서브 메뉴 항목*

- Off, 3 min, 6 min, 12 min, 18 min 항목이 정확히 표시되어야 함

*기본 선택값*

- 최초 진입 시, 예측 시간은 12 min으로 선택되어 있어야 함

*설정 상태 유지*

- Surround View 등 타 화면으로 전환 후 복귀 시에도 마지막 설정값 유지

*메뉴 동작*

- 예측 시간 선택 시, 메뉴는 자동으로 닫혀야 하며, 오른쪽 < 버튼으로 수동 닫기도 가능해야 함

*자선 벡터*

- 예측 시간이 Off일 경우, 자선 벡터 미표시. 나머지 선택 시 점선 형태로 표시됨

*타선 벡터*

- 유효한 데이터(COG, SOG)가 존재하는 모든 타선에 대해 예측 시간에 해당하는 점선 벡터 표시됨

*점선 길이*

- 예측 시간에 따라 점선의 길이가 비례하여 변경되어야 함 (3 min < 6 min < 12 min < 18 min)

===== 성능 요구사항

*API 응답 시간*

- 예측 벡터 계산 및 시각화는 설정 변경 후 500ms 이내에 화면 반영이 완료되어야 함

*화면 UI 반영 시간*

- 동시에 200개 이상의 Target에 대해 예측 벡터가 표시될 경우에도 UI 렌더링 지연이 없어야 함
- 예측 벡터는 COG, SOG 데이터를 기반으로 실시간으로 갱신되어야 함 (최대 1초 주기)
** *타선의 COG 또는 SOG의 데이터가 사라지거나 유효하지 않은 경우, 3초 이내에 타선의 예측 벡터가 제거되어야 되며 아이콘이 변경되어야 함.*

===== 인터페이스 요구사항

1. 프로토콜 및 형식
- 모든 API 통신은 HTTP/1.1 또는 HTTP/2를 사용함
- 요청 및 응답 데이터는 JSON 형식으로 주고받아야 함
- Content-Type은 `application/json`으로 지정해야 함

2. 응답 메시지 구조
- 모든 응답은 아래 구조를 따라야 함:
+
[source,json]
----
{
  "code": 200000,
  "message": "설명 메시지",
  "data": { ... }
}
----
- code: 표준 응답 코드 (성공: 200000, 실패: 400/500 등)
- message: 성공/실패에 대한 간단한 메시지
- data: 실제 응답 내용 (object 또는 null)


===== 기타 요구사항

*UI 시인성*

- Prediction Vector 는 **사용자가 최소 1m 거리에서 화면을 관찰할 때 명확히 인지**할 수 있도록 설계되어야 합니다.
- 사용자가 선박 조타실 또는 운항 위치에서 일반적인 시야각(약 30~40도)으로 바라보는 조건에서도,
  Prediction Vector의 선 굵기, 색상 대비, 외곽 화살표의 크기 및 방향 각도 등은 시각적 혼동 없이 식별 가능해야 합니다.
- **Prediction Vector의 색상 대비, 굵기등은 주간과 야간, 그리고 다양한 밝기 조건에서도 시인성을 유지**해야 하며, 국제 해사 표준(예: IMO HMI 지침)을 준수하는 색상 대비가 확보되어야 합니다.

*설정 영속성*

- 모든 설정 값은 시스템 재시작 이후에도 유지되어야 함
- Storage Module에 지속적으로 저장되어야 하며, 손실되지 않아야 함