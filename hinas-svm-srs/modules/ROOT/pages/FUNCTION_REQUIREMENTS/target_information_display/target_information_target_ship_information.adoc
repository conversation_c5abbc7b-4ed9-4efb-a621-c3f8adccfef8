==== Target Ship Information

===== 설명
본 기능은 HiNAS SVM의 Target Information Display에서 선택된 선박(Selected Target) 및 위험 선박(Dangerous Target)의 정보를 좌측 하단의 정보창에 표시하는 기능입니다.
사용자는 좌측 하단에서 최대 4개의 Selected Target 및 최대 99개의 Dangerous Target에 대한 정보를 확인할 수 있으며, 각 물표에 대해 다음과 같은 항목이 표시됩니다.


.Target Ship Information
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_target_information.png[align="center",600]


- **SHIP NAME**: AIS에서 수신된 선박명이며, 정보가 없을 경우 "No name"으로 표시됩니다.
- **CPA (Closest Point of Approach)**: 자선과의 최소 접근 거리로, 단위는 nautical miles(nm), 자리수는 `xxx.xx` 형식으로 표시됩니다.
- **TCPA (Time to CPA)**: CPA 발생 시점까지 남은 시간으로, 단위는 분(min), 자리수는 `xxx.x` 형식으로 표시됩니다.
- **DIST**: 자선과의 현재 거리로, 단위는 nm, 자리수는 `xx.xx` 형식입니다.
- **BRG (Bearing)**: 자선 기준 해당 선박의 방위각으로, 단위는 도(°), 자리수는 `xxx.x` 형식입니다.
- **HDG (Heading)**: 해당 선박의 실제 항해 방향이며, 단위는 도(°), 자리수는 `xxx.x`입니다.
- **COG (Course Over Ground)**: 실제 진행 방향을 나타내며, 단위는 도(°), 자리수는 `xxx.x`입니다.

위 항목 중 데이터가 수신되지 않을 경우, 자리수에 맞는 대시(`-`) 기호로 값이 대체됩니다. 예: CPA 값이 없으면 `---.--`로 표기

선택 해제 기능은 다음과 같습니다.

- "Clear All" 버튼 클릭 시 모든 선택된 물표가 해제되며 정보창도 함께 닫힙니다.
- 개별 "Unselect" 버튼 클릭 시 해당 물표만 해제되며, 정보창은 나머지 선택 상태에 따라 갱신됩니다.

추가적으로, 선택된 선박이 화면에서 사라졌더라도 정보창에는 계속 정보가 표시됩니다.

**AIS 정보 미수신 시 처리 방식:**

- AIS Class A: 3분 30초 이상 미수신 시 "Lost Target"으로 간주
- AIS Class B: 18분 30초 이상 미수신 시 "Lost Target"
- ARPA: 40초 이상 미수신 시 "Lost Target"
- Lost Target 상태는 20초 동안 유지되며 이후 자동 삭제됩니다.
- Lost 상태가 되면 정보창에 "Lost Target" 문구가 표시되고, 선박 아이콘은 진북(North Up) 방향으로 회전 후 깜빡이는 애니메이션을 표시합니다.


각 항목의 값은 단위 및 소수점 자리수를 기준으로 정밀하게 표기되며, 지정된 최대값 또는 최소값을 초과하는 경우 아래와 같은 방식으로 처리됩니다.

- **DIST**: 값이 99.99nm를 초과하면 "99.99+"로 표시됨

- **TCPA**: 값이 +1000분 이상이면 "999.9+", -1000분 이하이면 "-999.9-"로 표시됨

- **CPA**: 값이 1000.00nm 이상일 경우 "999.99+"로 표시됨


BRG(Bearing), HDG(Heading), COG(Course Over Ground)는 모두 항해 관련 각도 정보로서, 다음과 같은 범위 내에서만 값이 존재합니다.

- **BRG (Bearing)**: 자선 기준 상대 선박의 방위각이며, 실제 항해 기준으로 `0.0°`부터 `359.9°` 사이의 값만 유효합니다. 따라서 해당 항목은 값이 지정된 자리수(`xxx.x`)를 초과할 가능성이 없습니다.

- **HDG (Heading)**: 선박의 현재 항해 방향으로, 나침반 방향 기준 `0.0°`부터 `359.9°`까지의 범위를 갖습니다. 항법 장비에서도 일반적으로 이 범위를 넘는 값은 출력되지 않으므로 별도의 초과 처리 없이 표기 가능합니다.

- **COG (Course Over Ground)**: 선박의 실제 진행 방향을 의미하며, GPS 기반 항법 시스템에서 측정되며 `0.0°`부터 `359.9°`까지로 제한됩니다. 이 또한 값이 초과될 수 없으므로, 범위 초과 표기 처리는 불필요합니다.

위 세 항목은 모두 각도 단위 특성상 360° 미만 범위로 고정되며, 초과 값을 가질 수 없기 때문에 `999.9+` 등의 별도 표기 예외 처리는 적용하지 않습니다.

AIS에서 수신되는 선박명(`SHIP NAME`)은 고정된 길이가 아니며, 실제로는 영문 기준 최대 20자까지 수신될 수 있습니다. 다만 시스템 UI 내 표시 공간의 제한으로 인해 다음과 같은 처리 기준을 적용합니다.

- 선박명은 최대 16자까지만 표시되며, 이를 초과하는 경우 `...` 말줄임표(ellipsis) 처리하여 화면에 표기합니다.
  * 예: `"GLOBAL DREAM EXPRESS"` → `"GLOBAL DREAM EX..."`

- 툴팁 또는 마우스 오버 시 전체 선박명이 표시되도록 처리할 수 있습니다(선택 사항).

- AIS에서 선박명이 수신되지 않을 경우 `"No name"`으로 표시합니다.

UI 파손 또는 정보 누락을 방지하기 위해, 선박명 표기 길이 제한은 시스템 전반에서 일관되게 적용되어야 합니다.


===== 실행 조건

===== 검증 기준
[cols="1,4", options="header"]
|===
| 항목 | 검증 내용

| 기본 정보 표시
| 선택된 Target에 대해 SHIP NAME, CPA, TCPA, DIST, BRG, HDG, COG 값이 단위 및 자리수 기준에 맞게 표기되는지 확인한다.

| 값 미수신 처리
| 각 항목에 대해 수신되지 않은 경우 자리수에 맞는 `-` 기호로 표기되는지 확인한다. 예: `---.--`, `---.-`

| SHIP NAME
| AIS에서 선박명이 제공되지 않는 경우 "No name"으로 표시되는지 확인한다.

| SHIP NAME 길이 초과 처리
| 선박명이 16자를 초과할 경우 말줄임표(...) 처리되어 `"글자수+..."` 형태로 표시되는지 확인한다.

| 정보창 유지
| Target이 선택된 상태에서 화면 이동 또는 Zoom-in하여 화면에서 사라져도 정보창에 계속 해당 Target 정보가 표시되는지 확인한다.

| 선택 해제
| "Clear All" 버튼 클릭 시 모든 물표 선택이 해제되고 정보창이 닫히는지 확인한다. "Unselect" 버튼 클릭 시 해당 Target만 해제되며, 나머지 Target은 유지되고 정보창이 갱신되는지 확인한다.

| 이전/다음 버튼 비활성화
| 단일 Target만 선택된 경우 정보창 하단의 좌/우 버튼이 비활성화되는지 확인한다.

| 값 범위 초과 처리 - DIST
| DIST 값이 99.99nm를 초과할 경우 "99.99+"로 정확히 표기되는지 확인한다.

| 값 범위 초과 처리 - TCPA
| TCPA 값이 1000분 이상 또는 -1000분 이하일 경우 "999.9+", "-999.9-"로 표기되는지 확인한다.

| 값 범위 초과 처리 - CPA
| CPA 값이 1000nm 이상일 경우 "999.99+"로 정확히 표기되는지 확인한다.
|===

===== 성능 요구사항
- Target 선택/해제, 정보창 표시/갱신 작업은 500ms 이내에 UI 반영 완료되어야 함
- 최대 4개의 Selected Target 정보를 동시에 표시할 수 있어야 함
- AIS 수신 유무에 따른 상태 전환 처리 시간은 수신 기준 시간 ±5초 이내 오차 범위로 정확하게 동작해야 함


*선택 대상 처리량*

- 동시에 최대 4개의 Selected Target의 정보를 안정적으로 표시할 수 있어야 하며

- 최대 99개의 Dangerous Target에 대해 라벨링, 순서 유지, 선택 불가 처리 상태를 일관성 있게 유지할 수 있어야 함












===== 인터페이스 요구사항
- 정보창은 좌측 하단 고정 위치에 배치되며, 각 Target의 정보는 표 형태로 정렬되어야 함
- 항목별 단위는 각 값 옆에 소문자 단위로 표시
- Clear All, Unselect, 이전/다음 버튼은 정보창 내 명확한 위치에 배치되어야 하며, 비활성화 시 회색 처리
- AIS 미수신 시 아이콘은 North Up 상태로 회전하고, 1초 간격으로 깜빡이는 애니메이션이 수행되어야 함
- 화면 확대/축소 또는 타 화면 전환 시에도 정보창은 유지되어야 함

===== 기타 요구사항

- AIS 및 ARPA의 데이터 수신 기준은 다음과 같이 정의됨:
** AIS Class A: 3분 30초 이상 수신 없을 경우 Lost 처리
** AIS Class B: 18분 30초 이상 수신 없을 경우 Lost 처리
** ARPA: 40초 이상 수신 없을 경우 Lost 처리
- Lost 상태는 20초간 유지되며, 이후 정보창 및 화면에서 자동 제거됨
- Lost Target은 사용자 선택 대상이 될 수 없음 (선택 불가 처리)
- 시스템은 수신 기준 시간 경과 여부를 실시간으로 모니터링하며 상태 전환을 자동 처리해야 함