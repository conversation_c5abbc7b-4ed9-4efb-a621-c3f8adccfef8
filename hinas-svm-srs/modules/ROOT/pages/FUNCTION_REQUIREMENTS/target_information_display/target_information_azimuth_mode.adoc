==== Azimuth Mode

===== 설명

Azimuth Mode 기능은 Target Information Display에서 사용자가 선박 기준 방향(12시 방향 기준)을 어떻게 설정할지를 결정하는 기능입니다.
총 3가지 모드(North Up, Course Up, Head Up)를 지원하며, 모드 변경 시 화면 내 주요 항목들이 함께 회전합니다.

.Target Information Course Up
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_course_up.png[align="center",600]

* North Up: 북쪽이 항상 12시 방향 (기본값)
* Course Up: 자선의 COG(Course Over Ground)가 12시 방향
* Head Up: 자선의 HDG(Heading)가 12시 방향

모드 변경 시 다음 항목들이 함께 회전됩니다:

- 각도 표시
- North 아이콘
- 자선 모듈 (자선 아이콘, COG 점선, HDG 실선)
- 주변 물표 아이콘
- Prediction Vector (조건부)



*Azimuth Mode API*

1) Azimuth mode 설정 조회

- 기능 설명 : Azimuth mode 설정을 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/preference/azimuth-mode
- Request : 없음
- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "Get preference successful.",
    "data": {
        "azimuth_mode": "NORTH_UP"
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get preference successful.)
+
data : Azimuth mode 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| azimuth_mode | string | 방위각 모드 (NORTH_UP, COURSE_UP, HEAD_UP 중 하나)
|===


2) Azimuth mode 설정 변경

- 기능 설명 : Azimuth mode 설정을 변경한다.
- HTTP Method : PUT
- Endpoint : /svm-api/v1/preference/azimuth-mode
- Request :

+
[source,json]
----
{
    "azimuth_mode": "NORTH_UP"
}
----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| azimuth_mode | string | 방위각 모드 (NORTH_UP, COURSE_UP, HEAD_UP 중 하나) | required, "NORTH_UP", "COURSE_UP", "HEAD_UP" 중 하나
|===
- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "update preference successful.",
    "data": {
        "azimuth_mode": "COURSE_UP"
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (update preference successful.)
+
data : Azimuth mode 설정 정보


===== 실행 조건

- 시스템 구동 시 기본 Azimuth Mode는 `North Up`으로 설정됨
- Azimuth Mode 변경 버튼을 통해 모드 순환: `North Up → Course Up → Head Up → North Up`
- 모드 변경 시 회전 대상:
  - 각도값
  - North 아이콘
  - 자선 모듈 (자선 아이콘, COG 점선, HDG 실선)
  - 물표 아이콘
  - 자선 Prediction Vector

- 마지막 사용한 모드는 클라이언트 재접속 시 유지됨
- AIS/ARPA 초기 설정값:
  * Azimuth Mode: North Up
  * Range Scale: 6 NM
  * Range Ring: 3개 On 상태



===== 검증 기준

- 버튼 클릭 시 Azimuth Mode가 정확히 순차 순환되어야 함
- 각 모드의 기준 방향은 다음과 같아야 함:
  * North Up: 북쪽
  * Course Up: 자선의 COG 점선이 12시 방향
  * Head Up: 자선의 HDG 실선이 12시 방향

- 모드 전환 시 화면 내 회전 요소가 정확히 반영되어야 함
** 각도 표시
** North 아이콘
** 자선 모듈 (자선 아이콘, COG 점선, HDG 실선)
** 주변 물표 아이콘
** Prediction Vector (조건부)

- 모드 전환시 Lost Target의 경우 항상 진북방향을 바라보고 있어야 함.

===== 성능 요구사항

*화면 UI 반영 시간*

- Azimuth Mode 변경 후 UI 반영까지 500ms 이내
- 모드 변경 시 화면 회전 반응은 300ms 이내


*API 응답 시간*

- 설정 조회(GET) 요청에 대한 평균 응답 시간은 200ms 이내
- 설정 변경(PUT) 요청은 200ms 이내에 완료되어야 함

===== 인터페이스 요구사항

- Azimuth Mode 변경 버튼은 UI 내 상단 또는 우측 위치
- 현재 Azimuth Mode는 버튼 상태 또는 별도 텍스트로 명시


===== 기타 요구사항

*UI 시인성*

- Azimuth Mode 버튼은 **사용자가 최소 1m 거리에서 화면을 관찰할 때 명확히 인지**할 수 있도록 설계되어야 합니다.

*설정 영속성*

- 모든 설정 값은 시스템 재시작 이후에도 유지되어야 함
- Storage Module에 지속적으로 저장되어야 하며, 손실되지 않아야 함