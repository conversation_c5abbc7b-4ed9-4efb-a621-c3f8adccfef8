==== Target Data Streaming

===== 설명
본 기능은 자선 외 타선(Target Ship) 데이터를 실시간으로 스트리밍하는 기능이다. 클라이언트는 아래 두 네임스페이스 중 하나에 WebSocket으로 연결할 수 있으며, 서버는 연결 시 백그라운드 태스크를 시작하여 Redis에서 주기적으로 데이터를 조회하고 가공 후 클라이언트에 전송한다.

1. websocket 접속 namespace

- `/svm/target-display`: 현재 탐지된 타선 데이터 제공

- `/svm/lost-target-display`: 탐지 후 추적이 끊긴 타선 데이터 제공

2. source data redis key (svm redis)

- `svm:target`: 현재 탐지된 타선 목록

- `svm:lost_target`: 추적 손실된 타선 목록

전송되는 데이터 항목은 다음과 같다:

[cols="1,2", options="header"]
|===
| 필드 | 설명

| sensor_fusion_issued_time | 타선 정보 기준 UTC 시각
| targets | 탐지된 타선의 리스트. 각 항목은 TargetShip으로 구성
|===

TargetShip에는 다음 정보가 포함된다:

[cols="1,2", options="header"]
|===
| 필드 | 설명

| fusion_type | 센서 융합 타입. 1: AIS, 2: Radar, 3: AIS+Radar 등
| ship_name | 타선 선명 (예: "ORKIM_PEARL")
| target_mmsi | 타선 MMSI 번호 (AIS 기반인 경우)
| target_id | 고유 타선 식별자
| ship_type | 선박 타입 (1~99)
| navigation_status | 항해 상태 코드
| target_distance | 자선과 타선 간 거리 (단위: nm)
| target_cpa | CPA (Closest Point of Approach), 단위: nm
| target_tcpa | TCPA (Time to CPA), 단위: 분(min)
| target_heading | 타선의 침로 (도, degree)
| current_position | 타선의 현재 위도/경도 (latitude, longitude)
| prediction_position | 타선의 예측 위치 (latitude, longitude, distance, bearing)
| rot | 회전률 (Rate of Turn), 단위: 도/분(deg/min)
| target_risk | 위험도 (0: green, 1: yellow, 2: red)
| congested_target_risk | 혼잡 상황 시 위험도 (0: green, 1: yellow, 2: red)
| cog | 타선의 COG (도)
| sog | 타선의 SOG (knots)
| target_bearing | 자선 기준 타선의 방위각 (도, -180 ~ 180)
| image_position | 타선을 감지한 이미지 좌표 목록. 각 항목은 cam_name, x, y로 구성. (x, y가 null인 경우는 제외)
|===

===== 실행 조건

- Redis 인스턴스가 정상적으로 실행 중이어야 함

- svm:target, svm:lost_target 키가 Redis에 주기적으로 업데이트되고 있어야 함

===== 검증 기준

*실시간 데이터 수신*

- 클라이언트가 해당 네임스페이스에 연결된 이후 0.1초 간격으로 데이터 이벤트를 수신해야 함

*데이터 정합성*

- 각 타선 데이터는 TargetDisplaySchema 구조를 따르며, 필수 필드 누락 없이 전달되어야 함

*예외 응답 처리*

다음 상황에 따라 지정된 이벤트가 전송되어야 함.

- Redis에 데이터 없음 → no_signal_error

- Redis 또는 네트워크 오류 → server_error



===== 성능 요구사항

*전송 주기 정확도*

- 0.1초 ±30ms 이내 간격으로 WebSocket 이벤트가 전송되어야 함

*연속 오류 대응*

- Redis 오류 또는 NoSignal 상태 발생 시에도 이벤트는 중단되지 않고 주기적으로 오류 상태가 전송되어야 함


===== 인터페이스 요구사항

[cols="1,3", options="header"]
|===
| 인터페이스 | 설명

| WebSocket Client → /svm/target-display | 현재 탐지된 타선 목록을 수신할 때 연결하는 네임스페이스

| WebSocket Client → /svm/lost-target-display | 추적이 끊긴 타선 목록을 수신할 때 연결하는 네임스페이스

| Redis Key(svm redis) : svm:target | 현재 타선 목록이 저장된 Redis 키

| Redis Key(svm redis) : svm:lost_target | 추적 손실된 타선 목록이 저장된 Redis 키
|===

===== 기타 요구사항


*연결 유지*

- 클라이언트가 연결을 종료하지 않는 한 서버는 지속적으로 데이터를 전송해야 함