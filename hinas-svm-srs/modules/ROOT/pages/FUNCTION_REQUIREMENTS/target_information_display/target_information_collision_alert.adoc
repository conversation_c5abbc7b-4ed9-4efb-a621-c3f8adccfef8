==== Target Information Display Collision Alert

===== 설명
Collision Alert 기능은 선박 주변의 위험 선박(Dangerous Target) 수가 설정된 임계값(Collision Alarm Threshold)을 초과할 경우 경고를 발생시키는 기능이다.
운항자는 이 경고를 통해 즉각적인 위험 상황을 인지하고 대응할 수 있으며, 경고는 타선 정보창, Side View, Surround View 영역에 시각적으로 표시됩니다.


.Target Information Display Collision Alert
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_collision_alert.png[align="center",700,300]

===== 실행 조건
- Dangerous Target 수 ≥ Collision Alarm Threshold일 경우 Collision Alert 발생
- Dangerous Target 수 < Collision Alarm Threshold일 경우 Collision Alert 자동 해제
- Collision Alarm Threshold 값은 현재 운항 모드에 따라 결정됨:
  - Open Sea 모드: 높은 임계값
  - Congested Area 모드: 낮은 임계값
- Collision Alarm Threshold 값의 설정 범위는 1 이상 99 이하

===== 검증 기준
- Dangerous Target 수가 Threshold 이상일 때 다음 조건이 충족되어야 함:
  - 타선 정보창 상단에 텍스트 "Collision Alert: Targets at risk exceed threshold({threshold})" 표시
  - Side View 상단에 "{위험 선박 수} Danger" 텍스트가 붉은색으로 깜빡이며 표시됨
  - Surround View의 AIS 전환 아이콘에 붉은 점 표시
- Dangerous Target 수가 Threshold 미만으로 떨어질 경우, 위 경고 표시는 즉시 제거되어야 함
- 운항 모드 전환(Open Sea ↔ Congested) 시 threshold 값이 즉시 반영되어야 함

===== 성능 요구사항
- Dangerous Target 수의 변화 및 운항 모드 전환 시, 1초 이내로 Collision Alert UI가 반영되어야 함
- 깜빡임 효과는 1초 간격으로 0.5초 ON / 0.5초 OFF 주기로 안정적으로 반복되어야 함
- 최대 99개의 Dangerous Target이 존재하더라도 시스템이 정상적으로 경고를 발생시켜야 함

===== 인터페이스 요구사항
- 타선 정보창 상단 텍스트:
** 위치: 타선 정보창 헤더
** 형식: "Collision Alert: Targets at risk exceed threshold({threshold})"
** 색상: 경고 색상(Red 계열), 깜빡임 효과 포함
- Side View 텍스트:
** 위치: Side View 오른쪽 상단
** 형식: "{n} Danger"
** 스타일: 빨간색 배경, 흰색 텍스트, 깜빡임 효과
- Surround View:
** AIS 전환 아이콘에 붉은 점 표시
** 붉은 점은 Collision Alert가 존재할 때에만 표시

===== 기타 요구사항
- Collision Alert는 사용자가 별도로 끄거나 수동으로 해제할 수 없음
- Dangerous Target 수는 내부 알고리즘에 따라 자동 판단되며, 사용자 수정 불가
- Collision Alarm Threshold는 시스템 설정값이나 운항 모드 변경에 따라 자동 설정되며 UI를 통해 직접 조정 불가
