==== Range Ring

===== 설명

Range Ring 기능은 사용자가 범위 표시 상태를 제어하고, 적절한 Range Scale 값을 설정함으로써 주변 타겟의 위치를 시각적으로 파악할 수 있도록 지원합니다.

.Target Information Range Ring
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_range_ring.png[align="center",600]

*Range Ring 현시 ON/OFF*

- 메뉴 버튼을 통해 Range Ring의 표시 여부를 설정할 수 있습니다.
- ON 상태에서는 설정된 Range Scale에 따라 Ring이 화면에 표시됩니다.
- OFF 상태에서는 Ring이 숨겨지며, 타겟 위치 정보만 유지됩니다.

*Range Ring ON/OFF 설정 API*

1) Range Ring ON/OFF  설정 조회

- 기능 설명 : Range Ring ON/OFF  설정을 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/preference/target-range-status-setting
- Request : 없음
- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "Get preference successful.",
    "data": {
        "is_surround_range_ring_on": false
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get preference successful.)
+
data : Range Ring ON/OFF 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| is_surround_range_ring_on | boolean | 주변 선박 탐지 범위 링 표현 여부 (true: 활성화, false: 비활성화)
|===

2) Range Ring ON/OFF  설정 변경

- 기능 설명 : Range Ring ON/OFF 설정을 변경한다.
- HTTP Method : PUT
- Endpoint : /svm-api/v1/preference/target-range-status-setting
- Request :
+
[source,json]
----
{
    "is_surround_range_ring_on": true
}
----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| is_surround_range_ring_on | boolean | 주변 선박 탐지 범위 링 표현 여부 (true: 활성화, false: 비활성화) | required, true or false
|===

- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "update preference successful.",
    "data": {
        "is_surround_range_ring_on": true
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (update preference successful.)
+
data : Range Ring ON/OFF  설정 정보



*Range Scale 조정*

- "+" 버튼을 눌러 Range Scale 값을 증가시킬 수 있고, "-" 버튼을 눌러 감소시킬 수 있습니다.
- 현재 설정된 Range Scale 및 Range Interval은 화면 좌측 하단에 표시됩니다.
** 상단: Range Scale
** 하단: Range Interval
- 가장 최근에 사용한 Range Scale 값은 로컬 설정으로 저장되어, 다음 부팅 시 자동으로 반영됩니다.

*기본값 및 동작 조건*

- 시스템 초기화 시 기본값은 다음과 같습니다:
** Range Scale: 6
** Range Interval: 2
** Ring Number: 3
- Ring 변경 시 별도의 애니메이션은 제공되지 않습니다.


*범위를 초과한 타겟 표시*

- Range Scale을 초과하는 거리의 selected target 또는 dangerous target은 화면 경계에 방향 화살표로 계속 표시됩니다.
- 타겟이 범위를 벗어나도 정보는 사라지지 않으며, 위치 각도를 기준으로 외곽 표시기에 지속적으로 나타납니다.


[cols="1,1,1", options="header"]
|===
| Range Scale | Range Interval | Ring Number

| 0.5 | 0.125 | 4
| 0.75 | 0.25 | 3
| 1.5 | 0.5 | 3
| 3 | 1 | 3
| 6 | 2 | 3
| 12 | 3 | 4
| 18 | 6 | 3
|===

*Range Scale 설정 API*

1) Range scale 설정 조회

- 기능 설명 : Range scale 설정을 조회한다.
- HTTP Method : GET
- Endpoint : /svm-api/v1/preference/target-range-setting
- Request : 없음
- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "Get preference successful.",
    "data": {
        "scale": 6,
        "interval": 2,
        "ring_count": 3
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (Get preference successful.)
+
data : Range Scale 설정 정보
+
[cols="1,1,3", options="header"]
|===
| 필드명 | 타입 | 설명
| scale | integer | 탐지 범위의 스케일
| interval | integer | 탐지 범위 간격
| ring_count | integer | 탐지 범위 링 개수
|===



2) Range scale 설정 변경

- 기능 설명 : Range scale 설정을 변경한다.
- HTTP Method : PUT
- Endpoint : /svm-api/v1/preference/target-range-setting
- Request :
+
[source,json]
----
{
    "scale": 6,
    "interval": 2,
    "ring_count": 3
}
----
- 요청 필드 설명:
+
[cols="1,1,3,3", options="header"]
|===
| 필드명 | 타입 | 설명 | 제약사항
| scale | integer | 탐지 범위의 스케일 | required, 0.5 ~ 18
| interval | integer | 탐지 범위 간격 | required, 0.125 ~ 6
| ring_count | integer | 탐지 범위 링 개수 | required, 3 ~ 4
|===
- Response :
+
[source,json]
----
{
    "code": 200000,
    "message": "update preference successful.",
    "data": {
        "scale": 0.5,
        "interval": 0.125,
        "ring_count": 4
    }
}
----
- 응답 필드 설명:
+
code : 응답 코드 (200000)
+
message : 응답 메시지 (update preference successful.)
+
data : Range Scale 설정 정보




===== 실행 조건
- Target 정보가 AIS 또는 ARPA 데이터로 수신되고 있어야 함

===== 검증 기준

- 메뉴 버튼을 통해 Range Ring의 On/Off 동작이 즉시 반영되는지 확인
- "+" 및 "-" 버튼 클릭 시 정해진 Range Scale 목록 내에서 정확하게 증감되는지 확인
- UI 좌측 하단에 Range Scale 및 Interval이 정확하게 표기되는지 검증
- Range Ring 외부에 있는 selected/dangerous target이 화살표 형태로 외곽에 올바르게 표시되는지 확인

===== 성능 요구사항

*화면 UI 반영 시간*

- Range Ring의 On/Off 상태 변경은 300ms 이내에 반영되어야 합니다.
- Range Scale 변경 시, 화면 갱신은 400ms 이내에 완료되어야 합니다.
- 사용자가 Range Scale을 연속적으로 조작하더라도, UI는 각 입력에 대해 500ms 이내에 안정적으로 반영되어야 합니다.

===== 인터페이스 요구사항

1. 프로토콜 및 형식
- 모든 API 통신은 HTTP/1.1 또는 HTTP/2를 사용함
- 요청 및 응답 데이터는 JSON 형식으로 주고받아야 함
- Content-Type은 `application/json`으로 지정해야 함

2. 응답 메시지 구조
- 모든 응답은 아래 구조를 따라야 함:
+
[source,json]
----
{
  "code": 200000,
  "message": "설명 메시지",
  "data": { ... }
}
----
- code: 표준 응답 코드 (성공: 200000, 실패: 400/500 등)
- message: 성공/실패에 대한 간단한 메시지
- data: 실제 응답 내용 (object 또는 null)


===== 기타 요구사항

*UI 시인성*

- Range Ring 및 외곽 화살표(방향 표시)는 **사용자가 최소 1m 거리에서 화면을 관찰할 때 명확히 인지**할 수 있도록 설계되어야 합니다.
- 사용자가 선박 조타실 또는 운항 위치에서 일반적인 시야각(약 30~40도)으로 바라보는 조건에서도,
  Ring의 선 굵기, 색상 대비, 외곽 화살표의 크기 및 방향 각도 등은 시각적 혼동 없이 식별 가능해야 합니다.
- **Range Ring의 색상 대비, 굵기등은 주간과 야간, 그리고 다양한 밝기 조건에서도 시인성을 유지**해야 하며, 국제 해사 표준(예: IMO HMI 지침)을 준수하는 색상 대비가 확보되어야 합니다.

*설정 영속성*

- 모든 설정 값은 시스템 재시작 이후에도 유지되어야 함
- Storage Module에 지속적으로 저장되어야 하며, 손실되지 않아야 함