==== Dangerous Target

===== 설명

본 기능은 HiNAS SVM 시스템에서 자선 주변의 충돌 위험 선박(Dangerous Target)을 자동 식별하여 시각적으로 강조하고, 번호(label)를 부여하여 관리할 수 있도록 지원합니다.
Dangerous Target은 충돌 위험 판단 로직에 따라 자동으로 식별되며, 최대 99개까지 등록 가능합니다.
해당 선박은 일반 선박 아이콘과 달리 시각적으로 강조된 아이콘으로 표시되며, 레이블 번호와 함께 UI상에 표기됩니다.

.Dangerous Target
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_range_ring.png[align="center",600]

하단 정보 패널에는 현재 가장 최근에 위험으로 분류된 선박의 상세 정보가 표시됩니다.
이 정보 패널은 좌우 탐색 버튼을 통해 Dangerous Target 리스트를 순차적으로 탐색할 수 있습니다.

===== 실행 조건
* 충돌 위험 판단 로직(CPA, TCPA 기준 등)에 따라 선박이 Dangerous Target으로 자동 분류될 경우
* 최대 99개의 Dangerous Target을 동시에 관리 가능
* 이미 존재하던 Dangerous Target이 삭제되면, 이후 추가되는 선박은 삭제된 번호를 우선 할당받음
* Dangerous Target의 알림은 자동으로 발행되며, 사용자에 의해 수동 해제 불가능함

===== 검증 기준
* 신규 위험 선박이 자동으로 Dangerous Target으로 등록되고, 아이콘 및 라벨 번호가 부여되는지 확인
* 기존 라벨 번호(1~99) 중 삭제된 번호가 있을 경우, 가장 작은 비어있는 번호가 신규 선박에 부여되는지 확인
* 삭제된 Dangerous Target을 제외한 나머지 라벨 번호가 유지되는지 확인
* 하단 정보 패널에 가장 최근에 등록된 Dangerous Target의 상세 정보가 정확히 표시되는지 확인:
** SHIP NAME
** CPA
** TCPA
** BRG
** HDG
** COG
* 좌우 탐색 버튼을 통해 다른 Dangerous Target 정보로 정상 이동되는지 확인
* 사용자가 마우스 우클릭으로 Dangerous Target 알림을 해제할 수 없는지 확인

===== 성능 요구사항

*UI 렌더링*

* Dangerous Target의 등록/삭제 시 UI 반영은 500ms 이내에 완료되어야 함
* 99개 Dangerous Target에 대한 실시간 관리 처리 성능을 확보 해야 함.
* 99개 Dangerous Target가 렌더링된 상태에서, Range Ring, Prediction Vector을 조절하였을 때, 500ms 이내에 UI가 반영되어야 함

*리소스 사용 제한*

- 렌더링 처리 중 GPU 사용률은 60% 이하, CPU 사용률은 70% 이하로 유지되어야 함
- 렌더링 처리 중 발생하는 Peak Memory 사용량은 1GB 미만으로 제한하고, 메모리 누수 없이 반복 처리 가능해야 함
- GC(가비지 컬렉션)으로 인한 렌더링 지연이 100ms 이상 발생하지 않아야 하며, **메모리 압박 상황에서도 프레임 정지 없이 운영** 가능해야 함

*장시간 연속 렌더링 조건*

- Dangerous Target 99개 상태를 4시간 이상 유지하며 매 초마다 상태를 변경하는 스트레스 테스트를 수행할 경우에도, 프레임 드랍, 렌더링 지연, UI 렉 현상이 발생하지 않아야 하며, **CPU/메모리 누적 사용량 증가는 5% 이내로 제한**되어야 함

===== 인터페이스 요구사항

* Dangerous Target 아이콘은 일반 선박과 시각적으로 구분되도록 별도 크기 및 색상 적용 (Icon List 참조)
* Dangerous Target 번호는 아이콘 우측 상단 또는 적절한 위치에 명확히 표기
* 정보 패널 구성 항목:
** SHIP NAME
** CPA (Closest Point of Approach)
** TCPA (Time to CPA)
** BRG (Bearing)
** HDG (Heading)
** COG (Course Over Ground)
* 좌우 화살표를 통한 Dangerous Target 전환 기능 제공
* SVM REC 상태, Selector 버튼과의 UI 간섭 없이 동작해야 함



===== 기타 요구사항

*Dangerous Target 번호 규칙 검증*

** 기존 라벨이 없는 경우 1부터 순차적으로 부여
** 중간 번호가 삭제된 경우, 가장 작은 비어 있는 번호가 우선적으로 재사용됨
* Dangerous Target은 자동 판단 및 알림 기능에 의해 생성되며, 사용자가 직접 생성/수정/해제할 수 없음
* 가장 최근에 발행된 Dangerous Target이 항상 정보 패널의 기본 표시 대상이 되어야 함

*UI 시인성*

- Dangerous Target Icon, 번호, 정보등은 **사용자가 최소 1m 거리에서 화면을 관찰할 때 명확히 인지**할 수 있도록 설계되어야 합니다.
- 사용자가 선박 조타실 또는 운항 위치에서 일반적인 시야각(약 30~40도)으로 바라보는 조건에서도,
  Dangerous Target Icon, 번호 등은 시각적 혼동 없이 식별 가능해야 합니다.
- ** Dangerous Target Icon, 번호 다양한 밝기 조건에서도 시인성을 유지**해야 하며, 국제 해사 표준(예: IMO HMI 지침)을 준수하는 색상 대비가 확보되어야 합니다.
