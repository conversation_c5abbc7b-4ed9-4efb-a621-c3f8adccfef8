==== Target Information Display

===== 설명
해당 챕터에서는 Target Information Display 기능과 화면 구성에 대해서 설명합니다.

전반적인 화면 구성 및 AIS + ARPA 조합에 따라서 화면 아이콘이 어떻게 현시되는지에 대한 설명이 포함되어 있습니다.


Target Information Display에서의 화면 구성과 각 요소의 기능은 다음과 같습니다.

.Target Information Display 화면 구성 요소
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_explain_button.png[align="center",600]

.Target Information Display 방위각 표기
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_north_up.png[align="center",600]

.Target Information Display 물표 아이콘
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_target_icon.png[align="center",600]


센서의 종류에 따라 Fusion Icon은 다음과 같이 표현됩니다.

*AIS Fusion Icon*

.AIS Fusion Icon
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_ais_icon.png[align="center",20]


*AIS & ARPA Fusion Icon*

.AIS & ARPA Fusion Icon
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_ais_icon.png[align="center",20]


*ARPA Fusion Icon*

.ARPA Fusion Icon
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_arpa_icon.png[align="center",20]

아이콘은 아래 조건에 따라서 변화되어 표시됩니다.

- 타겟 유형 : AIS, ARPA, AIS + ARPA
- 타겟 상태 : 정상, 경고, 위험
- COG/SOG 정보 유무 : COG/SOG 정보가 있는 경우와 없는 경우
- 타겟 선택됨 : 타겟이 선택된 경우와 선택되지 않은 경우
- 위험 경고 : 위험 경고가 있는 경우와 없는 경우
- 타겟 소실 : 타겟이 소실된 경우와 소실되지 않은 경우

.Target Information Display Fusion Icon Brief
image::FUNCTION_REQUIREMENTS/target_information_display/target_information_display_icon_brief.png[align="center",700]

- Icon은 타선의 heading을 고려하여 heading 방향으로 아이콘 표기됩니다.
- 아이콘 방향 표기를 위한 heading, COG 값이 없는 경우 진북 방향으로 표기됩니다.
- Lost Target은 진북 방향으로 표기되며, 깜빡임 효과가 발생합니다.


**Target Icon Logic Diagram**

https://www.figma.com/design/KRxrOCqd2RUIt7WA8ouwPz/SVM-v1.0.0---24.-10.-31-?node-id=32-53983&t=oqHcZ6gDlhwlBRM0-4[SVM SRS Figma Target Information Display] 에서, 4. Icon Logic Diagram을 참고하여 Target Icon의 로직을 확인할 수 있습니다.




===== 실행 조건
- 해당 사항 없음.


===== 검증 기준

- 센서에 종류에 따라 Fusion Icon이 정상적으로 표시되어야 함.

====== Sensor Fusion Icon 현시


*데이터 인지 타이밍*

시스템은 VDR로부터 수신된 센서 데이터를 기반으로 Fusion Icon을 구성하며, 시스템 기동 후 초기 2분간 수신된 데이터를 기준으로 첫 인식 및 초기 매핑을 수행합니다.

*센서 변경 및 실시간 반영 여부*

기동 이후에도 센서 데이터에 실시간 변경 사항이 발생하는 경우, 시스템은 해당 변경을 즉시 반영하여 Fusion Icon 상태를 갱신해야 합니다.

예를 들어, AIS 신호가 중간에 수신되기 시작하면 AIS Fusion Icon을 추가로 표시하며, ARPA 데이터가 끊기면 ARPA Fusion Icon은 제거되어야 합니다.

AIS + ARPA Fusion 상황에서, ARPA 데이터가 끊기면 ARPA Fusion Icon은 제거되지만, AIS Fusion Icon은 계속 표시되어야 합니다.


*데이터 유형에 따른 Icon 표시 기준*

- AIS 데이터, ARPA 데이터, AIS + ARPA 데이터가 수신되는 경우 각각의 Fusion Icon이 정상적으로 표시되어야 합니다.


===== 성능 요구사항
- 해당 사항 없음.

===== 인터페이스 요구사항
- 해당 사항 없음.

===== 기타 요구사항
- 해당 사항 없음.