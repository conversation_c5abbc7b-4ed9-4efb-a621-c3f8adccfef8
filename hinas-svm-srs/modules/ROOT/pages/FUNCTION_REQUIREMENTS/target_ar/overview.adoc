=== Target AR

==== Overview

===== Terminology
- Target AR 챕터에서는 이전 Target Information Display 챕터와 Surround View, Side View 챕터에서 사용한 용어를 재사용합니다.

===== Table of Contents
[cols="1,1,4", options="header"]
|===
| No.
| Function
| Description

| 1
| Target AR
| Target Information Display에서 선택된 타선 정보를 Surround View 및 Side View에 AR 아이콘 형태로 표시하는 기능
|===


본 문서는 HiNAS SVM의 Target 기능에 대한 요구사항을 정의합니다.
본 챕터에서는 다음 내용을 순차적으로 설명합니다.



===== Target AR 범위 및 의존성

*SVM Backend*

- SVM Frontend API 연동하여 설정 값 저장
- Target 관련 정보를 WebSocket을 통해 클라이언트로 전송
- Dangerous Target 판단 로직, Prediction Vector 생성 등의 판단 기능 구현


*Sensor Fusion*

- AIS, ARPA, Radar 등 다양한 센서 데이터를 융합하여 타선(Target) 리스트 생성
- Fusion 결과를 기반으로 통합된 Target 데이터 구조 제공

*Core Redis*

- VDR(Vessel Data Recorder)로부터 UDP 6501 포트로 수신된 센서 데이터를 NMEA 프로토콜 기반으로 파싱하여,
  자선 및 AIS, ARPA 관련 정보를 Core Redis에 저장.

*SVM Redis*

- Sensor Fusion에서 정제된 자선과 타선의 예측 위치 정보를 저장하는 Redis 인스턴스


*SVM Frontend*

- SVM Backend로부터 수신한 데이터를 기반으로 Target Information Display UI를 구성하며,
  사용자가 주변 상황을 직관적으로 인식할 수 있도록 시각화합니다.




<<<
include::target_ar.adoc[]