==== Target AR

===== 설명

Target Information Display에서 select 되어 타선 정보가 표시되고 있는 1개의 선박을 AR 아이콘 형태로 Surround View와 Side View에 현시해주는 기능이다.

.Target AR
image::SYSTEM_OVERVIEW/system_overview_target_ar_1.png[align="center",600]


https://www.figma.com/design/KRxrOCqd2RUIt7WA8ouwPz/SVM-v1.0.0---24.-10.-31-?node-id=3131-129771&m=dev[Target AR Figma Link]

1) **Target selection**: Target Information Display View에서 사용자가 원하는 타선을 선택한다.

2) **AR 아이콘 표시**: 선택된 타선은 Surround View 및 Side View에서 AR 아이콘 형태로 위치에 따라 시각적으로 표시됩니다. 선택된 타선이 다중 Side View 영역에 포함된 경우, 해당 타선이 표시되는 모든 화면에 AR 아이콘이 동시에 나타나야 한다.

===== 실행 조건

- Target Information Display에서 선택된 타선 정보가 존재해야 함
- 선택된 타선의 위치 정보가 실시간으로 Redis를 통해 수신되어야 함
- Surround View, Side View 화면이 정상 출력 중이어야 함

===== 검증 기준

[cols="1,3", options="header"]
|===
| 항목 | 검증 기준
| AR 아이콘 표시 | 타선 선택 시 Surround View 및 Side View에서 이미지 상 타선 위치에 AR 아이콘이 표시되어야 함
| View 동기화 | 두 View 모두에서 동일한 타선에 대해 동일한 AR 표시가 동기화되어야 함
| Side View 다중 표시 | 선택된 타선이 포함된 모든 Side View 화면에 AR 아이콘이 동시에 표시되어야 함
| 회전 및 확대 시 위치 추적 | Surround, Side View에서 회전 또는 줌 상태에서도 AR 아이콘이 타선 위치를 따라 정확히 이동해야 함
| 선택 해제 시 제거 | Target deselect 시 AR 아이콘이 즉시 사라져야 함
|===

===== 성능 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 요구사항
| AR 표시 반응 속도 | 타선 선택 후 300ms 이내에 AR 아이콘이 표시되어야 함
| 위치 갱신 주기 | AR 아이콘의 위치는 타선 정보 갱신 주기와 동기화되어야 함
|===

===== 인터페이스 요구사항

[cols="1,3", options="header"]
|===
| 인터페이스 | 설명
| 사용자 입력 | Target Information Display UI 상에서의 타선 선택 이벤트
|===

===== 기타 요구사항

[cols="1,3", options="header"]
|===
| 항목 | 설명
| View 연동 | AR 아이콘은 Surround View 및 Side View 모두에 동시에 적용되어야 함
| 상태 유지 | 타선 선택 상태는 사용자가 명시적으로 해제하기 전까지 유지되어야 함
| 위치 추적 정확도 | AR 아이콘 위치는 화면 상 선박 위치와 시각적으로 일치해야 함 (±50px 이내 오차)
| 다중 View 반영 | 하나의 타선이 여러 Side View에 표시될 경우, 각 View 모두에 동일하게 반영되어야 함
|===
