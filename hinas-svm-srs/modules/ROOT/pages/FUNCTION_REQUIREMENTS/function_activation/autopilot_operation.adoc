==== Autopilot Operation

===== 설명

Autopilot Operation은 Route Tracking 기능에 필요한 자동조타장치(Autopilot)의 연동 및 활성화 절차를 다룹니다. HiNAS는 다음 모델들과 인터페이스할 수 있습니다:

* TOKYO KEIKI PR-9000
* YOKOGAWA PT900

각 Autopilot 모델은 고유한 구성과 활성화 절차를 가지고 있습니다.

====== TOKYO KEIKI PR-9000 활성화 절차

1. Route Tracking 스위치를 토글 ON 합니다
2. PR-9000의 heading controller display panel에 `E.HC RDY`가 표시됩니다
3. heading controller의 `TC/HC` 키를 1-2초간 누르고 유지합니다
4. HiNAS가 제어권을 가져오면 display가 `E.HC RDY`에서 `EXT.HC`로 변경됩니다

====== YOKOGAWA PT900 활성화 절차

1. Route Tracking 스위치를 토글 ON 합니다
2. PT900이 자동으로 `AUTO`에서 `NAS-HC`로 변경되며 음향 알람이 울립니다

===== 실행조건

Autopilot Operation의 정상 동작을 위한 실행 조건은 다음과 같습니다:

====== 필수 조건

* *Autopilot 시스템 연결*: TOKYO KEIKI PR-9000 또는 YOKOGAWA PT900이 HiNAS와 물리적으로 연결되어 있어야 합니다
* *Autopilot 전원 공급*: 해당 Autopilot 시스템에 정상적인 전원이 공급되어야 합니다
* *Autopilot 정상 동작 여부*: 해당 Autopilot 시스템에 선박의 센서 시스템으로부터 다음 센서 데이터가 정상적으로 입력되어야 합니다
  - 필수 센서 데이터: Heading(선수방향), ROT(Rate of Turn, 회전율)
  - 선택적 센서 데이터: SOG(Speed over Ground, 대지속도), STW(Speed through Water, 대수속도)
* *Autopilot Heading Control 모드*: 해당 Autopilot이 Heading Control(HC) 모드로 설정되어 있어야 합니다(PT900은 AUTO 모드)

====== TOKYO KEIKI PR-9000 특정 조건

* *Heading Controller 정상 동작*: PR-9000의 Heading Controller가 정상적으로 동작해야 합니다
* *Display Panel 정상*: Heading Controller Display Panel이 정상적으로 표시되어야 합니다
* *TC/HC 키 정상*: TC/HC 키가 물리적으로 정상 동작해야 합니다

====== YOKOGAWA PT900 특정 조건

* *AUTO 모드 준비*: PT900이 AUTO 모드에서 정상 동작 중이어야 합니다
* *Display Panel 정상*: Heading Controller Display Panel이 정상적으로 표시되어야 합니다
* *음향 시스템 정상*: 모드 전환 시 음향 알람이 정상 동작해야 합니다

===== 검증기준

Autopilot Operation의 검증 기준은 다음과 같습니다:

====== TOKYO KEIKI PR-9000 검증

* *E.HC RDY 표시*: Route Tracking 스위치 토글 후 heading controller display panel에 `E.HC RDY` 표시 확인
* *TC/HC 키 응답*: TC/HC 키를 1-2초간 누를 때 정상적인 응답 확인
* *EXT.HC 전환*: TC/HC 키 조작 후 display가 `E.HC RDY`에서 `EXT.HC`로 정상 전환 확인
* *HiNAS 제어권*: HiNAS가 Autopilot 제어권을 정상적으로 획득했는지 확인

====== YOKOGAWA PT900 검증

* *자동 전환*: Route Tracking 스위치 토글 후 자동으로 `AUTO`에서 `NAS-HC`로 전환 확인
* *음향 알람*: 모드 전환 시 음향 알람이 정상적으로 울리는지 확인
* *Display 표시*: Heading Controller Display Panel에 `NAS-HC` 상태가 정확히 표시되는지 확인
* *HiNAS 제어권*: HiNAS가 Autopilot 제어권을 정상적으로 획득했는지 확인

====== 공통 검증

* *통신 연결*: HiNAS와 Autopilot 간 통신이 정상적으로 연결되어 있는지 확인
* *상태 동기화*: Autopilot 상태가 HiNAS 시스템에 정확히 반영되는지 확인
* *제어 응답*: HiNAS의 제어 명령에 Autopilot이 정상적으로 응답하는지 확인

===== 성능 요구사항

====== 응답 성능

* *E.HC RDY 표시 시간*: Route Tracking 스위치 토글 후 3초 이내 표시
* *모드 전환 시간*: TC/HC 키 조작 후 5초 이내 EXT.HC 전환 (PR-9000)
* *자동 전환 시간*: Route Tracking 스위치 토글 후 3초 이내 NAS-HC 전환 (PT900)
* *제어 응답 시간*: HiNAS 제어 명령 후 2초 이내 Autopilot 응답

====== 정확성 요구사항

* *직진 모드 제어 정확도*: HiNAS Control이 보내는 방향 제어 값과 Autopilot의 heading command가 일치해야 함(소수점1자리 까지 )
* *터닝 모드 제어 정확도*: HiNAS Control이 보내는 ROT 커맨드와 Autopilot의 ROT 커맨드가 일치해야 함 (소수점 1자리 까지)
* *통신 지연*: 통신 딜레이 3초 이내

====== 장애 대응 요구사항

* *자동 비활성화*: 일시적 장애 시 HiNAS Control 제어 시스템 자동 de-activate
* *복구 후 활성화*: 복구 시 수동 또는 자동 activate가 가능해야 함

===== 인터페이스 요구사항

====== 하드웨어 인터페이스

* *TOKYO KEIKI PR-9000*: 
  - Heading Controller Unit과의 물리적 연결
  - Display Panel 인터페이스
  - TC/HC 키 인터페이스

* *YOKOGAWA PT900*:
  - Heading Controller Display Panel과의 물리적 연결
  - 자동 모드 전환 인터페이스
  - 음향 알람 시스템 인터페이스

====== 소프트웨어 인터페이스

* *통신 프로토콜*: 각 제조사별 표준 통신 프로토콜 지원
* *데이터 형식*: Autopilot 상태 및 제어 데이터 형식 지원
* *오류 처리*: 통신 오류 및 시스템 오류 처리 메커니즘
* *로그 기능*: 모든 Autopilot 관련 이벤트 로그 기록

====== 사용자 인터페이스

* *상태 표시*: HiNAS UI에서 Autopilot 연결 상태 및 동작 모드 표시
* *제어 인터페이스*: 스위치를 통한 Autopilot 활성화 제어
* *오류 표시*: HiNAS UI에서 Autopilot 관련 오류 발생 시 오류 메시지 표시

===== 기타 요구사항

====== 안전 요구사항

* *수동 전환*: 언제든지 수동 조타 모드로 전환 가능
* *비상 정지*: 비상 상황 시 즉시 Autopilot 제어 중단
* *감시 기능*: Autopilot 상태 실시간 감시 및 이상 감지

====== 호환성 요구사항

* *TOKYO KEIKI PR-9000*: 모든 버전과의 호환성 보장
* *YOKOGAWA PT900*: 표준 사양과의 호환성 보장
* *통신 표준*: 해당 제조사의 표준 통신 프로토콜 준수
* *하드웨어 표준*: 표준 해상 장비 인터페이스 규격 준수

====== 유지보수 요구사항

====== 문서화 요구사항

* *설치 매뉴얼*: 각 Autopilot 모델별 HiNAS 연동 설치 가이드
* *운영 매뉴얼*: Autopilot 활성화 및 운영 절차 매뉴얼
* *기술 문서*: 각 모델별 기술 사양 및 인터페이스 문서
* *트러블슈팅 가이드*: Autopilot 관련 일반적인 문제 해결 방법
