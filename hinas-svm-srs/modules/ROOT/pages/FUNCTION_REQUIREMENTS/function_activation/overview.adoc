=== Function Activation

==== Overview

===== Terminology
이 섹션에서는 본 챕터에서 반복적으로 사용되는 주요 구성 요소 및 용어를 간략히 정의합니다.

- *TC (Track Control)*: HiNAS Control Standard에서 사용되는 항로 추적 제어 기능
- *Route Tracking*: HiNAS Control Non-Standard에서 사용되는 항로 추적 기능
- *CAA (Collision Avoidance Assistance)*: 충돌 회피 지원 모드
- *CAC (Collision Avoidance Control)*: 충돌 회피 제어 모드
- *SG (Safe Guidance)*: 안전 경로 안내 정보
- *TCS Status*: TC 관련 상세 정보 (모드 및 autopilot, BMS 상태 포함)
- *Autopilot*: Route Tracking 기능에 필요한 자동조타장치
- *BMS (Bridge Management System)*: Route Tracking 기능의 속도 제어에 필요한 선교관리시스템

===== Table of Contents
[cols="1,1,4", options="header"]
|===
| No.
| Function
| Description

| 1
| Function Activation UI
| TC/Route Tracking 활성화를 위한 토글 스위치 및 관련 정보 표시

| 2
| Autopilot Operation
| Route Tracking 기능에 필요한 Autopilot 연동 및 활성화 절차

| 3
| BMS Operation
| Route Tracking 기능의 속도 제어에 필요한 BMS 연동 및 활성화 절차

|===

본 문서는 HiNAS Control 시스템의 Function Activation 기능에 대한 요구사항을 정의합니다.
본 챕터에서는 다음 내용을 순차적으로 설명합니다.

1. Function Activation UI - TC/Route Tracking 활성화를 위한 토글 스위치 및 관련 상태 정보 표시
2. Autopilot Operation - Route Tracking 기능에 필요한 Autopilot 시스템 연동 및 활성화 절차
3. BMS Operation - Route Tracking 기능의 속도 제어에 필요한 BMS 시스템 연동 및 활성화 절차

1장에서는 HiNAS가 제공하는 TC/Route Tracking 활성화를 위한 토글 스위치와 TCS 상태, CA Assistance, CA Control, Safe Path Info 등의 관련 정보 표시에 대해 설명합니다.
2장에서는 Route Tracking 기능에 필요한 Autopilot(TOKYO KEIKI PR-9000, YOKOGAWA PT900) 연동 방법과 활성화 절차를 다룹니다.
3장에서는 Route Tracking 기능의 속도 제어에 필요한 BMS(NABTESCO M-800-V/VII, KONGSBERG AutoChief 600) 연동 방법과 활성화 절차를 설명합니다.

===== Function Activation 범위

*Function Activation UI*

- HiNAS Control Standard: TC 활성화를 위한 토글 스위치 제공
- HiNAS Control Non-Standard: Route Tracking 활성화를 위한 토글 스위치 제공
- TCS Status: TC/Route Tracking과 관련된 상세 정보 표시 (모드 및 autopilot, BMS 상태)
- CA Assistance: CAA 활성화 기능
- CA Control: CAC 관련 상태 정보 표시
- Safe Path Info: SG 표시 및 결정 기능 (CA Control / Not Follow)
- Applied Route: TC/Route Tracking, SG, CA path에 적용된 항로 표시
- Operation Detail: Operation Detail 페이지로 이동하는 버튼

*Autopilot Operation*

- TOKYO KEIKI PR-9000: Route Tracking 스위치 토글 후 E.HC RDY 표시, TC/HC 키를 1-2초간 눌러 EXT.HC 모드로 전환
- YOKOGAWA PT900: Route Tracking 스위치 토글 후 AUTO에서 NAS-HC로 자동 전환 (음향 알람 포함)

*BMS Operation*

- NABTESCO M-800-V/VII: HiNAS HS/S 모드 설정, Auto Mode Ready 확인, Route Tracking 토글 후 AUTO MODE 버튼 점멸, AUTO MODE 버튼 눌러 활성화
- KONGSBERG AutoChief 600: HiNAS HS/S 모드 설정, 모든 상태 지시등 녹색 확인, HiNAS Connected/System Ready 확인, Activate HiNAS 버튼으로 활성화

===== Function Activation 의존성

Function Activation 기능은 다음과 같은 의존성을 가집니다.

*Function Activation UI*

- HiNAS Control 시스템의 정상 동작
- TC/Route Tracking 기능의 정상 동작
- Autopilot 및 BMS 시스템과의 연동 상태
- 충돌 회피 시스템의 정상 동작

*Autopilot Operation*

- Route Tracking 기능의 선행 활성화
- TOKYO KEIKI PR-9000 또는 YOKOGAWA PT900의 정상 동작
- 각 Autopilot 시스템의 Heading Controller 정상 동작
- HiNAS와 Autopilot 간의 통신 연결

*BMS Operation*

- HiNAS Control 모드가 HS(Heading & Speed) 또는 S(Speed) 모드로 설정
- NABTESCO M-800-V/VII 또는 KONGSBERG AutoChief 600의 정상 동작
- 각 BMS 시스템의 준비 상태 (Auto Mode Ready, 상태 지시등 녹색 등)
- HiNAS와 BMS 간의 통신 연결

[IMPORTANT]
====
모든 외부 시스템(Autopilot, BMS)과의 연동 상태는 실시간으로 모니터링되어야 하며,
연동 실패 시 사용자에게 명확한 상태 정보를 제공해야 합니다.
Route Tracking 기능의 정상 동작을 위해서는 Autopilot 연동이 필수이며, 속도 제어가 필요한 경우 BMS 연동도 필요합니다.
====

<<<
include::function_activation_ui.adoc[]

<<<
include::autopilot_operation.adoc[]

<<<
include::bms_operation.adoc[]
