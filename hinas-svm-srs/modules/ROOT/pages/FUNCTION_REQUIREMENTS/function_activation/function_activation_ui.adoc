==== Function Activation UI

===== 설명

Function Activation UI는 HiNAS Control 시스템에서 TC(Track Control) 또는 Route Tracking 기능을 활성화하기 위한 토글 스위치와 관련 정보를 표시하는 사용자 인터페이스입니다.

HiNAS Control Standard와 Non-Standard 버전에 따라 다음과 같이 구성됩니다:

*HiNAS Control Standard*
- TC activation: TC 활성화를 위한 토글 스위치
- TCS status: TC 관련 상세 정보 표시 (모드 및 autopilot, BMS 상태)

*HiNAS Control Non-Standard*
- Route Tracking activation: Route Tracking 활성화를 위한 토글 스위치  
- Route Tracking status: Route Tracking 관련 상세 정보 표시 (모드 및 autopilot, BMS 상태)

*공통 구성 요소*
- CA Assistance: CAA 활성화 기능
- CA Control: CAC 관련 상태 정보 표시
- Safe Path Info: SG 표시 및 결정 기능 (CA Control / Not Follow)
- Safe Path Info: TC/Route Tracking, SG, CA path에 적용된 항로 표시
- Operation detail: Operation Detail 페이지로 이동하는 버튼

===== 실행조건

Function Activation UI의 정상 동작을 위한 실행 조건은 다음과 같습니다:

====== 필수 조건

* *HiNAS Control 시스템 정상 동작*: 기본 시스템이 정상적으로 초기화되고 동작해야 합니다
* *사용자 인터페이스 시스템 정상*: 웹 기반 UI 시스템이 정상 동작해야 합니다
* *네트워크 연결*: 클라이언트와 서버 간 네트워크 연결이 정상이어야 합니다

====== 권장 조건

* *Autopilot 연결*: Autopilot 시스템이 연결되어 상태 정보를 표시할 수 있어야 합니다
* *BMS 연결*: BMS 시스템이 연결되어 상태 정보를 표시할 수 있어야 합니다
* *충돌 회피 시스템*: CA 관련 기능 표시를 위해 충돌 회피 시스템이 동작해야 합니다

====== 제한 조건

* *시스템 오류*: 중요 시스템 오류 발생 시 일부 기능 표시가 제한될 수 있습니다
* *통신 장애*: 외부 시스템과의 통신 장애 시 해당 상태 정보가 표시되지 않을 수 있습니다

===== 검증기준

Function Activation UI의 검증 기준은 다음과 같습니다:

====== 표시 정확성 검증

* *토글 스위치 상태*: TC/Route Tracking 활성화 상태가 정확히 표시되어야 함
* *TCS 상태 정보*: Autopilot 및 BMS 상태가 실시간으로 정확히 표시되어야 함
* *CA 기능 상태*: CAA 및 CAC 상태가 정확히 표시되어야 함
* *Safe Path 정보*: SG 및 적용된 항로 정보가 정확히 표시되어야 함

====== 사용자 인터페이스 검증

* *응답성*: 사용자 입력에 대한 즉각적인 반응 (1초 이내)
* *가독성*: 모든 텍스트와 아이콘이 명확히 식별 가능해야 함
* *일관성*: Standard와 Non-Standard 버전 간 일관된 UI 패턴 유지
* *접근성*: 다양한 화면 크기와 해상도에서 정상 표시

====== 기능 검증

* *토글 동작*: 토글 스위치의 정상적인 ON/OFF 동작
* *버튼 동작*: Operation detail 버튼의 정상적인 페이지 이동
* *정보 업데이트*: 시스템 상태 변화에 따른 실시간 정보 업데이트
* *오류 표시*: 시스템 오류 시 적절한 오류 메시지 표시

===== 성능 요구사항

====== 모드별 연동 상태 표시

* *H 모드 (Heading)*:
  - Autopilot 제어 완료 전까지 "연동중" 상태 표시
  - Autopilot 연동 완료 시 토글 "연동 완료" 상태로 변경

* *S 모드 (Speed)*:
  - BMS 제어 완료 전까지 "연동중" 상태 표시
  - BMS 연동 완료 시 토글 "연동 완료" 상태로 변경

* *HS 모드 (Heading & Speed)*:
  - Autopilot 및 BMS 제어 완료 전까지 "연동중" 상태 표시
  - 양쪽 시스템 모두 연동 완료 시 토글 "연동 완료" 상태로 변경

====== 응답 성능

* *상태 업데이트*: 연동 상태 변경 시 1초 이내 UI 반영
* *토글 상태 전환*: 연동 완료 시 2초 이내 토글 상태 변경
* *사용자 입력 응답*: 1초 이내

===== 인터페이스 요구사항

====== 사용자 인터페이스

* *토글 스위치*: 직관적인 ON/OFF 토글 스위치 (Standard: TC activation, Non-Standard: Route Tracking activation)
* *상태 표시 패널*: TCS/Route Tracking 상태 정보 표시 패널
* *CA 기능 패널*: CA Assistance 및 CA Control 상태 표시
* *Safe Path 정보 패널*: SG 정보 및 적용된 항로 표시
* *네비게이션 버튼*: Operation detail 페이지 이동 버튼

====== 시스템 인터페이스

* *외부 시스템 연동*: Autopilot 및 BMS 상태 정보 수신

====== 데이터 인터페이스

* *설정 데이터*: 사용자 설정 및 시스템 설정 데이터
* *이미지 데이터*: UI 구성 요소 이미지 및 아이콘

===== 기타 요구사항

====== 사용성 요구사항

* *직관적 설계*: 해상 운항자가 쉽게 이해할 수 있는 직관적인 인터페이스
* *색상 구분*: 상태별 명확한 색상 구분 (정상: 녹색, 경고: 노란색, 오류: 빨간색)
* *아이콘 표준화*: 해상 표준 아이콘 및 기호 사용

====== 보안 요구사항

* *Device ID 인증*: 등록된 Device ID를 통한 접근 제어
* *사용자 권한*: 사용자 ID별 권한 레벨에 따른 기능 접근 제어


====== 문서화 요구사항

* *사용자 매뉴얼*: Function Activation UI 사용법 매뉴얼
* *트러블슈팅 가이드*: 일반적인 문제 해결 방법
