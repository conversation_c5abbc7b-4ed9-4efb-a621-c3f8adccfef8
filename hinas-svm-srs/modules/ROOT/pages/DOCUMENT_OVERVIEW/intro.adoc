=== 목표

본 문서의 목적은 *HiNAS SVM (Surrounding View Monitoring)* 시스템의 소프트웨어 요구사항을 명확하게 정의하고,
개발, 테스트, 검토, 유지보수에 참여하는 모든 이해관계자가 공통된 기준으로 참조할 수 있도록 하는 데 있습니다.

요구사항 명세서는 다음을 목표로 합니다:

- 시스템의 기능 및 비기능 요구사항을 체계적으로 정리
- 개발 및 검증 기준의 일관성 확보
- 프로젝트 범위 관리 및 변경 추적 기반 제공
- 고객 및 선박 운항자의 요구사항을 기술적으로 반영

=== 범위

HiNAS SVM은 선박의 접안 및 항만 운항 중 선박 주변의 시각 정보를 종합적으로 제공하여, 항해사의 의사결정을 보조하는 자율 항해 보조 시스템입니다.
본 문서에서 정의하는 범위는 아래와 같습니다:

- HiNAS SVM 시스템에 포함된 소프트웨어 기능 정의
- Surround View, Side View, 타깃 정보 표시, 선박 상태 표시기, 영상 기록 등 주요 기능 포함
- 실제 운용 환경을 고려한 성능 요건 및 제약 사항
- 선택 기능(Optional) 항목에 대한 명시 및 조건
- 하드웨어 대한 최소 사항 및 외부 시스템과의 인터페이스 요구사항

※ 하드웨어 상세 사양, 외부 시스템과의 계약 조건 등은 본 문서의 범위에서 제외됩니다.

=== 문서규칙

본 문서는 다음의 규칙에 따라 작성되었습니다:

- 중요 용어나 약어는 문서 말미의 용어 정의(Glossary) 항목에 정리합니다.
- 기능 요구사항과 비기능 요구사항을 명확히 구분하여 기술합니다.
- 선택 기능은 *(Optional)* 로 명시하며, 기본 기능과 구분됩니다. 해당 선택 기능은 Feature Toggle을 통해서 선택적으로 제공할 수 있습니다.
- 모든 표기와 단위는 SI 단위계를 기준으로 통일하며, 시간은 24시간제를 사용합니다.
- 문서는 AsciiDoc 기반으로 작성되며, PDF 및 HTML 등 다양한 형식으로 출력될 수 있습니다.

=== 대상 및 읽는 방법

본 문서는 *HiNAS SVM (Surrounding View Monitoring)* 시스템의 기능, 성능, 제약 사항 및 설계 고려사항을 포함한 소프트웨어 요구사항 명세서입니다.

국제 표준 *ISO/IEC/IEEE 29148:2018* 및 **IEEE 830-1998**의 요구사항 명세서(SRS) 구조를 기반으로 작성되었습니다.
각 섹션은 요구사항의 유형 및 목적에 따라 다음과 같이 구성되어 있습니다.

- **1장: 문서 개요** — 문서의 목적, 범위, 대상 독자 및 작성 규칙
- **2장: 시스템 개요** — 시스템의 전체 구조, 용어 정의, 아키텍처, 형상 관리 등
- **3장: 기능 및 비기능 요구사항** — 주요 기능 정의 및 성능, 안정성 등의 품질 속성
- **4장: 인터페이스 요구사항** — 사용자, 하드웨어, 소프트웨어, 통신 인터페이스
- **5장: 기타 요구사항** — 설치, 보안, 제약 사항 등
- **6장: 부록** — 연관 문서, 기준 문서, 변경 이력 등


시스템 이해관계자들이 본 문서를 효과적으로 이해하고 활용할 수 있도록 다음과 같이 대상자 및 읽는 방법을 정의합니다.

==== 대상 독자

다음은 본 문서의 주요 대상자입니다.

[cols="1,3", options="header"]
|===
| 역할 | 설명

| 시스템 기획자 / 제품 관리자
| 시스템의 요구사항 정의 및 기능 검토

| 개발자
| 기능 구현을 위한 상세 요구사항 분석 및 참조

| QA 엔지니어
| 요구사항 기반 테스트 항목 도출 및 검증 기준 수립

| 기술지원 엔지니어
| 기능 동작 및 제약 조건에 대한 기술적 이해 확보

| 커미셔너
| 선박 현장에서 시스템 설치, 설정, 초기 시운전 수행 및 기능 확인을 위한 기준 문서로 활용

| 영업팀
| 제품 기능 및 차별화 포인트에 대한 이해 확보 및 고객 커뮤니케이션 자료로 활용
|===

==== 문서 읽는 방법

- 섹션 구조: 본 문서는 각 기능 항목별로 고유 번호를 부여하여 구성되어 있으며, 각 항목은 기능 설명,  제약 사항, UI 구성, API 및 입출력 등으로 구성됩니다.
- 우선순위 식별: 일부 요구사항에는 중요도(Priority)가 명시되어 있으며, 이는 구현 및 테스트 시 고려되어야 합니다.
- 필수 vs 선택 기능 구분: 선택 기능(optional)은 문서 내 명확히 표시되어 있으며, 선주 요구사항 또는 선박 사양에 따라 적용 여부가 달라질 수 있습니다.
- 표 및 그림: 기능 설명과 이해를 돕기 위해 다이어그램, 테이블, 이미지가 포함되어 있으며, 이들은 본문과 동일한 수준의 중요도를 갖습니다.
- 연계 문서: 설계서, 테스트 명세서, 운용 메뉴얼 등 다른 문서와 연계되어 참조될 수 있습니다. 해당 문서가 필요한 경우 문서 하단의 부록에 링크를 첨부합니다.


=== 요구사항 추적성
본 문서에서 정의된 모든 요구사항은 추적 가능성을 확보하기 위해 다음과 같은 규칙을 따릅니다.

- 각 요구사항은 고유한 식별자(ID)를 갖습니다. 예: `REQ-001`, `REQ-002`
- 요구사항의 변경 이력은 문서 하단의 부록에 기록되며, 변경된 사항은 명확히 표시됩니다.
- 각 요구사항은 관련된 기능, 테스트 케이스, 설계 문서와의 관계를 명시합니다.