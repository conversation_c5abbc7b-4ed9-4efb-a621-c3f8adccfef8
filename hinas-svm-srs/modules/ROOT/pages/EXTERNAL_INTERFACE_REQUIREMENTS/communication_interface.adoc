=== 통신 인터페이스 (Communication Interface)

==== Network Interface
===== Sensor Network
HiNAS SVM에 필요한 항해 장비는 센서 네트워크를 통해 연결됩니다.
해당 네트워크는 `Unicast` 및 `Multicast` 방식의 NMEA 패킷 수신을 모두 지원하며, GGA, RMC, VTG 등의 표준 NMEA 0183 sentence를 처리합니다.
멀티캐스트 수신 시에는 IGMP 설정 및 스위치의 멀티캐스트 라우팅 설정이 필요할 수 있습니다.
또한, 방화벽이 연결되는 경우, UDP 포트 6501에 대한 수신 허용이 필요합니다.

HiNAS SVM에서는 기본적으로 UDP 포트 6501을 사용하나, 네트워크 환경에 따라 6502 포트를 대체 포트로 사용할 수 있도록 설정되어 있으며, 우분투 방화벽 등에서도 해당 포트에 대한 수신이 허용되어 있습니다.

[TIP]
====
네트워크 환경에 따라 UDP 포트는 `6501` 외 다른 번호로 설정될 수 있습니다.
이 경우, 송수신 장비 간 포트 번호가 일치하도록 구성해야 합니다.
====

.Sensor Network Communication Interface
[cols="1,1,2", options="header"]
|===
| Source
| Destination
| Purpose

| SENSOR NETWORK
| Main Server (UDP/6501)
| NMEA SIGNAL

|===



HiNAS SVM이 Sensor Network를 통해 수신하는 데이터는 다음과 같습니다.

.Data Input List in Sensor Network
[cols="1,2,1", options="header"]
|===
| Source | Description | Remark

| GPS
| Latitude
|

| GPS
| Longitude
|

| GPS
| UTC
|

| GPS
| Local time zone
|

| GPS
| COG (Course Over Ground)
|

| GPS
| SOG (Speed Over Ground)
|

| Speed Log
| Longitudinal water speed
|

| Speed Log
| Transverse water speed
|

| Gyro
| Heading angle
|

| Gyro
| Rate of turn
|

| Anemometer
| Wind speed
|

| Anemometer
| Wind angle (Wind Direction)
|

| IP Camera
| Camera image
|

| AIS
| Own ship position
| Optional

| AIS
| Own ship speed
| Optional

| AIS
| Own ship course
| Optional

| AIS
| Own ship type
| Optional

| AIS
| Target ship position
| Optional

| AIS
| Target ship speed
| Optional

| AIS
| Target ship course
| Optional

| AIS
| Target ship type
| Optional

| RADAR(ARPA)
| Target ship position
| Optional

| RADAR(ARPA)
| Target ship bearing
| Optional

| Draft Gauge
| Own Ship Draft
| Optional
|===

===== Vsat
HiNAS SVM은 VSAT를 통해 인터넷에 연결할 수 있습니다.



===== CCTV




===== PTZ Camera


