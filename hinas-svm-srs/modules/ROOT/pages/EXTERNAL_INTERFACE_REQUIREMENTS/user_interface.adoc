=== 유저 인터페이스 (User Interface)

==== 사용자 플로우 (User Flow)

===== 1. Surround View 및 Side View Flow

[NOTE]
====
사용자는 카메라를 통해 선박 주변의 물체를 인지하고, 이를 바탕으로 회피 운항을 수행합니다.
====

. Main 페이지 접속
. 카메라 선택 창에서 원하는 카메라 4대를 선택 (참조: 3.2)
. 선택된 카메라 기반으로 Surround View 및 Side View가 현시됨 (참조: 1.1, 3.1)
. 사용자는 관심 물체를 확대하여 자선과의 상대 위치를 시각적으로 확인함
.. 가로 회전 모드를 통해 수평 방향으로 확장된 Surround View 확인
.. 세로 방향에서 4방향 중 원하는 방향을 선택하여 확대 확인
.. Side View를 클릭하여 해당 화면 확대
. AI 기반 객체 탐지 기능을 통해 주변 물체 인지
. 등거리 선 기반으로 자선과 주변 물체 간 거리 확인
.. Surround View에 표기된 등거리선을 통해 전체 거리 시각화
.. Side View에 표시된 거리 선을 통해 특정 방향 거리 상세 확인
. 시각적·거리 정보를 기반으로 안전 운항 수행

===== 2. Target Information Display Flow

[NOTE]
====
야간, 해무 등으로 인해 카메라 시야 확보가 어려운 상황에서 Target Information Display를 통해 주변 선박을 인지하고 안전 운항을 수행합니다.
====

. Main 페이지 접속 후 Target Information Display로 전환
. Collision Alert Setting 창에서 알림 조건 설정
.. Distance, CPA, TCA, 알림 Threshold 설정
. Target Information Display 상 위험도 색상 기반으로 주변 위험 선박 인지
. AIS 알림을 통해 충돌 위험 선박 실시간 확인
. 위험 선박 선택 후 상세 정보 확인
. 정보를 바탕으로 충돌 회피 운항 수행

===== 3. Video Record Flow

[NOTE]
====
운항 중 발생한 사고나 위험 상황에 대해, 해당 시점의 선박 주변 환경을 다시 확인하기 위해 저장된 영상을 조회 및 다운로드합니다.
====

. Video Record 화면 접속
. 사고 발생 시점의 날짜 선택
. 확인하고자 하는 카메라 선택
. 해당 시각의 녹화 영상 재생
. 사고 시점의 주변 상황 확인
. 영상 다운로드 후 필요 시 외부 공유

===== 4. 커미셔닝 Flow

[NOTE]
====
커미셔너는 설치된 카메라 수와 선박 스펙(Beam, Length)을 고려하여 Surround View의 정합성을 확보하기 위한 보정(Calibration)을 수행합니다.
====

. 설정(Setting) 페이지 접속 후 선박 정보 입력
. 설정 페이지에서 기본 카메라 수 및 제조사 확인
.. 카메라 네트워크 연결 상태 점검
.. 카메라 정보 저장
. 설치 환경에 맞는 해상도 및 배율 설정
. Surround View가 실환경 기반으로 구성됨을 확인
.. 개별 카메라 선택 시 해당 View 확인 가능
.. 각 카메라에 대해 Calibration 수행 → Surround View 통합 시각화 제공

