=== 소프트웨어 인터페이스 (Software Interface)

==== 소프트웨어 의존성 (Software Dependencies)

[cols="1,1,1,2,1", options="header"]
|===
| Name | Manufacturer | Version | Function | H/W with Installed S/W

| Ubuntu | Canonical | 22.04.1 LTS | Main Operating System | Main Server
| git | Git Development Community | 2.34.1 | Software Version Control | Main Server
| containerd | CNCF | v1.30.4+k3s1 | Software Deployment | Main Server
| Chrome | Google | 128 | Web Browser | Main Server
| ClamAV | ClamAV | 1.0.7-1 | Antivirus | Main Server
| OpenSSH | OpenBSD Project | 8.9p1 | Remote Connection | Main Server
| Lightweight Kubernetes | CNCF | v1.30.4+k3s1 | Lightweight Kubernetes | Main Server
| Helm3 | CNCF | v3.15.4 | Utility Program | Main Server
| k9s | Imhotep Software LLC | v0.32.5 | Utility Program | Main Server
| python | Python Software Foundation | 3.8, 3.9, 3.10 | Software Build | Main Server
| fluentbit | Treasure Data | 3.1.7 | Logging | Main Server
| fluentd | Treasure Data | 1.17.1 | Logging | Main Server
| MinIO | MinIO | RELEASE.2021-04-22T15-44-28Z.hotfix.56647434e | DB | Main Server
| redis | Redis Labs | 7.4.0-alpine | DB | Main Server
| USBGuard | OpenSource Project | 1.1.1+ds-3 | USB Access Control | Main Server
| HiNAS USBGuard | Avikus | 1.0.0 | HiNAS USBGuard | Main Server
| Kubernetes | CNCF | v1.30.4 | Kubernetes | Main Server
| MediaMTX | CNCF | 1.8.4 | Media Server | Main Server
| ArgoCD | CNCF | 2.12.3 | Continuous Deployment | Main Server
| DockerRegistry | CNCF | 2.8.3 | Container Management | Main Server
| LocalProxy | AWS | 92dd39cb7bad1c4731fb8a26f32fe20d370bc57f | Remote Connection | Main Server
| Skopeo | OpenSource Project | 1.16.1 | Container Management | Main Server
| systemd | OpenSource Project | 249.11-0ubuntu3.12 | System Management | Main Server
| systemd-timesyncd | OpenSource Project | 249.11-0ubuntu3.12 | Time Synchronization | Main Server
| cron | OpenSource Project | 3.0pl1-137ubuntu3 | Task Scheduling | Main Server
| network-manager | OpenSource Project | 1.36.6-0ubuntu2 | Network Management | Main Server
| wakeonlan | OpenSource Project | 0.41-12.1 | Remote Wakeup | Main Server
| ufw | OpenSource Project | 0.36.1-4ubuntu0.1 | Firewall Management | Main Server
| openssh-server | OpenSource Project | 1:8.9p1-3ubuntu0.10 | Remote Login | Main Server
| xrdp | OpenSource Project | 0.9.17-2ubuntu2 | Remote Desktop | Main Server
| keepalived | OpenSource Project | 1:2.2.4-0.2build1 | Load Balancing | Main Server
| nvidia-driver | OpenSource Project | 535.171.04-0ubuntu0.22.04.1 | Graphics Driver | Main Server
| nvidia-container-toolkit | OpenSource Project | 1.16.1-1 | Container Toolkit | Main Server
| udev | OpenSource Project | 249.11-0ubuntu3.4 | Device Management | Main Server
| busybox | OpenSource Project | 1.36 | Utilities | Main Server
| Fortigate 60F Rugged | Fortigate | 6.2.7 | Main Firewall | Firewall
| GC-F12BN | Arges | 2.1.1.340 | IPCAM | IPCAM
| IGS-4215-4p4t | PLANET | 2.3.05b240712 | Main POE Switch | POE
| MOXA-G508E | MOXA | 6.4 | Main Switch | L2
| Avikus Remote Manager | AWS | 1.0.0 | Remote Terminal Management | Cloud Server
| Avikus Software Update Manager | AWS | 1.0.0 | Software Update Management | Cloud Server
|===
