:toc: left
:toclevels: 3
:toc-title: 목차

=== 기술 개요 (Technical Overview)
본 장에서는 HiNAS SVM 시스템을 구성하는 주요 하드웨어 및 연관 기술에 대해 설명합니다.
기술적 배경이 없는 독자도 전체 구조를 이해할 수 있도록, 각 기술의 사용 목적과 기능을 명확하고 간결하게 서술하였습니다.


==== 하드웨어 장비

*Fish Eye IP Camera*

.Arges Fish Eye Camera
image::SYSTEM_OVERVIEW/technical_overview_fisheye_camera.png[align="center", 200]


Fish Eye IP Camera는 일반적인 카메라와 달리, 초광각 어안렌즈(Fisheye Lens)를 사용하여 최대 180도에서 360도까지 넓은 시야각을 확보할 수 있는 네트워크 카메라입니다.
HiNAS SVM 시스템에서는 선박 외곽에 설치되어, 선박 주변의 넓은 영상 데이터를 수집하는 데 사용됩니다. 어안렌즈 특성상 출력되는 영상은 왜곡된 원형 형태로 나타나며, 이는 Fish Eye View라고 불립니다.

.Fish Eye Camera View (Bow)
image::SYSTEM_OVERVIEW/technical_overview_fisheye_view.jpg[align="center",400]


해당 영상은 시스템 내 소프트웨어 모듈에서 Warping 또는 De-warping 과정을 거쳐 정사각형 형태의 Surround View 또는 Side View로 변환되어 사용됩니다.

.Side View (Bow)
image::SYSTEM_OVERVIEW/technical_overview_side_view.jpg[align="center",400]


Fish Eye IP Camera는 RTSP(Real-Time Streaming Protocol)를 통해 실시간으로 서버에 영상을 송출하며, 각 카메라는 고정된 방향으로 설치되어 물리적인 회전 없이도 넓은 영역을 커버할 수 있습니다. 이로 인해 사각지대 없이 선박 주변을 실시간으로 모니터링할 수 있으며, 다수의 카메라를 하나의 영상으로 통합한 Surround View 구현에 핵심적인 역할을 합니다.

또한, 카메라는 IP 기반으로 네트워크에 연결되며, 전원은 PoE(Power over Ethernet) 방식을 통해 공급됩니다. 이 구조는 설치와 유지보수가 간단하고, 선박 환경에서도 안정적인 운영이 가능합니다. HiNAS SVM에서 사용되는 Fish Eye IP Camera는 영상 품질, 저조도 성능, RTSP 호환성, 동작 온도 등을 기준으로 선정되며, 대형선박에서는 일반적으로 6대에서 10대, 중소형 선박에서는 4대 정도가 설치됩니다.

추가적으로, Ex-Proof Fish Eye Camera는 폭발 위험이 있는 환경에서 사용하기 위해 특수 설계된 방폭형 카메라입니다. “Ex-Proof”는 “Explosion Proof”의 약자로, 해당 카메라가 가연성 가스, 증기, 먼지 등이 존재하는 위험 구역에서 폭발을 유발하지 않도록 밀폐 또는 방호 설계된 장비임을 의미합니다. Ex-Proof 카메라는 주로 유조선, 화학운반선, 가스선 등 위험물질을 운송하거나 저장하는 선박에 사용되며, 국제 방폭 인증(예: ATEX, IECEx)을 획득한 제품이 사용됩니다.

*Main Server*

.Main Server
image::SYSTEM_OVERVIEW/technical_overview_main_server.png[align="center", 200]

- SVM Backend가 실행되는 XE4 서버로, 영상 처리, Inference, UI 통신 등 HiNAS SVM의 핵심 기능을 수행합니다.
** 12세대 Intel Core i7 프로세서
**  DDR5 메모리 탑재 (XE4 Tower 전용)
**  512GB m.2 SSD
**  2TB HDD
**  ABS, BV, CCS, DNV, KR, LR, NK 형식 승인(Type Approved)
**  IACS UR E10 및 IEC 60945 인증 (조타실 설치 적합)

*Dimming Switch*

디스플레이 밝기 조절 장치. 야간 또는 암실 환경에서 눈부심 방지를 위해 사용됨.

*DP (DisplayPort / 디스플레이 포트)*

고해상도 영상 출력을 위한 디지털 인터페이스입니다. HDMI보다 높은 대역폭을 지원하며, 모니터와 서버 간 연결 시 사용됩니다.

*HDMI (High Definition Multimedia Interface / 고선명 멀티미디어 인터페이스)*

비디오 및 오디오를 하나의 케이블로 전송하는 디지털 인터페이스입니다. 모니터 연결용으로 사용되며, TV나 산업용 패널에서도 널리 사용됩니다.

*UPS (Uninterruptible Power Supply / 무정전 전원 장치)*

정전 시에도 일정 시간 동안 전력을 공급하는 장치입니다. SVM 서버, 스위치, 모니터 등의 핵심 장비가 예기치 않은 전원 차단으로부터 보호되도록 합니다.

*RJ45 (Registered Jack 45 / 이더넷 포트)*

네트워크 장비에서 사용하는 8핀 표준 커넥터입니다. SVM 시스템에서는 IP 카메라, PoE 허브, L2 스위치, 서버 간 연결에 사용되며, 일반적으로 1Gbps 또는 100Mbps 통신을 지원합니다.

*POE HUB(Power over Ethernet / 이더넷 전원 공급)*

네트워크 케이블(RJ45)을 통해 데이터와 전원을 동시에 공급하는 장비입니다. SVM 시스템에서는 IP 카메라에 전력을 공급하기 위해 사용됩니다. 별도의 전원선 없이 설치가 간편합니다.

*L2 Switch (Layer 2 Switch / 2계층 스위치)*

OSI 7계층 중 2계층(데이터 링크 계층)에서 MAC 주소 기반으로 데이터 전송을 제어하는 네트워크 장비입니다. VLAN 구성, 트래픽 제어, 포트 관리 등을 지원합니다.

*LRP Injector & Extender*

LRP는 PoE 신호를 100m 이상 장거리로 전달하기 위한 장비입니다.
네트워크 케이블을 통해 전원과 데이터를 증폭하여 전달하는 장치입니다.
케이블 길이 또는 환경 노이즈로 인한 손실을 방지합니다.
HiNAS SVM에서는 LRP Injector와 Extender 장치를 함께 사용하여 IP 카메라가 멀리 떨어진 장소에도 안정적으로 전력과 데이터를 공급합니다.

*VDR (Voyage Data Recorder)*

.VDR
image::SYSTEM_OVERVIEW/technical_overview_vdr.jpeg[align="center", 200]

AIS, ARPA, 자선의 위치 및 속도 등 항해 데이터를 수신하는 장비. 타선 정보 표시, 자선 상태 표시 등에 사용됨.

*Loading Computer*

선박의 실시간 Draught 정보를 제공하는 장비.

*VSAT*

위성 통신을 위한 장비. 원격 유지보수, 소프트웨어 업데이트, 서비스 모니터링 등에 사용됨.

*Bridge Control Console*

윙브릿지에 설치된 조타실 제어 콘솔. 모니터, 서버, 입력장치 등 운항자가 실시간 영상을 확인하고 조작하는 주요 인터페이스 장치가 탑재됨.

==== 항해 장비 및 관련 시스템

*AIS (Automatic Identification System / 선박 자동 식별 장치)*

.AIS
image::SYSTEM_OVERVIEW/technical_overview_ais.png[align="center", 400]

선박 간 자동으로 정보를 송수신하는 시스템으로, 목적지, 도착예정시간(ETA), 헤딩, COG, CPA, 전장(LOA), 흘수(Draft) 등을 공유함. 대부분 VHF 주파수를 통해 작동하며 어선까지 광범위하게 설치됨.

*ARPA (Automatic Radar Plotting Aid / 선박 자동 충돌 예방 보조 장치)*

.RADAR
image::SYSTEM_OVERVIEW/technical_overview_radar.png[align="center", 400]

레이더와 연동하여 주변 선박의 이동 벡터를 자동 계산함. 상대 침로, CPA, TCPA 등을 연산하여 충돌 가능성을 예측하고 운항자에게 경고함.


*VDM (VHF Data Link Message)*

VDM은 AIS(Automatic Identification System)에서 사용하는 NMEA 0183 메시지 형식으로, 선박 간의 실시간 정보를 송수신하는 데 사용됩니다. 주요 특징은 다음과 같습니다.

* 사용 주체: AIS 장비
* 전송 방식: VHF 대역 무선 통신
* 메시지 형식: NMEA 0183 표준의 `!AIVDM` 또는 `!AIVDO` 형식
* 포함 정보:
** MMSI (Maritime Mobile Service Identity)
** 선박명, 호출부호
** 위치 (위도/경도), 속도(SOG), 방향(COG, HDG)
** 항해 상태 (정박, 항해 중 등)

*TTM (Tracked Target Message)*

TTM은 ARPA (Automatic Radar Plotting Aid) 시스템에서 생성하는 레이더 기반 추적 데이터 메시지입니다. 해당 메시지는 주변 선박의 위치 및 이동 상태를 레이더 기반으로 추정하여 출력합니다.

* 사용 주체: ARPA 레이더 시스템
* 메시지 형식: NMEA 0183 표준의 `TTM` 문장
* 포함 정보:
** 타겟 번호
** 거리 (Range), 방위각 (Bearing)
** 상대 속도 및 방위각 (Relative Course, Speed)
** CPA/TCPA (가장 가까운 접근 거리 및 시간)
** 타겟 상태 (신규/갱신/소실)

*MMSI (Maritime Mobile Service Identity)*

MMSI는 선박, 해안국, 항공기 등의 무선 통신 식별을 위한 고유 식별 번호로, 9자리 숫자로 구성됩니다. 선박의 AIS 메시지에서 해당 선박을 유일하게 식별하는 데 사용되며, 통신 대상 구분 및 데이터 추적의 기준이 됩니다.


==== 항해 관련 주요 용어

*HDG (Heading / 선수방위)*

선박의 정선수가 향하는 방위입니다. 나침반 또는 자이로센서를 기준으로 측정되며, 조타 기준이 되는 지표입니다. 실제 이동 경로(COG)와는 차이가 발생할 수 있습니다.

*COG (Course Over Ground / 대지방위, 실침로)*

조류, 바람 등 외부 요인의 영향을 포함한 선박의 실제 이동 경로입니다. HDG와 비교하여 외력에 의한 영향을 분석할 수 있습니다.

*Bearing (방위각)*

자선 기준으로 타선이나 고정 물체가 위치한 방향입니다. 정북(0도)을 기준으로 시계방향 각도로 표현되며, Radar, ARPA, AIS 등에서 활용됩니다.

*STW (Speed Through Water / 대수속력)*

선박이 물에 대해 이동하는 속도입니다. 조류를 제외한 선박 자체의 추진 속도로, 프로펠러 회전과 밀접하게 연관되어 있습니다.

*SOG (Speed Over Ground / 대지속력)*

GPS 기준으로 지상에서 측정된 실제 이동 속도입니다. 외력에 영향을 받으며, STW와 비교해 조류의 영향을 파악할 수 있습니다.

*knot (노트)*

항해 속도의 단위입니다. 1 knot는 1시간에 1해리(1.852 km)를 이동하는 속도를 의미합니다. 예: 20 knot = 약 37 km/h

*CPA (Closest Point of Approach / 예상 최근접 거리)*

현 속도와 방위로 항해 시, 타선과 가장 가까워지는 순간의 거리입니다. 충돌 위험도를 판단하는 핵심 지표로 사용됩니다.

*TCPA (Time to CPA / 예상 최근접 시간)*

타선과의 CPA까지 도달하는 데 걸리는 시간입니다. 추월 시점 예측, 충돌 회피 판단 등에 활용됩니다.

*Distance (거리)*

자선과 타선 또는 고정 구조물 간의 직선 거리입니다. Radar, ARPA, Side View 등 다양한 시스템에서 표시되며, CPA와 함께 충돌 회피 판단에 사용됩니다.

*True Wind (진풍 / 절대 풍향)*

지표(지구 기준)에서 측정한 바람의 방향과 속도입니다. 선박이 정지해 있다고 가정할 때 느껴지는 바람이며, 일반적으로 기상 정보, 항로 계획, 자동 항법 시스템 등에 사용됩니다.
AIS나 항해 장비에서 표시되는 기본 풍향이며, 북쪽(0도)을 기준으로 한 절대 방위로 표현됩니다.

*Relative Wind (상대풍 / 상대적 풍향)*

이동 중인 선박 위에서 체감되는 바람의 방향과 속도입니다. 선박의 진행 방향과 속도에 의해 영향을 받으며, 실제 갑판 위에서 느껴지는 바람입니다.
Relative Wind는 선수 기준(0도)을 기준으로 좌우(좌현/우현) 방향과 함께 시계 방향 각도로 표현됩니다. 예: "Port 45° 10 knot"

*ROT (Rate of Turn / 회전율)*

선박이 단위 시간당 회전하는 각도(°/분)입니다. ARPA 등에서 회전 경로 예측이나 변침 판단 시 사용됩니다.

*ETA (Estimated Time of Arrival / 도착 예정 시간)*

AIS를 통해 공유되는 도착 예상 시간입니다. 대부분 UTC 기준으로 입력되며, 일부는 Local Time을 사용하기도 합니다.

[[draught_explain]]
*Draught*

.Measured Draught와 Scantling Draught
image::FUNCTION_REQUIREMENTS/setting/ship_spec/ship_spec_draught_explain.png[align="center",400]


- Measured Draught: 선박이 현재 상태에서 실제로 측정된 흘수
- Scantling Draught: 선박 설계 시 기준으로 설정된 최대 흘수

Scantling Draught는 선박이 구조적으로 견딜 수 있는 최대 적재 조건에서의 흘수(Fully Loaded Condition)를 의미하며, 선체 설계 및 강도 계산의 기준으로 사용되는 고정값입니다. 선박 설계 도면 및 국제선급 규정에 따라 사전에 정의되며, 일반적으로 변경이 불가능합니다.

Measured Draught는 선박이 실제 운항 중 또는 정박 중의 현재 상태에서 측정된 흘수로, 화물 적재량, 연료, 배수 등 실질적인 무게 변화에 따라 달라지는 실시간 값입니다. Measured Draught는 센서 기반 자동 측정 또는 사용자 수동 입력을 통해 반영될 수 있으며, SVM 시스템에서는 사용자가 직접 입력하여 수정할 수 있습니다.

SVM에서는 Measured Draught 값을 기준으로 선박의 수면 아래 잠김 깊이를 보정하며, 이는 **카메라 기준 좌표계(Z축)**를 전체적으로 상하 이동시켜 가시 영역에 영향을 줍니다.

Measured Draught는 일반적으로 Scantling Draught의 80~95% 수준이며, 선박의 실제 적재 상태에 따라 달라집니다.

==== 카메라 및 이미지 관련 개념.

*De-warping (디워핑)*

- 어안(Fisheye) 렌즈로부터 생성된 왜곡된 이미지를 수학적으로 보정하여, 정규화된 평면 이미지로 변환하는 과정을 의미합니다. 디워핑은 각 카메라별 intrinsic 파라미터와 distortion 계수를 기반으로 수행됩니다.

*방사왜곡(radial distortion)*

- 방사왜곡(radial distortion)은 카메라 렌즈에서 발생하는 대표적인 비선형 왜곡으로, 이미지의 중심으로부터 멀어질수록 직선이 휘어 보이는 현상을 의미합니다.
- 수학적으로는 보통 렌즈 왜곡 모델을 통해 방사왜곡을 보정하며, 이는 다음과 같은 다항식 계수 형태로 표현됩니다.

[latexmath]
++++
x_{distorted} = x (1 + k_1 r^2 + k_2 r^4 + k_3 r^6) \\
++++

[latexmath]
++++
y_{distorted} = y (1 + k_1 r^2 + k_2 r^4 + k_3 r^6)
++++
- 각 픽셀 좌표의 왜곡 정도는 렌즈 중심과의 거리인 r에 따라 결정되며, 일반적으로 위와 같은 형태의 함수가 사용됩니다. 여기서 k₁, k₂, k₃는 방사 왜곡 계수이며, 카메라 보정(calibration)을 통해 추정됩니다. 방사왜곡은 정밀한 측정, 거리 추정, 3D 재구성 등에서 큰 오차를 유발할 수 있으므로, 실제 영상처리 시스템에서는 이를 정확히 보정하는 과정이 필수적입니다.


*Intrinsic Parameters (내부 파라미터)*

- Intrinsic 파라미터는 카메라 고유의 내부 설정값으로, 3차원 세계의 점을 2차원 이미지 좌표로 변환할 때 사용하는 행렬 요소이다. 이 값들은 카메라의 해상도, 센서 구조, 초점 거리, 주점(Principal Point) 등과 관련됩니다. 즉, "카메라 자체가 어떻게 생겼는가"를 설명하는 값들이다.

- 기본적인 intrinsic 행렬은 다음과 같은 3x3 형태를 가진다.

[latexmath]
++++
K =
\begin{bmatrix}
f_x & 0   & c_x \\
0   & f_y & c_y \\
0   & 0   & 1
\end{bmatrix}
++++

* `f_x`, `f_y` : X축과 Y축 방향의 초점 거리 (픽셀 단위)
* `c_x`, `c_y` : 주점(Principal Point) — 이미지 중심 좌표 (보통 이미지의 중심 근처에 위치)
* `K` : 카메라의 내부 행렬 (Intrinsic Matrix)

- 이 intrinsic matrix는 세계 좌표계에서 이미지 좌표계로 변환할 때 사용되며, 외부 파라미터(Extrinsic)와 함께 사용되어 카메라 투영 모델을 구성한다.

- 카메라 캘리브레이션 과정에서는 이 intrinsic matrix뿐만 아니라 왜곡 계수(distortion coefficients)도 함께 추정한다. 추정된 intrinsic 정보는 디워핑(dewarping), 3D 복원, 거리 측정 등에 필수적으로 사용됩니다.

*Homography*

- Homography는 서로 다른 평면 간의 변환 관계를 표현하는 3x3 행렬입니다. Fisheye 보정 후 Surround View 또는 Side View 좌표계로 변환할 때 사용됩니다. 카메라의 보정값과 위치/회전 정보를 기반으로 계산됩니다.

*LUT (Look-Up Table)*

- LUT는 좌표 변환 시 반복 연산을 줄이기 위해 미리 계산된 변환 값을 저장한 테이블로, Fisheye → 정규좌표 변환 등에서 사용됩니다.

*mAP (mean Average Precision)*

- 객체 탐지 정확도를 수치로 평가하는 대표적인 지표로, IoU 기준에 따라 예측이 정답과 얼마나 일치하는지를 나타냅니다.

*Meter-Per-Pixel (MPP) 변환*

- 픽셀 단위의 거리 값을 실제 거리(meter) 단위로 환산하기 위한 비율값으로, 센서 캘리브레이션 결과에 따라 결정됩니다.


==== 개발 관련 개념

*WebSocket (웹소켓)*

- WebSocket은 클라이언트와 서버 간의 지속적인 실시간 양방향 통신을 가능하게 하는 프로토콜입니다. HTTP와 달리, 연결이 한 번 수립되면 양측에서 자유롭게 메시지를 주고받을 수 있습니다. SVM 시스템에서는 Surround View, Side View, Target Information을 프론트엔드로 전송하는 데 사용됩니다.

*GGA Sentence 파싱 (NMEA 0183)*

- GGA Sentence는 GPS 기반 위치 정보를 포함한 표준화된 문자열(NMEA 0183 포맷)입니다. 해당 데이터를 파싱하여 UTC 시간, 위도, 경도, 위성 수 등을 추출합니다. 파싱된 결과는 Redis에 저장되어 WebSocket 또는 UI에서 활용됩니다.

*Redis*

- Redis는 In-memory 데이터베이스로, SVM에서는 이미지 저장소로 사용되며, GET, SET 메서드를 통해 메시지 전달 및 실시간 처리를 지원합니다. 각 카메라에서 캡처된 이미지는 Redis 키에 저장되며, TTL(Time-To-Live) 설정을 통해 일정 시간이 지나면 자동 삭제됩니다.

*RTSP (Real-Time Streaming Protocol)*

- RTSP는 IP 카메라로부터 실시간으로 영상 스트림을 수신하기 위한 표준 프로토콜입니다. 각 카메라의 RTSP 스트림은 rtsp_to_redis 모듈을 통해 캡처되며, JPEG 이미지로 변환 후 Redis에 저장됩니다.

*VDR 및 Sentence*

VDR(Voyage Data Recorder)는 선박의 블랙박스 역할을 하며, 항해 데이터 및 선박의 운항 정보를 기록하는 장치입니다. 일반적으로 선박의 위치, 속도, 방향, 엔진 상태, 교신 기록 등을 저장하며, 사고 발생 시 원인 분석을 위해 사용됩니다. VDR에서 저장하는 데이터는 국제적으로 표준화된 NMEA 0183 Sentence 형식으로 기록됩니다.

**NMEA 0183 Sentence **

- NMEA(National Marine Electronics Association) 0183은 해양 장비 간의 데이터 통신을 위한 프로토콜입니다.
데이터는 Sentence(문장) 단위로 전송되며, 다음과 같은 특징을 가집니다:

** $ 또는 ! 문자로 시작
** 5자리의 Talker ID + Sentence Type
** 쉼표(,)로 구분된 데이터 필드
** 마지막에 체크섬(*) 포함
** 예시: $GPRMC,123519,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A
*** 이 문장은 GPS 수신기의 RMC(Recommended Minimum Navigation Information) 데이터로, 현재 시간(UTC), 위치, 속도, 방향 등의 정보를 포함합니다.

**NMEA Parser**

- NMEA 데이터를 해석하는 프로그램을 NMEA Parser라고 합니다.
- 이 파서는 NMEA Sentence를 분석하여 특정 데이터를 추출하는 역할을 수행합니다.
- NMEA Parser의 주요 기능:

** Sentence의 유효성 검사 (체크섬 확인)

** Sentence Type을 기반으로 필요한 데이터만 추출

** 여러 장비에서 발생하는 Sentence를 필터링하여 특정 데이터만 처리


*TCP와 UDP (Unicast, Multicast, Broadcast)*

- 센서 데이터는 네트워크를 통해 전달되며, 전송 방식에 따라 TCP 또는 UDP가 사용됩니다.

- TCP (Transmission Control Protocol)

** 신뢰성 보장 (데이터 손실 시 재전송)
** 연결 기반(클라이언트-서버 간 연결 유지)
** 흐름 제어 및 오류 검사
** 예: HTTP, FTP, SSH, 데이터베이스 연결

- UDP (User Datagram Protocol)

** 비연결형 (데이터 전송 후 확인 없음)
** 속도가 빠르지만 신뢰성이 낮음 (패킷 손실 가능)
** 실시간 데이터 스트리밍, 센서 데이터 전송 등에 사용

*UDP 기반 데이터 전송 방식*

* Unicast (단일 전송)
** 한 대의 송신자가 특정 수신자에게 데이터 전송
** 예: 특정 클라이언트로 데이터 스트리밍
** 사용 예시: socket.sendto(data, (ip, port))

* Multicast (그룹 전송)
** 특정 그룹(멀티캐스트 주소)에 속한 여러 수신자가 동일한 데이터를 수신
** 네트워크 장비가 멀티캐스트 패킷을 그룹 멤버에게만 전달
** 예: 해양 장비에서 여러 클라이언트에게 항법 데이터 전송

* Broadcast (전체 전송)
** 같은 네트워크에 있는 모든 장치에게 데이터 전송
** 브로드캐스트 주소(예: ***************) 사용
**  예: DHCP 요청, 네트워크 전체에 정보 전파


*K3s*

- K3s는 Kubernetes의 기능은 그대로 유지하면서, 가볍고 빠르게 실행되도록 만든 **경량 버전의 Kubernetes**입니다.
설치가 간편하고 하나의 실행 파일로 구성되어 있으며, 다양한 CPU 아키텍처(예: ARM, x86)에서 동작합니다.

- HiNAS SVM 시스템은 K3s를 통해 Backend, Redis, RTSP 모듈 등을 Pod, Container 단위로 배포하며, K3s는 이를 자동으로 실행하고 감시하며, 문제가 발생하면 복구까지 수행합니다.


*K9s*

- K9s는 Kubernetes 클러스터를 터미널 기반 인터페이스(TUI) 로 실시간 모니터링하고 관리할 수 있도록 도와주는 경량 CLI 도구입니다.
별도의 GUI 없이도 kubectl 없이 주요 리소스를 조회하고 제어할 수 있습니다.

- HiNAS SVM 시스템은 운영 중인 Pod, Deployment, Redis 상태 등을 확인하거나, 로그를 실시간 추적하기 위해 K9s를 사용합니다.

- `kk` 명령어를 통해서 k9s를 실행시킬 수 있으며 주로 활용하는 단축키는 다음과 같습니다.
[cols="1,1,3", options="header"]
|===
| 단축키 | 설명 | 예시
| `:` | 명령어 입력 모드로 전환 | `:pods` - 현재 클러스터의 모든 Pod 목록 조회
| `d` | 현재 Pod의 상세 정보 조회 | `d` - 현재 선택된 Pod의 상세 정보 표시
| `l` | 현재 Pod의 로그 조회 | `l` - 현재 선택된 Pod의 로그 출력
| `s` | 현재 Pod의 shell 접속 | `s` - 현재 선택된 Pod의 쉘에 접속
| `ctrl + d` | 현재 Pod의 삭제 | `ctrl + d` - 현재 선택된 Pod 삭제
|===


*Argo CD*

- Argo CD는 GitOps 기반으로 동작하는 Kubernetes 배포 자동화 도구입니다.
Git에 정의된 상태를 기준으로 클러스터 상태를 동기화하고, 변경이 있을 경우 자동으로 반영합니다.

- 즉, "Git에 있는 설정 = 클러스터의 실제 상태" 가 되도록 유지하며, Git이 배포의 단일 진실(Single Source of Truth)이 됩니다.

*GitOps*

- GitOps는 Git을 단일 진실의 출처(Single Source of Truth) 로 삼아 인프라 및 애플리케이션을 자동으로 배포 및 운영 관리하는 방식입니다.

- 개발자는 Kubernetes 리소스(예: Deployment, ConfigMap 등)를 Git 저장소에 정의하고, 이를 변경함으로써 배포가 자동으로 수행됩니다.
배포 상태는 Git의 내용과 실시간 비교되며, 불일치 시 자동 또는 수동으로 동기화할 수 있습니다.

- 대표적인 GitOps 도구에는 Argo CD, Flux 등이 있으며, Git 기반의 형상 관리 + CI/CD 통합을 실현할 수 있습니다.


*Skopeo*

- Skopeo는 컨테이너 이미지를 원격 레지스트리 간에 직접 복사하거나, 이미지 메타데이터를 조회하는 데 사용하는 CLI 도구입니다.
로컬에 이미지를 pull하지 않고도 복사, 검사, 서명 검증 등이 가능하다는 점이 특징입니다.

- HiNAS SVM 시스템에서는 ECR에 있는 컨테이너를 Kubernetes 클러스터로 직접 가져오기 위해 Skopeo를 사용합니다.

*Amazon ECR (Elastic Container Registry)*

- Amazon ECR은 AWS에서 제공하는 완전관리형 Docker 컨테이너 이미지 저장소입니다.
보안, 고가용성, 스케일링을 기본 제공하며, IAM, ECS, EKS, CodePipeline 등 AWS 서비스와 긴밀하게 통합됩니다.

- HiNAS SVM 시스템에서는 빌드된 이미지(Backend, Redis, RTSP 등)를 ECR에 push하고, K3s 클러스터가 이를 pull하여 배포합니다.
인증은 AWS CLI 또는 IAM Role을 통해 자동으로 처리되며, CI/CD 파이프라인에 통합되어 사용됩니다.

*Docker Registry*

- Docker Registry는 Docker 이미지를 저장하고 배포하는 서버입니다. HiNAS시스템에서는 K3S 내부의 프라이빗 Docker Registry에 이미지를 저장하고, K3s에서 이를 pull하여 Pod로 배포합니다.

