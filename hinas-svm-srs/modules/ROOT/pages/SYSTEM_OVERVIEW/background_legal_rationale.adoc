=== 적용 근거 및 참고 조사 (Background and Legal Rationale)


==== 대형선 접안 절차 요약

1. 초기 감속 및 예선 지원 준비

* 거리 기준: 부두 전방 1,000～1,500m 지점

* 속도 기준: 타력에 의한 진행속도 2노트 이하로 감속

* 방법:
** 선박 기관 정지
** 조타기 및 예선의 보조를 통해 침로 유지
** 수압저항을 활용한 자연 감속 유도

2. 부두 접근 및 정지

* 접근 거리: 부두로부터 선폭의 약 3~4배 거리까지 접근

* 정지 방법:
** 예선의 도움을 받아 선박을 부두와 평행하게 정지
** 예선 + 선수 횡추진기(Bow Thruster)로 횡 이동
** 부두에 정확히 접안

3. 접안 속도 기준 (인천항 도선 표준매뉴얼 기준)

* 부두로부터 선폭 거리 지점: 0.2노트 (약 0.1 m/s)
* 접안 직전: 0.1노트 (약 0.05 m/s)

4. 도선 불가 조건

* 최대순간풍속이 초속 13m 이상인 경우:
* 도선 작업이 매우 위험하거나 곤란한 상태로 간주


==== 도선 관련 법령 및 안전 기준


[quote, 제33조(항만시설 이용자의 의무), https://law.go.kr/LSW/lsInfoP.do?lsiSeq=223573&efYd=20211209]
____
① 항만시설을 이용하는 자는 보안사건이 발생하는 것을 예방하기 위하여 다음 각 호에 해당하는 행위를 하여서는 아니 된다. +

4) 항만시설 내 해양수산부령으로 정하는 구역에서 항만시설보안책임자의 허가 없이 촬영을 하는 행위
____


- 도선법 제20조: 일정 규모 이상 선박의 강제 도선 규정
- 선박의 입항 및 출항에 관한 법률: 입출항 시 도선사 필요
- 인천항 도선 표준매뉴얼: 접안 속도 0.1~0.2노트 유지 기준

==== 시스템 필요성 및 설계 기준 도출

- 항만 접안 시 속도·거리 시각화 필요
- 타선과의 충돌위험 실시간 가시화 필요 (DCPA/TCPA)
- 예선 위치 및 협조 시각적 보조 수단 필요


==== 제품의 사용자 가치

===== 사용자가 느끼는 문제점

선장과 항해사들은 다음과 같은 문제를 경험하고 있습니다.

*접안 및 협수로 운항 시 문제점*

- 도선사의 지휘 하에 접안 또는 협수로 항해 시, 자선이 타선이나 안벽에 얼마나 근접했는지 직접 확인하고 싶어함.
- 하지만 도선사와의 관계를 고려해 브릿지 밖으로 나가 확인하는 것이 부담됨.
- 대형선의 경우 선체가 길어 브릿지나 윙브릿지에서 선체 끝까지 시야 확보가 어려움.
- 대형선은 보통 200m 이상으로, 브릿지 밖으로 나가도 선미까지 시야 확보가 어렵다.
- 선미 등 선체 끝의 위치는 항해사들과의 무전을 통해 파악하나, 시각 정보가 부재하다.
- 도선 사고의 주요 원인 중 하나는 도선사와 선장/항해사 간의 소통 부재다.

*항해 중 정보 인식의 한계*

- 1/2/3항사는 open sea 혹은 연안 항해 시, 기존 항통 장비만으로는 충돌/접촉 사고를 방지하기에 정보가 부족하다고 느낌.
- AIS 데이터는 시간 지연 및 거리 오차가 존재한다.
- 동남아, 중국 등 일부 연안에는 AIS 미탑재 어선이 많아 탐지 불가.
- Radar는 수 십~100m 이내의 근거리 물체는 인식에 한계가 있음.
- 목선 등 Radar로 인식이 어려운 물체가 존재.
- 파고가 높을 경우 작은 선박은 시야에서 가려질 수 있음.

===== 사용자에게 주는 가치

본 시스템은 다음과 같은 가치를 제공합니다.

- 기존 장비로는 인식이 어려운 정보를 제공합니다:
  * 자선 중심 시점의 시각 정보
  * 근거리 대상 탐지
  * 실시간 업데이트
  * 육안에 가까운 시각 정보

*효과 및 기대 가치*

1. 충돌 및 접촉 사고에 대한 예방 및 후처리 대응이 가능함.
2. 도선 중 소통이 불충분한 상황에서도 주변 상황 인식이 가능해 심리적 안정감 제공.

*운항 상황별 기대 효과*

*Open Sea*

항통 장비로 감지되지 않거나, 감지되었지만 CCTV/육안으로 보기 어려운 물표를 시각적으로 인지 가능.

*접안 시*

사용자가 육안으로 직접 확인하지 않아도, 안벽과의 거리 및 선박 자세를 효율적으로 파악 가능.

*협수로 항해 시*

수로 벽과의 거리 및 위치 관계를 직관적으로 인식할 수 있도록 도와줌.

==== 경쟁사 기술 분석


===== Seadronix NAVISS

.Seadronix NAVISS
image::SYSTEM_OVERVIEW/background/seadronix.png[align="center", 400]


.Seadronix NAVISS 2
image::SYSTEM_OVERVIEW/background/naviss.png[align="center", 400]


.Seadronix NAVISS 3
image::SYSTEM_OVERVIEW/background/naviss2.png[align="center", 400]

https://seadronix.com/product/naviss/[Seadronix NAVISS]

*Around View*

- 비전으로 물체 식별
- 물체 구간에 따른 충돌 알람
- 터그 위치 설정

*카메라 뷰*

- 개별 카메라 선택
- 충돌 경보 카드 노출
** 선박 기준 방향
** 선박과의 거리
** 구간별 위험

*이벤트 로그*

* 일자별 그룹 및 동영상 다운로드 가능


[cols="1,6", options="header"]
|===
| 구분 | 내용

| 장점 |

- 탑뷰 제공

- 사람 및 주변 탐지

- 카메라별 개별뷰 제공

- 거리추정

- 충돌 경보

| 단점 |

- 선박위의 디텍션을 하려변 카메라 설치위치가 배안에 존재하여야하며, Surround View가 부정확함

- 개별뷰 1개만 지원.

|===


===== Samsung S-Vision

.Samsung S-Vision
image::SYSTEM_OVERVIEW/background/svision.png[align="center", 400]

.Samsung S-Vision Compass
image::SYSTEM_OVERVIEW/background/svision_4.png[align="center", 400]

.Samsung S-Vision Surround View
image::SYSTEM_OVERVIEW/background/svision_3.png[align="center", 400]

.Samsung S-Vision Zoom in & Out
image::SYSTEM_OVERVIEW/background/svision_2.png[align="center", 400]

.Samsung S-Vision POE & UPS
image::SYSTEM_OVERVIEW/background/svision_1.png[align="center", 400]

*자선 정보 표시*

SVISION은 AIS 및 VDR로부터 수집한 본선의 주요 네비게이션 및 제원 정보를 통합하여 표시함.

- IDENTIFIERS: IMO, MMSI, CALL SIGN

- NAVIGATION: LAT, LON, DRAFT, ROLL, PITCH, HEADING(HDG), SPEED OVER GROUND(SOG), COURSE OVER GROUND(COG), RATE OF TURN(ROT)

- MACHINERY: RPM, RUDDER ANGLE, 알람 상태

- ENVIRONMENT: 풍속 및 풍향(WIND), 조류(CURRENT), 수심(DEPTH)

- DIMENSIONS: 전장(LOA), 전폭(BREADTH), 깊이(DEPTH)

*주변 선박 정보*

AIS 및 ARPA 기반의 주변 선박 정보를 수집하여 시각화

* IDENTIFIERS: IMO, MMSI, CALL SIGN

* NAVIGATION: LAT, LON, HDG, SOG, COG, ROT

* RISK 판단 지표:

** 거리(DIST)

** 최근접 접근 거리(DCPA: Distance at Closest Point of Approach)

** 최근접 접근 시각(TCPA: Time to CPA)

* 시각적 강조: 근거리 선박은 노란색 또는 빨간색으로 강조하여 표시

*컴파스 표시*

- 실제 선박의 방향과 연동된 디지털 컴파스 UI 제공


*거리 가이드라인 (Distance Guide Line)*

- 사용자 정의 기준선 설정 가능

- 근거리 물체와의 거리 감각 보조


[cols="1,6", options="header"]
|===
| 구분 | 내용

| 장점 |

- 3D 탑뷰 제공

- AIS 연동으로 영상에 선박 정보 표시 가능

| 단점 |
- Surround View가 곡선으로 왜곡되어 표현됨

- 보고 싶은 방향을 선택하려면 사용자의 조작이 필요함

- 전방 및 후방에 블라인드 영역 존재
|===


===== SEANET 선박 AROUND VIEW

.SEANET 선박 AROUND VIEW
image::SYSTEM_OVERVIEW/background/seanet.png[align="center", 400]


https://www.sea-net.co.kr/?param=business_sees_03&lang=kr[SEANET Homepage]

- 선박 외부 360° 화면 수집/감시
- 라이다 센서를 이용한 선박 주변 위협 물 감지 및 경보
- IP/방폭 카메라 하우징
- CCTV 및 기타 연동장비 화면 전송

===== LOOKOUT

.Lookout
image::SYSTEM_OVERVIEW/background/lookout.jpeg[align="center", 400]

https://www.getalookout.com/[Lookout Homepage]

- AI기반 근거리 위험 탐지
- 중소형 선박 중심
