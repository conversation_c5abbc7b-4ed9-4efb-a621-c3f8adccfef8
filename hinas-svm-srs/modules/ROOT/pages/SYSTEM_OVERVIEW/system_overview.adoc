=== 시스템 개요 (System Overview)

이 문서는 HiNAS SVM(Surrounding View Monitoring) 시스템의 기능 및 설계에 대해 상세히 설명합니다. HiNAS SVM은 선박의 접안 작업을 지원하기 위해 개발된 자율형 ANAS(Advanced Navigator Assistance System, 고급 항해 보조 시스템) 기술입니다.

이 서비스의 주요 목적은 항만 지역 및 접안 작업 중 선박의 안전하고 효율적인 운항을 지원하기 위해 실시간으로 종합적인 시각 정보를 제공하는 것입니다. 본 시스템은 여러 대의 카메라로부터 입력된 영상을 통합하여 선박 주변의 360도 뷰를 생성하며, 선박 상태에 대한 핵심 정보를 제공합니다.

HiNAS SVM의 기능 범위는 다음과 같습니다.

[NOTE]
====
비고: 시스템 구성은 선박의 사양 및 선주 요구 사항에 따라 조정될 수 있습니다. Optional 기능은 선택적으로 제공되며, 추후 HiNAS 365를 통해서 기능을 제공할 수 있습니다.
====

- *Surround View*: 다수의 카메라를 통해 선박 주변 360도 시야를 구성하며, 좌측 화면에 실시간 표시합니다.

- *Side View*: 개별 카메라의 원본 화면을 확대하여 특정 영역을 정밀 확인할 수 있습니다.

- *Vessel Status Indicator*: 선박의 주요 운항 정보를 실시간으로 시각화하여 안정적인 조작을 지원합니다.

- *Target Information Display (Optional)*: 위험 선박 정보를 시각적으로 표시하여 충돌 회피를 지원합니다.

- *Target AR (Optional)*: 선박 주변의 위험 요소를 AR(증강 현실)로 시각화하여 운항자가 직관적으로 인지할 수 있도록 지원합니다.

- *Inference (Optional)*: 객체 인식 기반의 운항 보조 기능을 제공합니다.

- *Video Recording (Optional)*: 중요 상황 분석 및 리뷰를 위한 영상 저장 기능을 제공합니다.

- *CCTV & PTZ (Optional)*: 선박 내외부 감시 및 카메라 제어 기능을 제공합니다

- *SVM Setting*: 시스템 설정을 통해 카메라 및 영상 처리 파라미터를 조정할 수 있습니다.

[TIP]
====
기능 설명은 사용자의 실제 운항 흐름에 따라  **인지 → 판단 → 분석 → 설정**의 순서로 구성되어 있습니다.

- *실시간 시야 확보 우선*: Surround View와 Side View는 항해 중 가장 자주 활용되는 주요 시야 기능으로 최우선 배치하였습니다.

- *운항 판단 보조*: Vessel Status Indicator와 Target 관련 기능은 시야 정보를 기반으로 위험 요소를 판단·표시하는 보조 기능이며, Target → AR → Inference 순으로 점진적 정보를 제공합니다.

- *사후 확인 기능 후순위*: Video Recording과 CCTV/PTZ는 실시간 판단보다는 분석·관찰 목적이므로 후순위로 배치하였습니다.

- *설정 기능은 마지막에 배치*: 전체 시스템 설정 기능은 사용자의 조작 흐름상 마지막 단계로 구성하였습니다.
====