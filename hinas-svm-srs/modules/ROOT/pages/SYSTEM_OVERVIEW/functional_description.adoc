=== 기능 설명 (Functional Description)
==== Surround View
Surround View는 선박 주변에 배치된 여러 대의 고화질 카메라로부터 영상을 통합하여, 주변 환경을 360도를 현시합니다.
SVM Backend에서 실시간으로 수신되는 IP Camera 영상 스트림을 처리하고 연결하여 Surround View를 제공하며, 화면상에 현시합니다.
아래 이미지의 좌측화면의 영상과 같이, 360도로 이어진 하나의 Surround View 영상을 제공합니다.

Surround View 영상을 통해서 항새중 선박 주변 물체와의 상대적 위치를 모니터링하는 데 도움이 되며, 항만 지역에서의 안전한 항해 및 접안 작업을 지원합니다.

.Surround View
image::SYSTEM_OVERVIEW/system_overview_surround_view.png[align="center",500]

Surround View의 구성 요소로는 다음과 같은 기능이 있습니다:

- 등거리선 (Equidistance Lines)
- 카메라 줌 및 회전 (Camera Zoom & Rotation)

<<<
===== Equidistance Guide


등거리선 현시는 ON/OFF 스위치릴 통해서 제어할 수 있습니다.
ON/OFF 설정을 통해서 Surround View와 Side View에 대한 등거리선을 키거나 끌 수 있습니다.

.Surround View Equidistance Guide
image::SYSTEM_OVERVIEW/system_overview_surround_view_equidistance_guide.png[align="center",500]

등거리선 간격은, 거리 설정 버튼을 클릭하여 거리 설정 메뉴에 접근할 수 있으며, 선박 주변의 최소 거리를 원하는 값으로 지정할 수 있습니다.

설정 가능한 거리는 4가지로 제공되며, 거리의 간격은 선박 사이즈에 따라 상이합니다.
설정된 최소 거리(t)를 기준으로 3개의 등간격 선이 표시됩니다. 이 선들은 각각 t×1, t×2, t×3 위치에 나타납니다.
예를 들어 5m, 10m, 15m, 20m가 존재할 때, 10m를 선택한 경우, 등거리선은 10m, 20m, 30m 지점에 표시됩니다.

거리선은 총 세 단계로 표시되며, 가장 가까운 선은 빨간색으로, 나머지 외곽의 두 선은 노란색으로 표시됩니다.

<<<
===== Camera Zoom & Rotation

.Surround View Camera Zoom & Rotation
image::SYSTEM_OVERVIEW/system_overview_surround_view_camera_zoom_and_rotation.png[align="center",500]

줌 인/아웃 기능은 선박 주변의 물체를 보다 정밀하게 확인할 수 있도록,
Surround View 화면의 좌상단, 우상단, 좌하단, 우하단 네 구역에서 확대 및 축소 기능을 제공합니다.



해당 기능은 두 가지 방식으로 사용할 수 있습니다.

1.줌 버튼을 이용하는 방법으로, 사용자가 버튼을 클릭하면 서라운드 뷰가 확대되며, 다시 클릭하면 원래 화면 크기로 복원됩니다.

2.화면의 네 모서리 중 하나에 마우스를 올려 클릭하는 방식으로, 해당 영역이 확대되며, 다시 클릭 시 초기 상태로 돌아갑니다.



.Surround View Rotation
image::SYSTEM_OVERVIEW/system_overview_surround_view_rotation.png[align="center",500]

회전 기능의 경우, Surround View 화면의 좌측 상단에 위치한 회전 버튼을 클릭하여 활성화할 수 있습니다.
화면을 회전하게 되면, 시계 방향으로 Surround View가 90도 회전하며, 화면 전체로 확대되며, 다시 클릭하면 원래 방향으로 돌아갑니다.


<<<
==== Side View

.Side View
image::SYSTEM_OVERVIEW/system_overview_side_view.png[align="center",500]

.Side View Zoom
image::SYSTEM_OVERVIEW/system_overview_side_view_zoom.png[align="center",500]


사이드 뷰(Side View) 기능은 화면 오른쪽에 네 개의 카메라 영상을 표시하며, 각 영상에는 해당 카메라의 이름이 라벨로 표시됩니다.
운항자는 원하는 영상을 클릭하여 확대할 수 있으며, 다시 클릭하면 원래 크기로 축소됩니다.
단, 오류가 발생한 카메라는 확대가 불가능하며, 각 카메라별로 상이한 오류 상태가 표시될 수 있습니다.


.Side View Please Select Camera
image::SYSTEM_OVERVIEW/system_overview_side_view_select_camera.png[align="center",500]


.Side View Please Camera Selector
image::SYSTEM_OVERVIEW/system_overview_side_view_camera_selector.png[align="center",500]

운항자는 **사이드 뷰 선택기(Selector)**를 통해 최대 네 개의 카메라를 선택할 수 있으며, 선택된 카메라는 지도에 위치 정보와 함께 실시간 영상으로 표시됩니다.
선택된 카메라 수는 화면에 표시되며, 사용자는 선택 내용을 저장하거나 취소할 수 있습니다.

영상 녹화 상태는 화면 우측 상단에 표시됩니다.
정상적으로 녹화 중일 경우 "SVM REC"이라는 문구가 빨간색으로 표시되며, 문제가 발생하면 회색으로 바뀌고 오류 메시지 및 녹화되지 않은 카메라 목록이 함께 표시됩니다.



<<<
==== Target Information Indicator

.Target Information Indicator
image::SYSTEM_OVERVIEW/system_overview_target_information_indicator.png[align="center",500]


Target Information Indicator는 Surround View 및 Side View만으로는 타선의 위치 및 위험도를 직관적으로 파악하기 어려운 저시정 상황(예: 해무, 야간)에 대응하기 위해 고안된 보조 기능입니다.

이 기능은 자선 주변의 타선을 감지하고, 각 타선의 위치, 속도, 침로, CPA(Closest Point of Approach), TCPA(Time to CPA), 거리, 위험도 등의 항해 핵심 정보를 시각적으로 현시하여 운항자의 충돌 회피 판단을 지원합니다.

특히 혼잡한 해역(Congested)과 개방 해역(Open Sea) 운항 상황에 따라 충돌 위험 판단 기준을 분리하여 적용할 수 있으며, 위험도가 높은 타선은 자동으로 강조되며, 추적이 중단된 타선도 최종 위치와 함께 계속 현시되어 상황 인지의 연속성을 보장합니다. 해당 기능은 SVM 시스템의 핵심 판단 보조 요소로서, 선박의 안전 운항과 충돌 회피 의사결정의 정확도를 향상시키는 역할을 합니다.


<<<
==== Vessel Status Indicator
.Vessel Status Indictor in Surround View
image::SYSTEM_OVERVIEW/system_overview_vessel_status_indicator_surround_view.png[align="center",500]

Surround View 화면에서 표시되는 데이터는 다음과 같습니다.

*STW (Speed Through Water)*

- 선박이 수면을 기준으로 얼마나 빠르게 이동하고 있는지를 실시간으로 나타냅니다. 이를 통해 운항자는 항로를 조정하거나 연료 효율을 최적화하는 데 필요한 결정을 내릴 수 있습니다.

*Theoretical Wind (Wind Direction, Speed)*

- 바람의 방향과 속도 정보를 제공하며, 이는 환경 조건을 파악하는 데 중요한 요소입니다. 특히 기상이 불량하거나 좁은 수역에서 운항할 때, 선박 운항 전략을 조정하고 안전 운항을 확보하는 데 유용합니다.


.Vessel Status Indicator in Target Information Display
image::SYSTEM_OVERVIEW/system_overview_vessel_status_indicator_target_infromation_indicator.png[align="center",500]

Target Information Display에서 표시되는 자선 데이터는 다음과 같습니다.

*COG (Course Over Ground)*

GPS와 자이로콤퍼스에서 수집되며, 조류나 외부 환경의 영향을 반영한 선박의 실제 진행 방향을 나타냅니다. 정밀한 항로 유지 및 진로 조정을 위해 필수적인 정보입니다.

*Heading*

자이로콤퍼스를 기반으로 하며, 선박이 진북(True North)을 기준으로 어느 방향을 향하고 있는지를 보여줍니다. 접안 시 선박 정렬이나 정확한 항로 유지를 위해 중요합니다.

*SOG (Speed Over Ground)*

해수면을 기준으로 한 선박의 실제 이동 속도를 나타냅니다. 실시간 속도 데이터를 통해 선박의 움직임을 정확히 파악하고 제어할 수 있으며, 좁은 수역이나 항만 등 복잡한 환경에서 안전하고 효율적인 운항을 지원합니다.



<<<
==== Video Recording

.Video Recording
image::SYSTEM_OVERVIEW/system_overview_video_recording.png[align="center",500]

Video Recording 기능은 사고 발생 시점 전후의 선박 주변 영상을 확인할 수 있도록 하여, 상황 분석 및 판단 근거 확보에 활용되는 핵심 기능입니다. 실시간으로 수신한 영상 스트림을 일정 간격으로 비디오 파일로 저장하고, 사용자 요청에 따라 해당 영상을 조회하거나 외부 저장장치로 다운로드할 수 있도록 지원합니다.

사용자는 Video Record 화면에서 특정 날짜를 선택한 후, Surround View, 선수(Bow), 선미(Stern) 등 관심 카메라를 지정하여 녹화된 영상을 확인할 수 있습니다. 선택한 영상은 재생 속도 조절이 가능하며, 필요한 경우 다운로드 기능을 통해 USB 등 외부 장치로 저장할 수 있습니다.

녹화 상태는 화면 우측 상단에 실시간으로 표시되며, 모든 카메라가 정상적으로 녹화 중일 경우 “SVM REC” 또는 “Navigation REC” 문구가 빨간색으로 나타납니다. 반면, 하나 이상의 카메라에서 녹화 이상이 감지되면 해당 문구가 회색으로 바뀌며, 오류 메시지와 함께 녹화되지 않은 카메라 목록을 확인할 수 있는 View 버튼이 함께 제공됩니다.

본 기능은 고장 추적, 운항 검토, 사고 원인 분석 등 다양한 상황에서 영상 데이터를 기반으로 한 정확한 판단을 가능하게 하며, 선박 운영의 안전성과 신뢰성을 높이는 데 기여합니다.


<<<
==== Inference

.Inference
image::SYSTEM_OVERVIEW/system_overview_inference.png[align="center",500]

Inference 기능은 카메라에서 선박 또는 암초등의 주요 대상을 자동으로 인식하고, 그 결과를 실시간으로 화면에 보여주는 기능입니다.
이 기능을 통해 사용자는 선박 주변 상황을 더 정확하게 파악할 수 있으며, 특히 위험 요소를 빠르게 감지하고 대응하는 데 도움을 받을 수 있습니다.

시스템은 각 카메라에서 수집한 영상을 바탕으로 대상의 종류(예: 선박, 암초 등), 위치, 거리 정보를 계산하고, 이를 Surround View 및 Side View 화면에 표시합니다.


<<<
==== Target AR

Target AR 기능은 선박 주변의 위험 선박(타선)에 대한 정보를 Surround View 화면에 증강현실(AR) 형태로 표시해주는 기능입니다.
유저는 실제 카메라 보이는 선박의 이름, 방향 등 주요 정보를 직관적으로 확인할 수 있어, 복잡한 항해 환경에서도 빠르고 정확한 판단이 가능합니다.

.Target AR - Tug Boat
image::SYSTEM_OVERVIEW/system_overview_target_ar_1.png[align="center",500]

Target Information Display와 연계되어, Target Information Display에서 타선을 선택하면 Side View 및 Surround View 화면에 해당 타선의 위치와 정보를 AR 형태로 표시합니다.

.Target AR - Ship
image::SYSTEM_OVERVIEW/system_overview_target_ar_2.png[align="center",500]


이를 통해 운항자는 주변 선박의 위치와 정보를 실제 영상 위에서 바로 파악할 수 있어, 레이더나 텍스트 정보만으로는 놓치기 쉬운 상황도 쉽게 인지할 수 있습니다.
Target AR 기능은 특히 좁은 해역이나 다수의 선박이 밀집한 상황에서 운항자의 상황 인지력과 대응 속도를 향상시키는 데 중요한 역할을 합니다.

<<<
==== CCTV & PTZ

CCTV 기능은 선박 내부 또는 외부에 설치된 카메라 영상을 실시간으로 확인할 수 있도록 지원하는 기능입니다. CCTV 기능의 경우 Optional 기능으로, CCTV가 `RTSP` 프로토콜을 지원하는 경우에만 제공됩니다.
시스템 화면내에서 SVM의 Side View 카메라 뿐만 아니라 CCTV의 영상을 실시간으로 확인할 수 있으며, 선박 전·후·좌·우 등 다양한 위치에 설치된 CCTV를 통해 선박 내외부의 주변 상황을 파악할 수 있습니다.

.CCTV
image::SYSTEM_OVERVIEW/system_overview_cctv.png[align="center",500]


PTZ(Pan-Tilt-Zoom) 카메라 제어 기능은 선박에 설치된 회전형 카메라를 원격으로 조작할 수 있도록 지원하는 기능입니다. 운항자는 시스템 화면을 통해 카메라의 방향을 상하좌우로 이동시키거나, 줌 인·아웃을 조작하며, 필요 시 카메라에 부착된 와이퍼를 작동시켜 렌즈를 닦을 수도 있습니다.
사용자는 PTZ 카메라의 방향을 조정하여 특정 영역을 자세히 관찰하거나, 줌 기능을 통해 멀리 있는 물체를 확대하여 볼 수 있습니다.
PTZ 기능의 경우 Arges 사의 PTZ 카메라에 한정되어 제공됩니다.

.PTZ
image::SYSTEM_OVERVIEW/system_overview_ptz.png[align="center",500]


CCTV 와 PTZ 기능은 외부 시스템과 연계되어 제공되며, 선박 구성에 따라 해당 기능이 제공되지 않을 수 있습니다.
또한, 외부 시스템과 연계되어 제공되므로 CCTV와 PTZ 에서는 녹화 기능을 제공하지 않습니다.

<<<
==== SVM Setting


===== Ship Spec & CCRP
SVM 시스템의 Ship Specification 및 CCRP 설정 기능은 커미셔닝 단계에서 선박의 기본 제원과 센서 기준점을 정의하기 위한 초기 설정 절차입니다. 해당 기능은 설정 페이지를 통해 선박 정보, 시스템 기본값, 카메라 관련 정보 등을 일괄 구성할 수 있도록 지원하며, 설정된 값은 Sensor Fusion 및 영상 보정의 기준으로 활용됩니다.

.Setting Ship Spec
image::SYSTEM_OVERVIEW/system_overview_setting_ship_spec.png[align="center",500]



Stage 1에서는 선박의 IMO 번호, 선박명, 길이(Length), 너비(Beam), 설계 흘수(Scantling Draught) 등을 입력합니다. 이 값들은 SVM 시스템 내 공간 좌표계와 센서 배치 유효 범위를 정의하는 기준으로 사용되며, 정확한 입력이 필수적입니다. 특히 Scantling Draught는 구조적으로 허용 가능한 최대 흘수이며, Measured Draught와 비교하여 선박의 실제 운항 상태를 보정하는 데 활용됩니다.


.Setting CCRP
image::SYSTEM_OVERVIEW/system_overview_setting_ccrp.png[align="center",500]

Stage 2에서는 CCRP(Central Control Reference Point)를 기준으로 GPS, 레이더, 카메라 등 주요 센서들의 상대적인 위치 좌표(X, Y)를 입력합니다. CCRP는 선박 내부 좌표계의 기준점 역할을 하며, 두 개의 설정값을 사전 정의하고 선택적으로 적용할 수 있도록 설계되어 있습니다. CCRP 좌표계는 선미 중앙 기준으로 정의되며, 각 센서의 좌표는 이를 기준으로 한 상대 위치입니다. 모든 좌표 입력은 선박 치수에 따른 유효 범위 내에서 이뤄져야 하며, 실시간 유효성 검사를 통해 입력 오류를 즉시 확인할 수 있습니다.


===== Camera Configuration
Camera Configuration 화면은 선박에 설치된 카메라들의 종류, 개수, 위치, 네트워크 상태를 한눈에 확인하고 설정할 수 있는 기능입니다.

.Setting Camera Configuration
image::SYSTEM_OVERVIEW/system_overview_setting_camera_configuration.png[align="center",500]

사용자는 화면 상단에서 전체 카메라 수와 사용 중인 모델명을 확인할 수 있으며, 각 카메라는 선박 도식 위에 번호로 표시되어 설치 위치를 직관적으로 파악할 수 있습니다. 우측 표에서는 카메라 별로 이름, IP 주소, 설치 좌표(X: 앞뒤, Y: 좌우, Z: 높낮이), 그리고 네트워크 연결 상태(Ping)를 확인하며 수정할 수 있습니다.

해당 기능을 통해서 사용자는 커미셔닝 단계에서, 카메라의 위치 정보를 입력하고, Ping을 통해서 연결 상태를 확인 할 수 있습니다.

===== Side View Calibration
.Setting Side View Calibration
image::SYSTEM_OVERVIEW/system_overview_setting_camera_calibration.png[align="center",500]

Side View Calibration은 선박에 설치된 각 카메라의 영상 정렬 상태를 수동으로 보정하여, Side View 영상의 정확성과 일관성을 확보하기 위한 기능입니다. 실제 설치 환경에서는 카메라의 물리적 위치나 각도, 렌즈 특성 등의 차이로 인해 영상이 왜곡되거나 시야가 어긋날 수 있습니다. 본 기능은 사용자가 각 카메라에 대해 개별적으로 영상의 배율과 시야 각도를 조정할 수 있도록 설계되어 있습니다.

사용자가 설정할 수 있는 주요 항목은 다음과 같습니다:

- *Scale*: 영상의 확대·축소 비율을 조정하는 값으로, 실제 거리 대비 영상의 크기를 일치시키는 데 사용됩니다.

- *Pitch*: 카메라의 상하 기울기를 조정하는 값입니다. 카메라가 위나 아래로 기울어진 경우, 해당 각도를 보정합니다.

- *Roll*: 카메라의 좌우 기울기를 조정하는 값입니다. 설치 시 수평이 맞지 않아 영상이 기울어진 경우에 사용됩니다.

- *Yaw*: 카메라의 좌우 회전 방향을 조정하는 값입니다. 정면이 아닌 방향을 향하고 있을 경우 화면을 정렬하는 데 사용됩니다.

설정값은 실시간으로 반영되며, 사용자는 좌측의 Surround View 영상과 우측 상단의 개별 카메라 영상에서 설정된 결과를 즉시 확인할 수 있습니다.
각 카메라의 설정은 독립적으로 관리되며, 하나의 카메라에 적용된 보정값은 다른 카메라에 영향을 미치지 않습니다.

보정값을 설정한 후에는 “Apply camera setting to inference” 버튼을 통해 Inference 시스템에 적용할 수 있습니다.

===== Surround View Calibration
.Setting Surround View Calibration
image::SYSTEM_OVERVIEW/system_overview_setting_surround_view_calibration.png[align="center",500]

Surround View Calibration은 Surround View 이미지를 생성할 때, 각 카메라들이 자연스럽게 연결될 수 있도록 설정할 수 있는 설정값입니다.
선박에서 보이는 시야 범위는 Visible Distance를 통해서 조절 할 수 있습니다.

보정 항목은 다음과 같습니다.

*Visible Distance*

Surround View 화면에서 표시되는 최대 거리로, 실제 관측 가능한 거리 범위를 설정합니다. Recommended 모드를 선택하면 시스템이 자동으로 거리 값을 계산하여 적용하며, Etc(사용자 입력) 모드에서는 수치를 직접 입력할 수 있습니다.

*Measured Draught*

실제 선박의 흘수(수면 아래 잠긴 깊이) 값으로, 수직 시야 계산에 사용됩니다. Scantling Draught 이하의 값이어야 하며, Surround View 시야 범위 결정에 영향을 미칩니다.

*Bow Camera Slope / Stern Camera Slope*

선수 및 선미에 설치된 카메라의 영상 경사도입니다. 화면의 왜곡을 최소화하고 자연스러운 화면 연결을 위해 수치를 조정할 수 있습니다.

*Mask Gradient Width*

카메라 간 영상이 중첩되는 구간의 전환 구간 너비를 설정하는 값입니다. 이 값은 영상 이질감을 줄이기 위한 마스킹 처리에 사용되며, 주변 영상의 자연스러운 전환을 유도합니다.
