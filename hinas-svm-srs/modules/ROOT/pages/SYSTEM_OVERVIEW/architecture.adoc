=== 아키텍처 (Architecture)

==== Overall Architecture

.Overall Architecture
image::SYSTEM_OVERVIEW/architecture/overall_architecture.png[align="center", 600]


<<<
==== Data Flow Diagram

===== Image Flow

.Image Data Flow
image::SYSTEM_OVERVIEW/architecture/dfd_image_flow.png[align="center",600]

1. 각 IP 카메라는 RTSP 프로토콜로 영상을 송출하며, 카메라 수 만큼 RTSP to Redis 모듈이 수신을 처리합니다.
2. RTSP to Redis 모듈은 수신한 Fish-eye View 형태의 영상을 SVM Redis에 저장
3. SVM Backend는 SVM Redis에서 Fish-eye View 원본 이미지를 가져옵니다.
4. Backend는 해당 이미지를 Warping하여 Surround View 및 Side View 형태로 변환합니다.
5. 변환 시 필요한 이미지 보정(calibration) 정보 및 사용자 선택 Side View 목록은 Storage Module로부터 가져옴
6. 변환된 Surround/Side View 이미지는 WebSocket을 통해 Client로 전송되고 화면에 표시됨


<<<
===== 자선 & 타선 Data Flow

.Own & Target Data Flow
image::SYSTEM_OVERVIEW/architecture/dfd_own_and_target_ship.png[align="center",600]

1. VDR 등 Sensor Network로부터 NMEA 형식의 항해 데이터(GPS, AIS 등)를 수신함
2. UDP Proxy는 멀티캐스트 또는 유니캐스트 방식으로 데이터를 수신하고, 유니캐스트 방식으로 DTA 및 NMEA Parser/Filter에 전달함
3. DTA는
- 자선 데이터는 nmea:own
- AIS 타선 데이터는 nmea:vdm
- ARPA 타선 데이터는 nmea:ttm 형식으로 Core Redis에 저장함
4. Sensor Fusion 모듈은
- Core Redis로부터 nmea:own, nmea:vdm, nmea:ttm 데이터를 읽고
- 예측 시간 설정값(prediction vector length)은 Storage Module에서 조회함
5. Sensor Fusion은 다음과 같은 처리를 수행함:
- nmea:own 데이터 기반으로 예측 위치 포함된 svm:own 생성
- vdm/ttm 데이터를 융합해 svm:target 생성
- 일정 시간 이상 갱신되지 않은 객체는 svm:lost_target 으로 분류
6. 생성된 svm:own, svm:target, svm:lost_target 데이터를 SVM Redis에 저장함
7. SVM Backend는 Redis에 저장된 데이터를 WebSocket을 통해 Client로 전달함
8. Client는 전달받은 정보를 기반으로 자선/타선 위치 및 상태를 화면에 표시함


<<<
===== Inference Data Flow

.Inference Data Flow
image::SYSTEM_OVERVIEW/architecture/dfd_inference.png[align="center",600]

1. Storage API로부터 각 카메라에 대한 calibration 정보, 제조사 정보, RTSP URL 등을 수신함
2. Inference 모듈은 RTSP 프로토콜을 통해 각 카메라의 실시간 영상을 수신함
3. 수신된 이미지를 기반으로 인퍼런스를 수행하고, 결과를 Surround View 좌표계에 맞게 변환함
4. 결과는 svm:inference 키로 SVM Redis에 저장됨
5. SVM Backend는 Redis로부터 인퍼런스 결과를 가져와 WebSocket으로 Client에 전달함
6. Client는 전달받은 인퍼런스 결과를 화면에 시각화함



<<<
===== Video Recording Data Flow

.Video Recording Data Flow
image::SYSTEM_OVERVIEW/architecture/dfd_video_recording.png[align="center",600]

1. SVM Backend는 다음 정보를 SVM Redis에 주기적으로 저장함:
- 각 카메라 영상 (bow, port1, stern 등)
- Surround View 이미지
- 각 카메라 상태 정보
2. Video Recorder 모듈은 SVM Redis에서 이미지 스트림을 수신함
3. 수신된 이미지를 1시간 단위로 저장하여, 1시간 길이의 비디오 파일로 생성함
4. 생성된 영상은 다음 경로로 MinIO(HDD)에 저장됨:
- video/{날짜}/{카메라 이름}/{카메라 이름}_{시작시간}_{종료시간}.mp4
- 각 영상의 첫 프레임은 썸네일로 별도 저장됨
5. MinIO는 HDD에 마운트되어 모든 영상은 로컬 디스크에 저장됨
6. Video Recorder API 서버는 클라이언트 요청 시 MinIO로부터 정보를 조회하여 다음 기능을 제공:
- 리스트 조회/재생: presigned URL 제공
- 다운로드: API 서버를 통해 직접 다운로드

7. Client는 API로부터 전달받은 정보로 영상 리스트를 보여주고, 영상 재생 및 다운로드 UI를 제공함
8. 다운로드 요청 시, 영상 파일은 삽입된 USB 등 외부 저장 장치에 저장됨




<<<
===== Audit Data Flow

.Video Recording Data Flow
image::SYSTEM_OVERVIEW/architecture/dfd_audit.png[align="center",600]

1. SVM Backend, Auth Module, System은 각각 로그 파일을 생성하며, Fluentbit가 이를 실시간으로 감시함
2. 수집된 로그는 Fluentd를 통해 파싱 및 구조 변환됨
3. 변환된 로그는 MinIO에 저장됨
4. MinIO는 HDD에 연결되어 있으므로, Audit 로그는 HDD에 저장됨
5. Audit 조회 요청 시, MinIO에서 해당 로그를 조회함
6. Auth Console Page를 통해 조회된 로그가 사용자에게 시각화됨

<<<
===== CCTV & PTZ Data Flow

.CCTV & PTZ Data Flow
image::SYSTEM_OVERVIEW/architecture/dfd_cctv_ptz.png[align="center",600]

1. 사용자는 SVM Setting 페이지에서 CCTV/PTZ 카메라 정보를 등록함
2. SVM Backend는 등록된 정보를 Media Server 설정 파일에 반영함
3. 사용자가 SVM Main Page에서 CCTV View를 선택함
4. 각 카메라는 RTSP로 Media Server에 실시간 영상을 송출함
5. Media Server는 수신된 RTSP 스트림을 WebRTC로 변환하여 SVM Main Page로 전달함
6. 사용자는 SVM Main Page를 통해 PTZ 카메라 방향, 줌 등을 제어함
7. 제어 명령은 SVM Backend를 통해 실제 PTZ 카메라로 전달되어 제어됨

