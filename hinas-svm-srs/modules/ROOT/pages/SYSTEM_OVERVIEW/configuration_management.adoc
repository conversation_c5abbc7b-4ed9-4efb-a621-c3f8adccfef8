=== 형상 관리 (Configuration Management)

==== SW 산출물

본 프로젝트의 소프트웨어 산출물은 다음과 같으며, 각 단계별 산출 및 제출이 요구됩니다.

- 소프트웨어 요구사항 명세서 및 아키텍처 설계서 (현재 문서)
- 소스 코드
- 배포용 패키지 (Docker 이미지 포함)
- 단위/통합/시스템 테스트 리포트
- 사용자 매뉴얼 및 설치 가이드
- 릴리스 노트

===== 소스 코드 저장소 (Source Code Repository)

본 프로젝트의 소스 코드는 Git 및 GitHub 기반의 버전 관리 체계를 통해 저장 및 관리됩니다. 저장소는 기능별로 분리되어 있으며, 각 저장소는 별도의 브랜치 전략 및 릴리스 정책을 따릅니다.

[cols="1,2", options="header"]
|===
| 구성 요소 | 저장소 위치 (GitHub)

| SVM Backend
| `https://github.com/AVIKUS-SVM/svm-backend`

| SVM Frontend
| `https://github.com/avikus-ai/avikus-frontend`

| SVM Inference
| `https://github.com/avikus-ai/svm-inference-app`

| Avikus Sensor Fusion
| `https://github.com/avikus-ai/avikus-sensor-fusion`

| SVM Rtsp to Redis
| `https://github.com/AVIKUS-SVM/tug-rtsp-redis-sink`
|===

NOTE: 각 저장소는 CI/CD 파이프라인과 연동되어 있으며, 버전 태그는 semantic versioning(`vX.Y.Z`) 기준으로 관리됩니다.


===== 배포용 패키지

각 Github 저장소에서 Tag를 기준으로 배포용 패키지가 생성됩니다. 패키지는 Docker 이미지 형태로 제공되며 AWS ECR(Elastic Container Registry)에 저장됩니다. 배포용 패키지는 다음과 같은 구조로 관리됩니다:

[cols="1,2", options="header"]
|===
| 구성 요소 | 패키지 위치 (AWS ECR)
| SVM Backend
| `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/svm-backend`

| SVM Frontend
| `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/svm-frontend`

| SVM Inference
| `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/svm-inference-app`

| Avikus Sensor Fusion
| `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/avikus-sensor-fusion`

| SVM Rtsp to Redis
| `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/tug-rtsp-redis-sink`
|===


===== 테스트 리포트

.테스트 책임 및 분장
[cols="1,2,3", options="header"]
|===
| 구분 | 책임자 | 비고

| 모듈 단위 테스트
| 각 모듈 담당 개발자
| 기능별 단위 검증 수행

| 인터페이스 연동 테스트
| 개발팀
| API, 데이터 연동, 통신 포맷 검증

| 통합 테스트
| 품질팀
| 실환경 기반 통합 시나리오 테스트

| 회귀 테스트
| 품질팀
| 주요 이슈 수정 이후 반복 검증
|===

본 시스템의 기능 및 안정성 확보를 위해 다음과 같이 테스트를 수행하였으며, 각 모듈별 테스트는 해당 모듈의 담당 개발자 또는 검증 담당자가 수행합니다.

부록에 포함된 테스트 리포트 참고

- Test Report






===== 사용자 메뉴얼 및 설치 가이드
부록에 포함된 사용자 메뉴얼 및 설치 가이드 참고.

- User Manual
- Installation Guide

===== 릴리즈 노트
- 제품 릴리즈 노트의 경우, 각 GitHub 저장소의 릴리즈 노트에 작성됩니다.


<<<
==== 버전 관리


===== HiNAS 버전 관리 체계

HiNAS 소프트웨어는 https://semver.org/lang/ko/[Semantic Versioning 2.0] 체계를 따릅니다. 버전은 다음과 같은 구조로 구성됩니다.

`MAJOR.MINOR.PATCH` 형식

예: `v1.3.2`

* *MAJOR (주 버전)*: 호환되지 않는 변경이 있을 경우 증가합니다. (예: `v1.0.0` → `v2.0.0`)

* *MINOR (부 버전)*: 호환성을 유지하면서 새로운 기능이 추가될 경우 증가합니다.(예: `v1.0.0` → `v1.1.0`)

* *PATCH (패치 버전)*: 하위 호환성이 유지되며 버그 수정이나 보안 패치가 포함된 경우 증가합니다. (HotFix 포함) (예: `v1.0.0` → `v1.0.1`)

* *RC (RC 버전)*: 릴리즈 후보 버전으로, 최종 릴리즈 전 테스트용으로 사용됩니다. (예: `v1.0.0-rc.1`)


===== HiNAS 버전 체계

HiNAS Software는 개별 모듈 단위로 개발되며, 이들 모듈은 제품 버전(Product Version) 및 공통 통합 모듈(Common Integration Package)과 함께 설치되는 구조를 따릅니다.

전체 버전 구조는 다음과 같이 계층적으로 구성됩니다.

[cols="1,3", options="header"]
|===
| 구분 | 설명

| 개별 모듈 버전 (Module Version)
| 각 기능 단위 모듈별로 Semantic Versioning 2.0 체계를 기반으로 버전이 관리됩니다. 예: `svm-backend v1.1.0`

| 제품 버전 (Product Version)
| 여러 개별 모듈이 조합되어 하나의 제품으로 패키징되며, 이 조합에 대한 통합 버전이 별도로 정의됩니다.
예: `svm-backend v1.1.0`이 포함된 제품 패키지 → `Product v1.1.3`

| 통합 설치 패키지 (Common Integration Package)
| Product Version은 공통 모듈(예: 인증, 설치 스크립트, 모니터링, UI 런처 등)과 함께 통합되어 최종 설치 이미지로 구성됩니다. 이 최종 패키지는 제품 납품 및 설치 시 사용됩니다.
|===

<<<
==== 인증 및 준수사항 (Certification and Compliance)

https://www.krs.co.kr/kor/Content/CF_View.aspx?MRID=155&URID=153[*사이버보안 형식승인*]

KR의 사이버보안 형식승인 지침은 선박에 탑재될 제품(또는 시스템)에 대하여 사이버보안의 기본요소인 무결성, 가용성, 기밀성 관점에서 인증해 주는 서비스 이며, 국제표준 IEC 62443, 61162를 기반으로 설계되었습니다.

- 2024년 7월부터는 대양 운항 상선(터그선 제외)과 계약(단순 약정 포함)을 체결하기 위해서는 사이버 보안 형식승인(Cyber Security Type Approval)을 반드시 완료한 상태여야 합니다.

https://www.krs.co.kr/kor/Content/CF_View.aspx?MRID=134&URID=133[*제품 형식승인*]

형식 승인이란 재료 및 기기 등에 대하여 선박에 설치하기 전에 제품의 형식마다 미리 이 기준에서 규정한 자료 심사 및 승인 시험을 하고 만족할 경우 제품이 규정에 적합하다는 것을 제조자에 대해서 증명하는 것을 말합니다. ic60945 ure10




===== KR Type Approval Certificate

NOTE: 상기 인증은 사이버 회복탄력성을 갖춘 스마트 선박 솔루션으로서, 해당 인증 기준을 만족하도록 설계 및 구현되었음을 의미합니다.

[cols="1,3", options="header"]
|===
| 항목 | 내용

| 인증 명칭
| 형식승인서 (Type Approval Certificate)

| 승인 명칭
| Smart Ship Solution with Cyber Resilience

| 유효기간
| 2025년 2월 5일 ~ 2030년 2월 4일

| 제품 유형
| HiNAS SVM

| 인증 조건
| Appendix 1 참조
|===


